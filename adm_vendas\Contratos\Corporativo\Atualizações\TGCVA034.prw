#Include "FWBrowse.ch"
#Include "FWMvcDef.ch"
#Include "Protheus.ch"
#Include "RwMake.ch"
#Include "TGCVA034.ch"

/*/{Protheus.doc} TGCVA034
Telas de interação para paremetrizar o calculo do corporativo de um cliente,

<AUTHOR>
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.

@Return caracter, contem a mensagem de erro.
/*/

User Function TGCVA034(_cCliente,_cLoja)

	Local _cAnoRef := ""
	Local _cTipCalc := ""
	Local _cCalculo := ""
	Local _cMensCalc := ""
	Local _cMsgErro := ""	

	Local _nVlrDesc := 0

	Local _lContinua := .F.

	Default _cCliente := ""
	Default _cLoja := ""
	
	If Empty(_cCliente) .or. Empty(_cLoja)
		MsgStop(STR0001 + STR0002) // "[TGCVA034]: "###"Código/Loja do cliente não foi informado!"
	Else
		If _fTelaPara(@_cTipCalc,@_cCalculo,@_cAnoRef)
			If _cTipCalc $ "NR" .Or. _fTelaCnpj(_cCliente,_cLoja,_cAnoRef,_cTipCalc,@_nVlrDesc,@_cMensCalc)
				// Função para calcular para calculo			
				_lContinua := U_TGCVA033(_cCliente,_cLoja,_cAnoRef,_cTipCalc,_cCalculo,"",_nVlrDesc,_cMensCalc)
				
				If _lContinua
					Help(,,"Help",,STR0001 + STR0003,1,0)
				Else
					_cMsgErro := U_GV033A01()
					
					U_GV001X24(STR0001 + STR0005,_cMsgErro) // "[TGCVA034]: "###"Problema no Calculo"
				EndIf
			EndIf
		EndIf
	EndIf

Return _lContinua

/*/{Protheus.doc} _fTelaPara
Tela de parametrização.

<AUTHOR> Ribeiro
@Since 10/05/15

@Return logica, continua ou não.
/*/

Static Function _fTelaPara(_cTipCalc,_cCalculo,_cAnoRef)

	Local _oDlgTela
	Local _oPanel
	Local _oTipCalc
	Local _oCalculo
	Local _oAnoRef

	Local _cTitulo := "Calculo do Corporativo"
	
	Local _bChange := {|| _fChange(_cTipCalc,@_cCalculo,@_cAnoRef,@_lWCalc,@_lWAnoR) }
	Local _bValid := {|| Val(_cAnoRef) <= Year(dDataBase) }
	Local _bConfirma := {|| IIf(_fParaOk(_cTipCalc,_cCalculo,_cAnoRef,_lWCalc,_lWAnoR),(_lContinua:=.T.,_oDlgTela:Deactivate()),) }
	Local _bCancela := {|| _lContinua:=.F.,_oDlgTela:Deactivate() }
	
	Local _lWCalc := .F.
	Local _lWAnoR := .F.
	Local _lContinua := .F.
	
	Private _cTitTipo := ""
	Private _cTitCalc := ""
	Private _cTitAnoR := ""
	
	Private _aTipCalc := {}
	Private _aCalculo := {}
	
	Default _cTipCalc := ""
	Default _cCalculo := ""
	Default _cAnoRef := ""

	SX3->(DbSetOrder(2))
	
	_fChange(_cTipCalc,@_cCalculo,@_cAnoRef,@_lWCalc,@_lWAnoR)

	_oDlgTela := FWDialogModal():New()
	_oDlgTela:SetBackground(.F.)
	_oDlgTela:SetTitle(_cTitulo)
	_oDlgTela:SetEscClose(.T.)
	_oDlgTela:SetSize(96,200) 
	_oDlgTela:CreateDialog()
	_oDlgTela:EnableFormBar(.T.)
	_oDlgTela:CreateFormBar()
	
	_oPanel := _oDlgTela:GetPanelMain()

	SX3->(DbSeek("PH0_TIPCAL"))
	_cTitTipo := AllTrim(SX3->(FieldGet(FieldPos("X3_TITULO"))))
	_aTipCalc := _fComboBox(AllTrim(SX3->(FieldGet(FieldPos("X3_CBOX")))))

	@ 05, 05 SAY _cTitTipo OF _oPanel PIXEL
	@ 05, 60 COMBOBOX _oTipCalc VAR _cTipCalc ITEMS _aTipCalc SIZE 100,10 OF _oPanel PIXEL
	
	_oTipCalc:bChange := _bChange
	
	SX3->(DbSeek("PH0_CALC"))
	_cTitCalc := AllTrim(SX3->(FieldGet(FieldPos("X3_TITULO"))) )
	_aCalculo := _fComboBox(AllTrim(SX3->(FieldGet(FieldPos("X3_CBOX")))))

	@ 20, 05 SAY _cTitCalc OF _oPanel PIXEL 
	@ 20, 60 COMBOBOX _oCalculo VAR _cCalculo ITEMS _aCalculo SIZE 100,10 WHEN _lWCalc OF _oPanel PIXEL
	
	SX3->(DbSeek("PH0_ANOREF"))
	_cTitAnoR := AllTrim(SX3->(FieldGet(FieldPos("X3_TITULO"))))

	@ 35, 05 SAY _cTitAnoR OF _oPanel PIXEL 
	@ 35, 60 MSGET _oAnoRef VAR _cAnoRef SIZE 40,10 VALID _bValid WHEN _lWAnoR OF _oPanel PIXEL
	
    _oDlgTela:AddButton("Confirmar",_bConfirma,"Confirmar",,.T.,.F.,.T.,)	// "Confirmar"
    _oDlgTela:AddButton("Cancelar",_bCancela,"Cancelar",,.T.,.F.,.T.,)	// "Cancelar"
	
	_oDlgTela:Activate()
	
Return _lContinua

/*/{Protheus.doc} _fComboBox
Função para retornar um array com o combobox.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCbox, caracter, contem o X3_CBOX.

@Return array, retornar o CBOX em um array.
/*/

Static Function _fComboBox(_cCbox)

	Local _aAux1 := {}
	Local _aAux2 := {}
	
	Local _nCount := 0
	
	Default _cCbox := ""
	
	_aAux1 := Separa(_cCbox,";")
	Aadd(_aAux2,"")
	
	For _nCount := 1 To Len(_aAux1)
		If !Empty(_aAux1[_nCount])
			Aadd(_aAux2,AllTrim(_aAux1[_nCount]))
		EndIf
	Next

Return _aAux2

/*/{Protheus.doc} _fChange
Função para alteração de campo.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _lWCalc, caracter, se o campo calculo pode ser alterado.
/*/

Static Function _fChange(_cTipCalc,_cCalculo,_cAnoRef,_lWCalc,_lWAnoR)

	Default _cTipCalc := ""
	Default _cCalculo := ""
	Default _cAnoRef := ""

	Default _lWCalc := .F.
	Default _lWAnoR := .F.

	If _cTipCalc $ "NR"
		_lWCalc := .T.
		_lWAnoR := .T.
	Else
		_lWCalc := .F.
		_lWAnoR := .F.
		_cCalculo := Space(1)
		_cAnoRef := GetMv("TDI_ANOCOR")
	EndIf

Return

/*/{Protheus.doc} _fParaOk
Função para confirmação dos dados.

<AUTHOR> Ribeiro
@Since 10/05/15

@Return logico, se continua ou não.
/*/

Static Function _fParaOk(_cTipCalc,_cCalculo,_cAnoRef,_lWCalc,_lWAnoR)

	Local _lContinua := .F.
	
	Local _cCampo := ""
	Local _cAuxiliar := ""
	
	Local _nPosicao := 0
	
	Default _cTipCalc := ""
	Default _cCalculo := ""
	Default _cAnoRef := ""

	Default _lWCalc := .F.
	Default _lWAnoR := .F.
	
	If Empty(_cTipCalc)
		Help(,,"Help",,"O campo '" + _cTitTipo + "' é obrigatório!",1,0)
	Else
		_nPosicao := Ascan(_aTipCalc, {|x| SubStr(x,1,1) == _cTipCalc })
		
		_cAuxiliar := Separa(_aTipCalc[_nPosicao],"=")[2]
	
		If Empty(_cCalculo) .And. _cTipCalc $ "NR"
			_cCampo := _cTitCalc
		ElseIf Empty(_cAnoRef)
			_cCampo := _cTitAnoR
		Else
			_lContinua := .T.
		EndIf
		
		If !_lContinua
			Help(,,"Help",,"Para calculo '" + _cAuxiliar + "', o campo '" + _cCampo + "' é obrigatório",1,0)
		EndIf
	EndIf
	
Return _lContinua

/*/{Protheus.doc} _fTelaCnpj
Tela para seleção de CNPJ.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cAnoRef, caracter, ano de referencia.
@Param _cTipCalc, caracter, tipo do calculo.
@Param _nVlrDesc, numerico, valor de desconto para o calculo.
@Param _cMensCalc, caracter, mensagem de calculo.

@Return logica, continua ou não.
/*/

Static Function _fTelaCnpj(_cCliente,_cLoja,_cAnoRef,_cTipCalc,_nVlrDesc,_cMensCalc)

	Local _aCampos := {}
	
	Local _lContinua := .F.

	Local _cTitulo := IIf(_cTipCalc=="I",STR0018,STR0019)	// "Inclusão de CNPJ"###"Exclusão de CNPJ"
	Local _cContrato := ""
	Local _cRevisao := ""
	
	Local _oDialog

	Local _bConfirma := {|| _lContinua:=.T.,_oDialog:Deactivate() }
	Local _bCancela := {|| _lContinua:=.F.,_oDialog:Deactivate() }
	Local _bMarkColu := {|| _aCnpjs[_oMarkCnpj:nAt][1] := !_aCnpjs[_oMarkCnpj:nAt][1] }
	
	Private _aCnpjs := {}

	Private _oBrowse
	Private _oPainel
	Private _oTmpTable

	Private _cAlias := ""

	Default _cTipCalc := ""
	Default _nVlrDesc := ""
	Default _cMensCalc := ""
	
	_fGetContr(_cCliente,_cLoja,@_cContrato,@_cRevisao)

	If _cTipCalc == "I"		// Inclusão de CNPJ
		_aCnpjs := _fIncCNPJ(_cContrato,_cRevisao,_cCliente,_cLoja,_cAnoRef)
	ElseIf _cTipCalc == "E"	// Exclusão de CNPJ
		_aCnpjs := _fExcCNPJ(_cContrato,_cRevisao,_cCliente,_cLoja,_cAnoRef)
	EndIf
	
	If Empty(_aCnpjs)
		MsgStop(STR0001 + STR0020 + IIf(_cTipCalc=="I",STR0021,STR0022) + "!")	// "[TGCVA034]: "###"Não existe CNPJ's "###"inclusos"###"excluídos"
	Else
		_oDialog := FWDialogModal():New()
		_oDialog:SetBackground(.F.)  
		_oDialog:SetTitle(_cTitulo)
		_oDialog:SetEscClose(.T.)
		_oDialog:EnableAllClient() 
		_oDialog:EnableFormBar(.F.)
		_oDialog:CreateDialog()
		
		_oPainel := _oDialog:GetPanelMain()
		
		_fTabTemp()

		_aTamanho := TamSx3("ZAL_CGCAGR") 
		Aadd(_aCampos,{"CNPJ","CORP_CGC",_aTamanho[3],_aTamanho[1],_aTamanho[2],PesqPict("ZAL","ZAL_CGCAGR")})
		
		_aTamanho := TamSx3("ZAL_NOMAGR")
		Aadd(_aCampos,{"Nome","CORP_NOME",_aTamanho[3],_aTamanho[1],_aTamanho[2],PesqPict("ZAL","ZAL_NOMAGR")})
		
		_aTamanho := TamSx3("ZAL_MTRINI")
		Aadd(_aCampos,{"Valor","CORP_VALOR",_aTamanho[3],_aTamanho[1],_aTamanho[2],PesqPict("ZAL","ZAL_MTRINI")})
	
		_oBrowse := FWMarkBrowse():New()
		_oBrowse:SetAlias(_cAlias)
		_oBrowse:SetFields(_aCampos)
		_oBrowse:SetFieldMark("CORP_OK")
		_oBrowse:SetTemporary()
		_oBrowse:SetDataQuery(.F.)
		_oBrowse:SetDataTable(.T.)
		_oBrowse:SetOwner(_oPainel)
		_oBrowse:DisableDetails(.T.)
		_oBrowse:DisableConfig()
		_oBrowse:DisableReport()
		_oBrowse:SetMenuDef("")
		_oBrowse:AddButton(STR0016,_bConfirma,,,,.F.,1)	// "Confirmar"
		_oBrowse:AddButton(STR0017,_bCancela,,,,.F.,1)	// "Cancelar"

		_oBrowse:Activate()
		_oDialog:Activate()
		
		If _lContinua
			(_cAlias)->(DbGoTop())
			While (_cAlias)->(!Eof())
				If !Empty((_cAlias)->CORP_OK)
					_nVlrDesc += (_cAlias)->CORP_VALOR
					_cMensCalc += (_cAlias)->CORP_CGC + " | "
				EndIf
				(_cAlias)->(DbSkip())
			EndDo
		EndIf

		_oTmpTable:Delete()
	EndIf
	
Return _lContinua

/*/{Protheus.doc} _fGetContr
Função para buscar a revisão do contrato gerada pela proposta.

<AUTHOR> Ribeiro
@since 05/11/2015

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
/*/

Static Function _fGetContr(_cCliente,_cLoja,_cContrato,_cRevisao)

	Local _cQuery := ""
	
	Local _aDados := {}
	
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cContrato := ""
	Default _cRevisao := ""
	
	_cQuery := " SELECT " + CRLF
	_cQuery += "	 CN9.CN9_NUMERO " + CRLF
	_cQuery += "	,CN9.CN9_REVISA " + CRLF
	_cQuery += " FROM " + RetSqlName("CN9") + " CN9 " + CRLF
	_cQuery += CRLF
	_cQuery += " INNER JOIN " + RetSqlName("CNC") + " CNC ON " + CRLF
	_cQuery += "	CNC.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND CNC.CNC_FILIAL = '" + xFilial("CNC") + "' " + CRLF
	_cQuery += "	AND CNC.CNC_NUMERO = CN9.CN9_NUMERO " + CRLF
	_cQuery += "	AND CNC.CNC_REVISA = CN9.CN9_REVISA " + CRLF
	_cQuery += "	AND CNC.CNC_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND CNC.CNC_LOJACL = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND CNC.CNC_TIPCLI = '01' " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	CN9.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND CN9.CN9_FILIAL = '" + xFilial("CNB") + "' " + CRLF
	_cQuery += "	AND CN9.CN9_SITUAC = '05' " + CRLF
	
	_aDados := U_GV001X23(_cQuery)
	
	If !Empty(_aDados)
		_cContrato := PadR(_aDados[1][1],TamSx3("CN9_NUMERO")[1])
		_cRevisao := PadR(_aDados[1][2],TamSx3("CN9_REVISA")[1])
	EndIf

Return

/*/{Protheus.doc} _fIncCNPJ
Formata mensagem de erro no calculo do corporativo

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cAnoRef, caracter, ano de referencia do calculo.

@Return array, contem o cnpj, o nome, e o valor.
/*/

Static Function _fIncCNPJ(_cContrato,_cRevisao,_cCliente,_cLoja,_cAnoRef)

	Local _cQuery := ""
	
	Local _aSetField := {}
	
	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cAnoRef := ""

	_cQuery := " SELECT " + CRLF
	_cQuery += "	 'F' MARK " + CRLF
	_cQuery += "	,ZAL.ZAL_CGCAGR " + CRLF
	_cQuery += "	,ZAL.ZAL_NOMAGR " + CRLF
	_cQuery += "	,ZAL.ZAL_MTRINI " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL " + CRLF
	_cQuery += " WHERE ZAL.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_ANOREF = '" + AllTrim(Str(Year(dDataBase))) + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_STATUS = 'A' " + CRLF
	_cQuery += "	AND NOT EXISTS ( " + CRLF
	_cQuery += "	 SELECT 1 " + CRLF
	_cQuery += "	 FROM " + RetSqlName("ZAL") + " CORP " + CRLF
	_cQuery += "	 WHERE " + CRLF
	_cQuery += "		CORP.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "		AND CORP.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_ANOREF = '" + _cAnoRef + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_STATUS = 'A' " + CRLF
	_cQuery += "		AND CORP.ZAL_CGCAGR = ZAL.ZAL_CGCAGR) " + CRLF
	
	// Preenche o array que será utilizado na função GV001X23 para a chamada do TcSetField.
	Aadd(_aSetField,{"MARK","L",1,0})

Return U_GV001X23(_cQuery,,,.T.,_aSetField)

/*/{Protheus.doc} _fExcCNPJ
Formata mensagem de erro no calculo do corporativo

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cAnoRef, caracter, ano de referencia do calculo.

@Return array, contem o cnpj, o nome, e o valor.
/*/

Static Function _fExcCNPJ(_cContrato,_cRevisao,_cCliente,_cLoja,_cAnoRef)

	Local _cQuery := ""
	
	Local _aSetField := {}

	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cAnoRef := ""

	_cQuery := " SELECT " + CRLF
	_cQuery += "	 'F' MARK " + CRLF
	_cQuery += "	,ZAL.ZAL_CGCAGR " + CRLF
	_cQuery += "	,ZAL.ZAL_NOMAGR " + CRLF
	_cQuery += "	,CASE " + CRLF
	_cQuery += "		WHEN ZAL.ZAL_MTDIFR <> 0 THEN ZAL.ZAL_MTDIFR " + CRLF
	_cQuery += "		WHEN ZAL.ZAL_MTRDIP <> 0 THEN ZAL.ZAL_MTRDIP " + CRLF
	_cQuery += "		WHEN ZAL.ZAL_MTINFR <> 0 THEN ZAL.ZAL_MTINFR " + CRLF
	_cQuery += "		WHEN ZAL.ZAL_MTRINF <> 0 THEN ZAL.ZAL_MTRINF " + CRLF
	_cQuery += "	 END AS VALOR " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL " + CRLF
	_cQuery += " WHERE ZAL.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_ANOREF = '" + _cAnoRef + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_STATUS = 'A' " + CRLF
	_cQuery += "	AND EXISTS ( " + CRLF
	_cQuery += "	 SELECT 1 " + CRLF
	_cQuery += "	 FROM " + RetSqlName("ZAL") + " CORP " + CRLF
	_cQuery += "	 WHERE " + CRLF
	_cQuery += "		CORP.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "		AND CORP.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_ANOREF = '" + AllTrim(Str(Year(dDataBase))) + "' " + CRLF
	_cQuery += "		AND CORP.ZAL_STATUS = 'I' " + CRLF
	_cQuery += "		AND CORP.ZAL_CGCAGR = ZAL.ZAL_CGCAGR) " + CRLF
	
	// Preenche o array que será utilizado na função GV001X23 para a chamada do TcSetField.
	Aadd(_aSetField,{"MARK","L",1,0})

Return U_GV001X23(_cQuery,,,.T.,_aSetField)

/*/{Protheus.doc} _fTabTemp
Função para criar tabela temporária

<AUTHOR> Ribeiro
@Since 10/05/15
/*/

Static Function _fTabTemp()

	Local _aCampos := {}
	Local _aTamanho := {}
	
	Local _nCount := 0
	
	Local _aCnpjs := {}
	
	_cAlias := GetNextAlias()
	
	_aTamanho := TamSx3("ZAL_CGCAGR") 
	Aadd(_aCampos,{"CORP_CGC",_aTamanho[3],_aTamanho[1],_aTamanho[2]})

	_aTamanho := TamSx3("ZAL_NOMAGR") 
	Aadd(_aCampos,{"CORP_NOME",_aTamanho[3],_aTamanho[1],_aTamanho[2]})

	_aTamanho := TamSx3("ZAL_MTRINI") 
	Aadd(_aCampos,{"CORP_VALOR",_aTamanho[3],_aTamanho[1],_aTamanho[2]})
	Aadd(_aCampos,{"CORP_OK","C",1,0}) 
	
	_oTmpTable := FWTemporaryTable():New(_cAlias)
	_oTmpTable:SetFields(_aCampos)
	_oTmpTable:AddIndex("1",{"CORP_CGC"})
	_oTmpTable:AddIndex("2",{"CORP_NOME"})
	_oTmpTable:Create()
	
	For _nCount := 1 To Len(_aCnpjs)
		RecLock(_cAlias,.T.) 
			(_cAlias)->CORP_CGC := _aCnpjs[_nCount][2]
			(_cAlias)->CORP_NOME := _aCnpjs[_nCount][3]
			(_cAlias)->CORP_VALOR := _aCnpjs[_nCount][4]
			(_cAlias)->CORP_OK := "" 
		(_cAlias)->(MsUnLock())
	Next
	
	(_cAlias)->(DbGoTop())
	
Return
