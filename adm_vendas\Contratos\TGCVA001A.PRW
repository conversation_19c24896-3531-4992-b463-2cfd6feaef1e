#INCLUDE "TGCVA001A.ch"
#INCLUDE "TGVX001DEF.CH"
#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "GCTXDEF.ch"

//-------------------------------------------------------------------
/*/{Protheus.doc} MenuDef
Menu do Browse da CN9, tela de Cockpit
<AUTHOR>
@since 30/10/2015
@version 1.0
/*/
//-------------------------------------------------------------------
STATIC Function MenuDef()
	Local aRotCon		:= {}
	Local aRotRec		:= {}
	Local aRotRel		:= {}
	Local aRotPeds		:= {}
	Local aRotMeds		:= {}
	Local aRotBema		:= {}

	//Local aRotina		:= {}
	Local aRotProp		:= {}
	Local aRotBill		:= {}
	Local aRotIntera	:= {}
	Local aRotMkpt      := {}

	PRIVATE aRotina		:= {}

	If IsInCallStack( 'U_GctXRun' )
		ADD OPTION aRotCon TITLE STR0006 ACTION "U_GV01CLFBT(2,11)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_HIST_CONT 	//"Hist. Contrato"
		ADD OPTION aRotRel TITLE STR0020 ACTION "U_GV01CLFBT(2,14)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 					//"Relatório Bonif/Caren"
		ADD OPTION aRotRel TITLE STR0012 ACTION "U_GV01CLFBT(2,15)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_REL_VERS		//"Relatorio de Versionamento"
        ADD OPTION aRotRel TITLE STR0016 ACTION "U_GV01CLFBT(2,16)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_REL_PROP 	//"Relatório Represados"
		ADD OPTION aRotRel TITLE STR0030 ACTION "U_GV01CLFBT(2,22)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 					//"Relatório Revisões Pendentes"
		ADD OPTION aRotCon TITLE STR0002 ACTION "U_GV01CLFBT(2,10)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CONTR 	//"Vis. Contrato"
        ADD OPTION aRotCon TITLE "Vis. Cronograma" 			ACTION "U_GCVC014C()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CONTR 	//"Vis. Cronograma"
		ADD OPTION aRotRec TITLE "Alt. Motivos Can/Bon/Car"	ACTION "U_TGCVA111()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CONTR 	//"Alt. Motivos Can/Bon/Car"
    	ADD OPTION aRotRec TITLE "Relatório Conf. Canc/Reativ."	ACTION "U_TGCVRT31()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_REL_PROP 	
    	ADD OPTION aRotRec TITLE "Relatório Conf. Bonif/Carência"	ACTION "U_TGCVRT32()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_REL_PROP 	
    Else
        ADD OPTION aRotCon TITLE STR0003 ACTION "U_CFGX001A('U_GV01CLFBT', 2,8)"  OPERATION OP_COPIA ACCESS 0 ID DEF_ALT_CONTR 				//"Alt. Contrato"
		ADD OPTION aRotCon TITLE STR0010 ACTION "U_GV01CLFBT(2,6)"  OPERATION MODEL_OPERATION_UPDATE ACCESS 0					//"Aprov. Revisão"
        ADD OPTION aRotCon TITLE STR0031 ACTION "U_GV01CLFBT(2,24)"  OPERATION OP_COPIA ACCESS 0 ID DEF_ALT_CONTR 				//"Alt. Contrato Corporativo"
        ADD OPTION aRotCon TITLE STR0005 ACTION "U_GV01CLFBT(2,2)"  OPERATION MODEL_OPERATION_DELETE	ACCESS 3 				//"Excluir"
		ADD OPTION aRotCon TITLE STR0006 ACTION "U_CFGX001A('U_GV01CLFBT',2,11)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_HIST_CONT 	//"Hist. Contrato"
		ADD OPTION aRotCon TITLE STR0001 ACTION "U_GV01CLFBT(2,9)"  OPERATION MODEL_OPERATION_INSERT ACCESS 0  					//"Incluir"
		ADD OPTION aRotCon TITLE STR0013 ACTION "U_GV01CLFBT(2,12)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 					//"Jobs do Contrato"
		ADD OPTION aRotCon TITLE STR0022 ACTION "U_GV01CLFBT(2,13)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 					//"Proces. Lotes"
		ADD OPTION aRotCon TITLE STR0008 ACTION "U_GV01CLFBT(2,4)"  OPERATION MODEL_OPERATION_UPDATE ACCESS 0 ID DEF_REATIV_IT	//"Reativar"
		ADD OPTION aRotCon TITLE STR0009 ACTION "U_GV01CLFBT(2,5)"  OPERATION MODEL_OPERATION_UPDATE ACCESS 0 ID DEF_ROLLB_CNT	//"Rollback"
		ADD OPTION aRotCon TITLE STR0004 ACTION "U_GV01CLFBT(2,1)"  OPERATION MODEL_OPERATION_UPDATE	ACCESS 0 				//"Situacao"
		ADD OPTION aRotCon TITLE STR0036 ACTION "U_GV01CLFBT(2,3)"  OPERATION MODEL_OPERATION_UPDATE ACCESS 0 ID DEF_TRANSF_LI	//"Transferência"
		ADD OPTION aRotCon TITLE STR0037 ACTION "U_TGCVA078()"  OPERATION MODEL_OPERATION_UPDATE ACCESS 0            	//"Transf. Lote"
		ADD OPTION aRotCon TITLE STR0038 ACTION "U_CFGX001A('U_TGCVA130')"  OPERATION MODEL_OPERATION_UPDATE ACCESS 0 ID "AJUSTFIN" //"Ajuste Financeiro"
		ADD OPTION aRotCon TITLE "Trocar Rateios de Contrato" 			ACTION "U_GV01CLFBT(2,43)" OPERATION MODEL_OPERATION_INSERT ACCESS 0
		ADD OPTION aRotCon TITLE STR0002 ACTION "U_GV01CLFBT(2,10)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CONTR 	//"Vis. Contrato"
        ADD OPTION aRotCon TITLE "Vis. Cronograma" ACTION "U_GCVC014C()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CONTR 	//"Vis. Cronograma"        
        ADD OPTION aRotCon TITLE "Vis. Vinculo Licenciamento" ACTION "U_TGCVC015()" OPERATION MODEL_OPERATION_INSERT ACCESS 0
        ADD OPTION aRotCon TITLE "Recalcula Cronograma " ACTION "U_GCVA095R()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CONTR 	//"Vis. Cronograma"
	    ADD OPTION aRotCon TITLE "Ajuste de Represados" ACTION "U_GCVA105X()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CONTR 	//Ajuste Represados 
		ADD OPTION aRotCon TITLE "Ajusta Ano Ref. (Corporativo)" ACTION "U_TGCVA122()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CONTR 	//Ajusta Ano Ref. (Corporativo)
		ADD OPTION aRotCon TITLE "Rateio"	       				 ACTION "U_GV01CLFBT(2,49)"        OPERATION OP_COPIA ACCESS 0 ID DEF_ALT_CONTR 				
		
		If GetMV("TI_INT201",, "N" ) != "N"  //S-Sim;N-Não;P-Pergunta
			ADD OPTION aRotCon TITLE "Solicitacao/simulacao de Cancelamento"	        ACTION "U_CFGX001A('U_TGCVA264')" OPERATION MODEL_OPERATION_INSERT ACCESS 0
			ADD OPTION aRotCon TITLE 'Aprov. Solicitacao Cancelamento'	ACTION "U_CFGX001A('U_POGCT001')" OPERATION MODEL_OPERATION_UPDATE		ACCESS 0
			ADD OPTION aRotCon TITLE 'Aprov. Cancelamento Lote'	ACTION "U_CFGX001A('U_GCVA234L')" OPERATION MODEL_OPERATION_INSERT		ACCESS 0
		EndIf 

		ADD OPTION aRotRec TITLE "Alt. Motivos Can/Bon/Car"	      ACTION "U_TGCVA111()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CONTR 	//"Alt. Motivos Can/Bon/Car"        
    	ADD OPTION aRotRec TITLE "Relatório Conf. Canc/Reativ."	  ACTION "U_TGCVRT31()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_REL_PROP 	
    	ADD OPTION aRotRec TITLE "Relatório Conf. Bonif/Carência" ACTION "U_TGCVRT32()" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_REL_PROP 	
  
        ADD OPTION aRotPeds TITLE "Gerar Pedido"   ACTION "U_CFGX001A('U_GCVA143')" OPERATION MODEL_OPERATION_VIEW ACCESS 0 					
        ADD OPTION aRotPeds TITLE "Excluir Pedido" ACTION "U_CFGX001A('U_GCVA145')" OPERATION MODEL_OPERATION_VIEW ACCESS 0 						
		
		ADD OPTION aRotMeds TITLE STR0021 ACTION "U_GV01CLFBT(2,20)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 					//"Gerar Medição Penden."
		ADD OPTION aRotMeds TITLE STR0023 ACTION "U_CFGX001A('U_GV01CLFBT',2,19)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 					//"Medições do Contrato"
		ADD OPTION aRotMeds TITLE STR0024 ACTION "U_CFGX001A('U_GV01CLFBT',2,21)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 					//"Medições Vendas"
		
        ADD OPTION aRotProp TITLE STR0033 ACTION "U_CFGX001A('U_TGCVA053',1)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 		//"Propostas Reprovadas"


		// Seattle -  Bematech HW e SW
		ADD OPTION aRotBema TITLE "Ativa/Bloqueia Lic Misterchef"		ACTION "U_GV01CLFBT(2,37)" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
		ADD OPTION aRotBema TITLE "Alterar Royalties Cliente" 			ACTION "U_GV01CLFBT(2,33)" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
		ADD OPTION aRotBema TITLE "Apuração Royalties Misterchef" 		ACTION "U_CFGX001A('U_GV01CLFBT',2,32)" OPERATION MODEL_OPERATION_INSERT ACCESS 0
		ADD OPTION aRotBema TITLE "Consulta Multas Bemacash" 			ACTION "U_GV01CLFBT(2,30)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 	//"Multas Bemacash"
		ADD OPTION aRotBema TITLE "Efetivar Lic Bematech no Contrato"	ACTION "U_CFGX001A('U_GV01CLFBT',2,41)" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
		ADD OPTION aRotBema TITLE "Integração Licenciador Externo"		ACTION "U_GV01CLFBT(2,40)" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
		ADD OPTION aRotBema TITLE "Licença MIX - Misterchef" 			ACTION "U_GV01CLFBT(2,35)" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
		ADD OPTION aRotBema TITLE "Monitor Integrações Propostas Adquir"ACTION "U_CFGX001A('U_GV01CLFBT',2,42)" OPERATION MODEL_OPERATION_INSERT ACCESS 0
		ADD OPTION aRotBema TITLE "Recalculo de Multas Bemacash" 		ACTION "U_GV01CLFBT(2,31)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 	//"Recalculo de Multas Bemacash"
		ADD OPTION aRotBema TITLE "Relatorio Canais - Portal" 			ACTION "U_GV01CLFBT(2,38)" OPERATION MODEL_OPERATION_INSERT ACCESS 0
		ADD OPTION aRotBema TITLE "Trocar PCs - Misterchef" 			ACTION "U_GV01CLFBT(2,34)" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
		ADD OPTION aRotBema TITLE "Vis. Licenças Misterchef" 			ACTION "U_GV01CLFBT(2,36)" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
		
		ADD OPTION aRotBill TITLE 'Pré-Billing'							ACTION "U_CFGX001A('U_TGCVA098')" OPERATION MODEL_OPERATION_INSERT ACCESS 0
		ADD OPTION aRotBill TITLE 'Manutenção Billing'					ACTION "U_CFGX001A('U_GCVA084X')" OPERATION MODEL_OPERATION_INSERT ACCESS 0
		ADD OPTION aRotBill TITLE 'Relatório Bilhetagem'				ACTION "U_TGCVR026()" OPERATION MODEL_OPERATION_INSERT ACCESS 0

		// Intera Ilimitado
		ADD OPTION aRotIntera TITLE "Visualizar tickets Intera"			ACTION "U_TGCIIA04(CN9_NUMERO)"   OPERATION MODEL_OPERATION_UPDATE ACCESS 0
		ADD OPTION aRotIntera TITLE "Fotografia"	       				ACTION "U_GV01CLFBT(2,47)"        OPERATION OP_COPIA ACCESS 0 ID DEF_ALT_CONTR 				//"Contr. Intera Iliminado"
		
		ADD OPTION aRotIntera TITLE "Downgrade Intera"	             	ACTION "U_CFGX001A('U_TGCIIA07')" OPERATION MODEL_OPERATION_UPDATE ACCESS 0

		// Market Place
		ADD OPTION aRotMkpt TITLE "Faturamento dia - Início"			ACTION "U_FATCTRDIA(1) " OPERATION MODEL_OPERATION_UPDATE ACCESS 0
		ADD OPTION aRotMkpt TITLE "Faturamento dia - Termino"         	ACTION "U_FATCTRDIA(2) " OPERATION MODEL_OPERATION_UPDATE ACCESS 0


	EndIf
    ADD OPTION aRotina TITLE STR0027 			ACTION aRotCon 		OPERATION MODEL_OPERATION_VIEW ACCESS 0 //"Manutenção"	
	If Len(aRotMeds)  > 0
		ADD OPTION aRotina TITLE STR0025 ACTION aRotMeds OPERATION MODEL_OPERATION_VIEW ACCESS 0 							//"Medições"
	EndIf
	If Len(aRotPeds)  > 0
		ADD OPTION aRotina TITLE "Pedido" ACTION aRotPeds OPERATION MODEL_OPERATION_VIEW ACCESS 0 							
	EndIf

	ADD OPTION aRotina TITLE "Bematech" 		ACTION aRotBema 	OPERATION MODEL_OPERATION_VIEW ACCESS 0	//"Bematech"
	ADD OPTION aRotina TITLE "Bilhetagem" 		ACTION aRotBill 	OPERATION MODEL_OPERATION_VIEW ACCESS 0	//"Bilhetagem"
	ADD OPTION aRotina TITLE "Intera" 			ACTION aRotIntera 	OPERATION MODEL_OPERATION_VIEW ACCESS 0	//"Intera"
	ADD OPTION aRotina TITLE STR0034 			ACTION aRotProp 	OPERATION MODEL_OPERATION_VIEW ACCESS 0	//"Proposta"
	ADD OPTION aRotina TITLE "Rec. Recorrente" 	ACTION aRotRec		OPERATION MODEL_OPERATION_VIEW ACCESS 0	//"Receita Recorrente"
	If GetMv('TI_EMPMKT',,"90") == cEmpAnt
		ADD OPTION aRotina TITLE "Market Place" 	ACTION aRotMkpt		OPERATION MODEL_OPERATION_VIEW ACCESS 0	//"Market Place"
	Endif		


Return aRotina


//-------------------------------------------------------------------
/*/{Protheus.doc} GV01ASEL
Selecionador de Funcionalidade - Valida se existe item da empresa/filial logado no contrato. Caso não exista, abre apenas como visualização

<AUTHOR> Ferreira
@since 08/04/2016
@return Nil
/*/
//-------------------------------------------------------------------
User Function GV01ASEL(nTipo,nRecCN9)

Default nTipo := 0

CN9->(DbGoTo(nRecCN9))

 
If U_GV01VLCP(CN9->CN9_NUMERO, CN9->CN9_REVISA, .T. , nTipo)	

	If nTipo >= 9  // Rotinas que não precisam passar pela validação de Unidade de Negocio Logada
		
		Do Case
			Case nTipo == 9
				U_TGC001CI()  	//"Incluir"
			Case nTipo == 10
				U_TGC001CV(nRecCN9) 	//"Vis. Contrato"
			Case nTipo == 11
				U_TGCVA006(nRecCN9) 	//"Hist. Contrato"
			Case nTipo == 12
				U_GV001JOB(nRecCN9) 	//"Jobs do Contrato"
			Case nTipo == 13
				U_TGCVJ000(nRecCN9) 	//"Proces. Lotes"
			Case nTipo == 14
				U_TGCVR013(nRecCN9) 	//"Relatório Bonif/Caren"
			Case nTipo == 15
				U_TGCVR002(nRecCN9) 	//"Relatorio de Versionamento"
			Case nTipo == 17
				U_TGCVR014(nRecCN9) 	//"Relatório Cronograma Financeiro"
			Case nTipo == 18
				U_TGCVR015(nRecCN9) 	//"Relatório Analítico Contratos"
			Case nTipo == 19
				U_TGCVA056()	//"Medições do Contrato"			
			Case nTipo == 20
				U_TGCVA055(Nil, Nil, Nil, Nil, Nil, Nil, nRecCN9)	//"Gerar Medição Penden."
			Case nTipo == 21
				U_GV56Md27(nRecCN9)	//"Medições Vendas"
			Case nTipo == 22
				U_TGCVR016()	//Relatório Revisões Pendentes
			Case nTipo == 23
				U_TGCORPCA(nRecCN9,.F.)	//Visualizar Contrato Corporativo
			Case nTipo == 24
				U_TGCORPCA(nRecCN9,.T.)	//Alterar contrato Corporativo	
			Case nTipo == 30			
				U_TGCVA070(nRecCN9)		//Seattle - Consulta de Multas Bemacash.
			Case nTipo == 31 
				U_TGCVA071(nRecCN9)		//Seattle - Recalculo de Multas Bemacash.
			Case nTipo == 32 
				U_TGCVC010()			//Seattle - Consulta Apuração Royalties Misterchef
			Case nTipo == 33 
				U_TGCVA075(nRecCN9)		//Seattle - Vlr Royalties Misterchef
			Case nTipo == 34 
				U_TGCV73TRC(nRecCN9)	//Seattle - Troca de Computador Misterchef
			Case nTipo == 35 
				U_TGCV73MIX(nRecCN9)	//Seattle - Manutenção licença MIX Misterchef
			Case nTipo == 36 
				U_G73VLicMst(nRecCN9)	//Seattle - Visualização licenças
			Case nTipo == 37 
				U_G73AtDsM(nRecCN9)		//Seattle - Ativação/Desativação das licenças Misterchef - TOTAL
			Case nTipo == 38
				U_TGCVR021()			//Seattle - Relatorio Canais - Portal Parceiros
			Case nTipo == 39
				U_TGCVR022()			//Seattle - Relatorio Apuração Royalties Misterchef
			Case nTipo == 40
				U_TITC8Brw(nRecCN9)		//Seattle - Integração de licenças Externas
			Case nTipo == 41
				U_TITC4Brw(nRecCN9)		//Seattle - Ativação das Licenças Bematech no Protheus
			Case nTipo == 42
				u_TITC2Brw()			//Seattle - Monitor Integrações Propostas Adquiridas
			Case nTipo == 43
				u_TGCVA077()			//Troca de Rateio de contratos (destinatario de faturamento em Lote)
			Case nTipo == 45
				U_TGCVR029(nRecCN9) 	//"Relatório Troca Modalidade"
			Case nTipo == 46
				U_TGCVR033() 	//"Relatório de Cancelamentos"
			Case nTipo == 47
				U_GCIIA11A() //Tela do Novo Intera		//TIADMVIN-3138
			Case nTipo == 48
				U_TGCVR034() 	//"Relatório de Oscilação Intera"
			Case nTipo == 49
				U_GCVA148A() //Tela do Novo Rateio
			Case nTipo == 50
				U_TGCVR035() //Relatório do Rateio Vigente
			Case nTipo == 51
				U_TGCVR037() //Relatório do Rateio Vigente	
			Case nTipo == 52
				U_FatCtrDia(1) //Faturamento dia Inicio
			Case nTipo == 53
				U_FatCtrDia(2) //Faturamento dia Termino
		End Case
	Else
		
		CN9->(DbGoTo(nRecCN9))
		
		If !U_GV01AVAL( CN9->CN9_NUMERO, CN9->CN9_REVISA, .T. ) //Caso não exista itens do Grupo/Unidade logado, abre o contrato apenas como visualização
			If ApMsgYesNo( STR0018, STR0002 ) //"Deseja abrir o contrato como visualização?"###"Vis. Contrato" 
				U_TGC001CV(nRecCN9) //"Vis. Contrato"
			EndIf
		Else
			Do Case
				Case nTipo == 1
					U_TGC001ST(nRecCN9) //"Situacao"
		
				Case nTipo == 2
					U_TGC001CE(nRecCN9) //"Excluir"
		
				Case nTipo == 3
					If !U_GV27SMED( CN9->CN9_NUMERO, CN9->CN9_REVISA, cEmpAnt )
						ApMsgAlert( 'Existem Medicoes em Aberto para o Contrato [ '+ AllTrim( CN9->CN9_NUMERO ) +' ][ '+ AllTrim( CN9->CN9_REVISA ) +' ]', 'Medicao Em Aberto' )
					Else
						U_TGC001TR(nRecCN9) //"Transf. Licenças"
					EndIf
				
				Case nTipo == 4
					If !U_GV27SMED( CN9->CN9_NUMERO, CN9->CN9_REVISA, cEmpAnt )
						ApMsgAlert( 'Existem Medicoes em Aberto para o Contrato [ '+ AllTrim( CN9->CN9_NUMERO ) +' ][ '+ AllTrim( CN9->CN9_REVISA ) +' ]', 'Medicao Em Aberto' )
					Else
						U_TGC001RE(nRecCN9) //"Reativar Itens"
					EndIf
					
				Case nTipo == 5
					If !U_GV27SMED( CN9->CN9_NUMERO, CN9->CN9_REVISA, cEmpAnt )
						ApMsgAlert( 'Existem Medicoes em Aberto para o Contrato [ '+ AllTrim( CN9->CN9_NUMERO ) +' ][ '+ AllTrim( CN9->CN9_REVISA ) +' ]', 'Medicao Em Aberto' )
					Else
						U_GV020MAN(nRecCN9) //"Rollback de Contratos"
					EndIf
					
				Case nTipo == 6
					U_TGC001AP(nRecCN9) //"Aprov. Revisão"
				
				Case nTipo == 7
					CN240Acesso(nRecCN9) //"Controle Acesso"
			
				Case nTipo == 8
					U_TGC001CA(nRecCN9) //"Alt. Contrato"
			End Case
		EndIf
	EndIf
Endif 	

Return Nil


//-------------------------------------------------------------------
/*/{Protheus.doc} GV01AVAL
Valida Alteração - Valida se existe item da empresa/filial logado no contrato que está sendo alterado.

<AUTHOR> Ferreira
@since 07/04/2016
@return lRet
/*/
//-------------------------------------------------------------------
User Function GV01AVAL(cContra, cRevisa, lShowMsg)
Local aArea			:= GetArea()
Local lRet			:= .F.
Local cQuery 		:= ""
Local cTMPTRB  		:= GetNextAlias()
Local cEmpExclus	:= GETMV("TI_FEXCLSM",,"00001001000|00001001100") // GCV - Codigo das filiais exclusivas, somente logado nessas filiais podem editar os contratos dessa filial
Local aFilExc 		:= {}
Local nC			:= 0
Local cDescri		:= ""

Default cContra		:= ""
Default cRevisa		:= ""
Default lShowMsg	:= .F.


If !(cFilAnt $ cEmpExclus) // Se não for filial Exclusiva
 
	cQuery := " SELECT COUNT(1) CONTADOR "+CRLF
	cQuery += " FROM "+RetSqlName("CNB")+CRLF
	cQuery += " WHERE CNB_FILIAL = '"+FWxFilial("CNB")+"' "+CRLF
	cQuery += "   AND CNB_CONTRA = '"+cContra+"' "+CRLF
	cQuery += "   AND CNB_REVISA = '"+cRevisa+"' "+CRLF
	cQuery += "   AND CNB_UNINEG NOT IN " + FormatIn(cEmpExclus,"|") + " " +CRLF
	cQuery += "   AND D_E_L_E_T_ = ' ' "+CRLF
	
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cTMPTRB,.F.,.T.)
	(cTMPTRB)->(dbGoTop())
	If !(cTMPTRB)->( Eof() )
		If (cTMPTRB)->CONTADOR > 0
			lRet := .T.
		EndIf
	EndIf
	(cTMPTRB)->( dbCloseArea() )
	
	If !lRet .And. lShowMsg .And. !IsBlind()	
		
		cQuery := " SELECT DISTINCT CNB_GRUPO, CNB_UNINEG "+CRLF
		cQuery += " FROM "+RetSqlName("CNB")+CRLF
		cQuery += " WHERE CNB_FILIAL = '"+FWxFilial("CNB")+"' "+CRLF
		cQuery += "   AND CNB_CONTRA = '"+cContra+"' "+CRLF
		cQuery += "   AND CNB_REVISA = '"+cRevisa+"' "+CRLF
		cQuery += "   AND D_E_L_E_T_ = ' ' "+CRLF
		
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cTMPTRB,.F.,.T.)
		(cTMPTRB)->(dbGoTop())
		If (cTMPTRB)->(! Eof())
			While (cTMPTRB)->(! Eof())
				
				cDescri += Alltrim(Posicione("SM0", 1, (cTMPTRB)->(CNB_GRUPO+CNB_UNINEG), "M0_FILIAL" )) +CRLF
				(cTMPTRB)->(dbSkip())
				
			EndDo
			Help(" ",1, 'Help','GV01AVAL_01', STR0017 + cDescri , 3, 0 ) // "Esse contrato só pode ser editado pela Unidade(s) de Negócio:"
		Else
			lRet := .T.
		EndIf
		(cTMPTRB)->( dbCloseArea() )		
			
	EndIf
	
Else

	cQuery := " SELECT COUNT(1) CONTADOR "+CRLF
	cQuery += " FROM "+RetSqlName("CNB")+CRLF
	cQuery += " WHERE CNB_FILIAL = '"+FWxFilial("CNB")+"' "+CRLF
	cQuery += "   AND CNB_CONTRA = '"+cContra+"' "+CRLF
	cQuery += "   AND CNB_REVISA = '"+cRevisa+"' "+CRLF
	cQuery += "   AND CNB_UNINEG IN " + FormatIn(cEmpExclus,"|") + " " +CRLF
	cQuery += "   AND D_E_L_E_T_ = ' ' "+CRLF
	
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cTMPTRB,.F.,.T.)
	(cTMPTRB)->(dbGoTop())
	If !(cTMPTRB)->( Eof() )
		If (cTMPTRB)->CONTADOR > 0
			lRet := .T.
		EndIf
	EndIf
	(cTMPTRB)->( dbCloseArea() )
	
	If !lRet .And. lShowMsg .And. !IsBlind()
	
		aFilExc := StrToArray(cEmpExclus,"|")
		For nC := 1 To Len(aFilExc)
			cDescri += Alltrim(Posicione("SM0", 1, cEmpAnt + aFilExc[nC] , "M0_FILIAL" )) +CRLF
		Next nC
		
		Help(" ",1, 'Help','GV01AVAL_02', STR0019 + cDescri +"." , 3, 0 ) 	 //"Esse contrato só pode ser editado pela(s) Unidade de Negócio: "
	EndIf
	
EndIf
RestArea(aArea)

Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} GV01VLCP
Valida se a rotina pode ser acessada de acordo com o bloqueio em contrato

<AUTHOR> wise
@since 28/04/2017
@return lRet
/*/
//-------------------------------------------------------------------
User Function GV01VLCP(cContra, cRevisa, lShowMsg, nTipo)
Local aArea			:= GetArea()
Local lRetorno		:= .T.
Local cQuery 		:= ""
Local cAliasCNB		:= GetNextAlias()
Local cFilDescr		:= ""
Local lNValida		:= .T. 
Default cContra		:= ""
Default cRevisa		:= ""
Default lShowMsg	:= .T.
Default nTipo		:= 0 

lNValida := "|"+Alltrim(cValToChar(nTipo))+"|" $ "|10|13|14|15|16|17|18|19|22|23|"

If ! lNValida

	cQuery := " SELECT DISTINCT CNB_GRUPO,CNB_UNINEG "+CRLF
	cQuery += " FROM "+RetSqlName("CNB")+CRLF
	cQuery += " WHERE CNB_FILIAL = '"+FWxFilial("CNB")+"' "+CRLF
	cQuery += "   AND CNB_CONTRA = '"+cContra+"' "+CRLF
	cQuery += "   AND CNB_REVISA = '"+cRevisa+"' "+CRLF
	cQuery += "   AND D_E_L_E_T_ = ' ' "+CRLF
		
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAliasCNB,.F.,.T.)
	
	(cAliasCNB)->(dbGoTop())

	While !(cAliasCNB)->(EOF())
	
		//Caso reotrne .F. o período está fechado
		lRetorno := U_GVCMPOPEN(Nil,(cAliasCNB)->CNB_UNINEG)	
		
		If !lRetorno 
			
			If lShowMsg	
				cFilDescr := Alltrim(Posicione("SM0", 1, (cAliasCNB)->(CNB_GRUPO+CNB_UNINEG), "M0_FILIAL" ))
				Help(" ",1, 'Help','GV01VLCP_01', "COMPETÊNCIA BLOQUEADA PARA MANUTENÇÃO NA UNIDADE " + Alltrim((cAliasCNB)->CNB_UNINEG)+" - "+cFilDescr + " - Há item(s) neste contrato cuja competência será faturada/atualizada pela rotina do faturamento, não sendo permitido alterações nesse momento. Tente mais tarde a alteração ou visualize o contrato nesse momento." , 3, 0 )				
	
	 		Endif 
			
			Exit
		
		Endif
	
		(cAliasCNB)->(DbSkip())
	
	Enddo 	
		
Endif  

RestArea(aArea)

Return lRetorno 
