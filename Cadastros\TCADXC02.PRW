#include "totvs.ch"
#Include 'FWMVCDef.ch'

//Variáveis Estáticas
Static cTitulo := "Clientes Monitorados"
 
/*/{Protheus.doc} User Function 
    (long_description)
    @type  Function
    <AUTHOR>
    @since 22/08/2023
    @version TIPDBP-1750
    @param 
    @return 
    @example
    (examples)
    @see (links_or_references)
    /*/
User Function TCADXC02()
//https://terminaldeinformacao.com/2015/08/26/exemplos-de-rotinas-mvc-em-advpl/

    Local aArea   := GetArea()
    Local oBrowse
     
    DbSelectArea("AI0")
    oBrowse := FWMBrowse():New()
    oBrowse:Set<PERSON>lias("AI0")
    oBrowse:SetDescription(cTitulo)
	oBrowse:SetFilterDefault("AI0_XMONCL=='S'")
    oBrowse:AddLegend( "AI0_XMONCL=='S'", "GREEN",    "Original" )
    oBrowse:AddLegend( "AI0_XMONCL=='N'", "RED",    "Não Original" )
    oBrowse:DisableDetails()
    oBrowse:Activate()
     
    RestArea(aArea)
Return Nil
 

/*/{Protheus.doc} User Function 
    (long_description)
    @type  Function
    <AUTHOR> Matheus Monteiro
    @since 22/08/2023
    @version TIPDBP-1750
    @param 
    @return 
    @example
    (examples)
    @see (links_or_references)
    /*/
Static Function MenuDef()
    Local aRot := {}
    ADD OPTION aRot TITLE 'Excluir Monitoramento'    ACTION 'RptStatus({|| u_BDDelMon()}, "Exclusao de Monitoramento", "Executando rotina...")'     OPERATION 6  ACCESS 0 //OPERATION X
Return aRot
 

/*/{Protheus.doc} User Function 
    (long_description)
    @type  Function
    <AUTHOR> Matheus Monteiro
    @since 22/08/2023
    @version TIPDBP-1750
    @param 
    @return 
    @example
    (examples)
    @see (links_or_references)
    /*/ 
Static Function ModelDef()
    Local oModel := Nil
    Local oStAI0 := FWFormStruct(1, "AI0", {|cCampo|  AllTrim(cCampo) $ 'AI0_CODCLI' }) //

    oModel := MPFormModel():New("TCADXC02",/*bPre*/, /*bPos*/,/*bCommit*/,/*bCancel*/) 
    oModel:AddFields("AI0MASTER",/*cOwner*/,oStAI0)
    oModel:SetPrimaryKey({'AI0_CODCLI','AI0_LOJA'})
    oModel:SetDescription("Modelo de Dados do Cadastro "+cTitulo)
    oModel:GetModel("AI0MASTER"):SetDescription("Formulário do Cadastro "+cTitulo)
Return oModel
 

 
/*/{Protheus.doc} User Function 
    (long_description)
    @type  Function
    <AUTHOR> Matheus Monteiro
    @since 22/08/2023
    @version TIPDBP-1750
    @param 
    @return 
    @example
    (examples)
    @see (links_or_references)
    /*/ 
Static Function ViewDef()

    Local oModel := FWLoadModel("TCADXC02") 
    Local oStAI0 := FWFormStruct(2, "AI0", {|cCampo|  AllTrim(cCampo) $ 'AI0_CODCLI' })  //pode se usar um terceiro parâmetro para filtrar os campos exibidos { |cCampo| cCampo $ 'AI0_NOME|AI0_DTAFAL|'}
    Local oView := Nil
 
    oView := FWFormView():New()
    oView:SetModel(oModel)
    oStAI0:RemoveField('AI0_CODCLI')
    oView:AddField("VIEW_AI0", oStAI0, "AI0MASTER")
    oView:CreateHorizontalBox("TELA",100)
    //oView:EnableTitleView('VIEW_AI0', 'Dados do Grupo de Produtos' )  
    //oView:SetCloseOnOk({||.T.})
    //oView:SetOwnerView("VIEW_AI0","TELA")
Return oView
 





/*/{Protheus.doc} User Function 
    (long_description)
    @type  Function
    <AUTHOR> Matheus Monteiro
    @since 22/08/2023
    @version TIPDBP-1750
    @param 
    @return 
    @example
    (examples)
    @see (links_or_references)
    /*/
User Function BDDelMon()
  
    Local oRest    := Nil
    Local aHeader  := {}
    Local cResult  := ""
    Local lRet     := .F.
	Local cACESS_T := ""
    Local cTokenId  :=  ''
	Local nRecPN4  := 0
    Local cSubscriptionId  := '' 
    Local oJsonSub := Nil
    Local aAreaPN4 := PN4->(GetArea())
 	Local cInteg 	:= SuperGetMv("TI_CDXC01I", ,"000000")
	Local cProce 	:= SuperGetMv("TI_CDXC02D", ,"000035")
	//Local cProcM 	:= SuperGetMv("TI_CDXC01M", ,"000037")   
    Local oView     := FWViewActive()



	Private oJson := Nil
    

     SetRegua(6)


    DbSelectArea("PN4")
	DbSetOrder(1) //PN4_FILIAL+PN4_CINTEG+PN4_PROCES+PN4_TPMETO

	If !PN4->(DbSeek(FwXFilial("PN4") + cInteg + cProce))
		PN4->(RestArea(aAreaPN4))  
		Return
	EndIf
    IncRegua()



	// Recupera metodo de consumo de token
	nRecPN4 := PN4->(Recno())
	If !PN4->(DbSeek(FwXFilial("PN4") +cInteg + PN4->PN4_PTOKEN))
		PN4->(RestArea(aAreaPN4)) 
        Alert("Sem credenciais de Token") 
		Return
	EndIf
    IncRegua()	

    oRest := FWREST():New(AllTrim(PN4->PN4_URLWS))
    oRest:SetPath(AllTrim(PN4->PN4_METODO))
	oRest:SetPostParams(AllTrim(PN4->PN4_AUTENF))
	aHeader := {}
	Aadd( aHeader, 'Content-Type:application/json' )
	If oRest:Post(aHeader)
		cRetWsPad := IIF( Valtype(oRest:GetResult()) <> "U", oRest:GetResult() , "")
		FWJsonDeserialize(cRetWsPad,@oJSon)
		If Type("oJson:token") = "C"
			cACESS_T := oJson:token
            cTokenId := oJson:tokenID
		EndIf
	Else
		cResult	:= alltrim(oRest:GetResult())
        Alert("Erro: " + cResult) 
		Return
	EndIf
    IncRegua()

    PN4->(DbGoTo(nRecPN4))
	oRest := FWREST():New(AllTrim(PN4->PN4_URLWS))
    
    // Defino o método que vai ser consumido.
    oRest:SetPath(AllTrim(PN4->PN4_METODO)) 

	Aadd( aHeader, 'Content-Type:application/json' )
	Aadd( aHeader, 'AccessToken: '  + cACESS_T)
    Aadd( aHeader, 'TokenId: '      + cTokenId)
	
	
    //+++++++++++
    //https://docs.bigdatacorp.com.br/plataforma/reference/api-de-monitoramento-unsubscribe
    oJsonSub := JsonObject():New()
	oJsonSub:FromJson(AllTrim(AI0->AI0_XDTASE))
    cSubscriptionId := oJsonSub:GetJsonObject("SubscriptionId")
    //+++++++++++
    cPost := '{ '
	cPost += '"SubscriptionIds": ["'+cSubscriptionId+'"]'     
	cPost += '}'

	oRest:SetPostParams(cPost)

    lRet := oRest:Post(aHeader)
    IncRegua()
    If lRet	
		cResult	:= alltrim(oRest:GetResult())
		AI0->(RecLock("AI0", .F.))
		AI0->AI0_XDTASE := cResult
		AI0->AI0_XMONCL := 'N'
		AI0->(MsUnLock())
    Else
		Alert("Erro: " + alltrim(oRest:GetLastError()) )
		
    EndIf  
    IncRegua()


	FreeObj(oRest)
	oRest := Nil  
    IncRegua()

    If oView <> Nil
		oView:Refresh()
	EndIf
Return Nil
