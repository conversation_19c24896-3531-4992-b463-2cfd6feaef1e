#include "TOTVS.CH"

 /*/{Protheus.doc} User Function TCOMJ000
	Callback de autenticacao Mercado Eletronico
	@type  Function
	<AUTHOR>
	@since 11/01/2023
	@version 1.0
	/*/
User Function TCOMJ000(cRespBody,oInterceptor,lLock)
	Local cToken    := oInterceptor:SearchJsonKey(cRespBody,"accessToken")
	Local nExpire   := oInterceptor:SearchJsonKey(cRespBody,"expiresIn")
	Local cAuth     := 'Authorization: Bearer ' + cToken + "|Content-Type: application/json"
	Local cExpire   := ""

	If Valtype(nExpire) <> "N" .Or. Empty(cToken)
		Return
	EndIf

	cExpire :=	RetDtHrExp(nExpire)

	If lLock
		Reclock("P36",.F.)
		P36->P36_HEADRE := cAuth
		P36->P36_EXPIRE := cExpire
		P36->(MsUnlock())
	Else
		oInterceptor:HeaderAuthResp:= cAuth
		oInterceptor:ResponseAuth := cRespBody
	EndIf
Return

User Function COMJ000A(cRespBody,oInterceptor,lLock)
	Local cToken    := oInterceptor:SearchJsonKey(cRespBody,"access_token")
	Local nExpire   := oInterceptor:SearchJsonKey(cRespBody,"expires_in")
	Local cAuth     := 'Authorization: Bearer ' + cToken + "|Content-Type: application/json"
	Local cExpire   := ""

	If Valtype(nExpire) <> "N" .Or. Empty(cToken)
		Return
	EndIf

	cExpire :=	RetDtHrExp(nExpire)

	If lLock
		Reclock("P36",.F.)
		P36->P36_HEADRE := cAuth
		P36->P36_EXPIRE := cExpire
		P36->(MsUnlock())
	Else
		oInterceptor:HeaderAuthResp:= cAuth
		oInterceptor:ResponseAuth := cRespBody
	EndIf
Return


Static Function RetDtHrExp(nExpire)
	Local cTime       := ""
	Local nSeconds    := 0
	Local dDtExpira   := Date() 
	Local cDtHrExpira := ""

	Default nExpire := 0

	nSeconds := nExpire

	If nExpire > 1
		nSeconds := nSeconds - 1
	EndIf
	cTime := IncTime(Time(), 0, 0, nSeconds)

	If Left(cTime, 2) >= "24"
		cTime := DecTime(cTime, 24, 0, 0)
		dDtExpira := DaySum(dDtExpira, 1)
	EndIf

	cDtHrExpira := DtoS(dDtExpira) + strtran(strtran(cTime,":")," ") 

Return cDtHrExpira

User Function TCOMJ0ID(cP37_CODEXT)

	Local oJson:= JsonObject():new()
	Local aAreaP37 := P37->(GetArea())
	Local lFound   := .F.

	If Empty(P37->P37_HEADER) .Or. P37->(FieldPos("P37_CODEXT")) == 0
		Return .F.
	EndIF
	
	oJson:FromJson(P37->P37_HEADER)
	If ValType(oJson['X-ME-EVENT-ID']) = "C"
		cP37_CODEXT := oJson['X-ME-EVENT-ID']
		cP37_CODEXT := Left(cP37_CODEXT + Space(Len(P37->P37_CODEXT)), Len(P37->P37_CODEXT))
	EndIF

	IF ! Empty(cP37_CODEXT)
		DbSelectArea("P37")
		DbSetOrder(7)	// P37_FILIAL+P37_CODEXT
		lFound := DbSeek(xFilial() + cP37_CODEXT)
	EndIf

	P37->(RestArea(aAreaP37))
    FreeObj(oJson)
	oJson := Nil

Return lFound

/* {Protheus.doc} TIINTME()
	Funcao Job Responsavel por executar o  envio e recebimento do Interceptor
	
	<AUTHOR>
	@since 11/05/2023
	@version 1.0
*/

User Function TIINTME()
	Local oIntercep	:= Nil
	Local cFila     := "ME"
	
	If Select("SM0") == 0
		RpcSetEnv( '00', '00001000100')
	EndIf

	//Inicia o Interceptor
	oIntercep:=TIINTERCEPTOR():New()
	oIntercep:PrepareDistributedQueues(cFila, "1", .t.)  //Envio
	oIntercep:PrepareDistributedQueues(cFila, "2", .t.)  //Recebimento

	RpcClearEnv()
	FreeObj(oIntercep)
	oIntercep := Nil
Return

/* {Protheus.doc} TIINTMEE()
	Funcao Job Responsavel por executar somente o  envio  do Interceptor
	
	<AUTHOR>
	@since 11/05/2023
	@version 1.0
*/

User Function TIINTMEE()
	Local oIntercep	:= Nil
	Local cFila     := "ME"
	
	If Select("SM0") == 0
		RpcSetEnv( '00', '00001000100')
	EndIf

	//Inicia o Interceptor
	oIntercep:=TIINTERCEPTOR():New()
	oIntercep:PrepareDistributedQueues(cFila, "1", .T.)  //Envio

	RpcClearEnv()
	FreeObj(oIntercep)
	oIntercep := Nil
Return

/* {Protheus.doc} TIINTMEE()
	Funcao Job Responsavel por executar somente o recedimento do Interceptor
	
	<AUTHOR>
	@since 11/05/2023
	@version 1.0
*/

User Function TIINTMER()
	Local oIntercep	:= Nil
	Local cFila     := "ME"
	
	If Select("SM0") == 0
		RpcSetEnv( '00', '00001000100')
	EndIf

	//Inicia o Interceptor
	oIntercep:=TIINTERCEPTOR():New()
	oIntercep:PrepareDistributedQueues(cFila, "2", .t.)  // Recebimento

	RpcClearEnv()
	FreeObj(oIntercep)
	oIntercep := Nil
Return
