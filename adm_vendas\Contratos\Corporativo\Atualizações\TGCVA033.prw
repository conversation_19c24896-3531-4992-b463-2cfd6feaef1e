#Include "Protheus.ch"
#Include "RwMake.ch"
#Include "TGCVA033.ch"

Static _cMsgErro := ""	// Mensagem de erro

/*/{Protheus.doc} TGCVA033
Função para geração do cálculo

<AUTHOR> <PERSON>
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cAnoRef, caracter, ano de referencia do calculo.
@Param _cTipCalc, caracter, qual o tipo de calculo (N=Normal;R=Revisao;I=Inclusao Cnpj;E=Exclusao Cnpj).
@Param _cCalculo, caracter, qual calculo (I=Incremento;D=Diferenca).
@Param _cMulta, caracter, se gera multa ou não.
@Param _nVlrDesc, numerico, valor de desconto. Variavel utilizada para o calculo de inclusão e exclusão de CNPJ.
@Param _cMensCalc, caracter, mensagem no calculo.

@Return caracter, contem a mensagem de erro.
/*/

User Function TGCVA033(_cCliente,_cLoja,_cAnoRef,_cTipCalc,_cCalculo,_cMulta,_nVlrDesc,_cMensCalc, cDiagErr)

	Local _cContrato 	:= ""
	Local _cRevisao 	:= ""
	Local _lValidInd	:= .T.

	Private _cNomeView := ""
	Private _cAnoCorp := ""
	Private _cSiteFat := ""
	Private _cCondPag := ""

	Private _aProdCorp := {}
	
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cAnoRef := ""
	Default _cTipCalc := ""
	Default _cCalculo := ""
	Default _cMulta := "N"
	Default _cMensCalc := ""
	Default cDiagErr := ""
	
	Default _nVlrDesc := 0

	_cMsgErro := ""
	
	// Função para abrir as tabelas utilizadas.
	_fOpenTab()
	
	// Busca a ultima revisão do contrato
	_fGetContr(_cCliente,_cLoja,@_cContrato,@_cRevisao)

	U_TCORP_REV(_cCliente,_cRevisao)

	// Função para validar o calculo.
	_fValidInf(_cContrato,_cRevisao,_cCliente,_cLoja,_cAnoRef,_cTipCalc,_cCalculo,_cMulta)
	
	If Empty(_cMsgErro)
		// Função para atualizar as tabelas relacionadas
		_lValidInd := _fAtualiza(_cContrato,_cRevisao,_cCliente,_cLoja)
		
		If _lValidInd
			//Função para calcular.
			_fCalcula(_cContrato,_cRevisao,_cCliente,_cLoja,_cTipCalc,_cCalculo,_cMulta,_nVlrDesc,_cMensCalc)
		Else
			_cMsgErro := "Cálculo abortado"
		EndIf
	Else
		_cMsgErro := STR0001 + _cMsgErro // "[TGCVA033]: " 
		MsgStop(_cMsgErro)
	EndIf
	
	// Função para fechar as tabelas utilizadas.
	_fCloseTab()

cDiagErr := _cMsgErro

Return Empty(_cMsgErro)

/*/{Protheus.doc} GV033A01
Função para retornar a mensagem de erro.

<AUTHOR> Ribeiro
@Since 10/05/15

@Return caracter, mensagem de erro.
/*/

User Function GV033A01()

Return _cMsgErro

/*/{Protheus.doc} _fOpenTab
Função para abriar as tabelas utilizadas.

<AUTHOR> Ribeiro
@Since 10/05/15
/*/

Static Function _fOpenTab()

	DbSelectArea("SA1")	// CLIENTES
	SA1->(DbSetOrder(1))	// A1_FILIAL+A1_COD+A1_LOJA
	
	DbSelectArea("ZTS")	// CLIENTES CORPORATIVOS
	ZTS->(DbSetOrder(1))	// ZTS_FILIAL+ZTS_CODCLI+ZTS_LOJA

	DbSelectArea("ZAL")	// CADAST.EMPRESA/FILIAL CLIENTES
	ZAL->(DbSetOrder(1))	// ZAL_FILIAL+ZAL_CLIENT+ZAL_LOJA+ZAL_CONTRA+ZAL_PROPOS+ZAL_VERSAO+ZAL_ITPR+ZAL_PRCDU+ZAL_TPREAJ+ZAL_ANOREF+ZAL_ANOREA

	DbSelectArea("ZAX")	// TABELA CLIENTES CORPORATIVO
	ZAX->(DbSetOrder(2))	// ZAX_FILIAL+ZAX_CLIENT+ZAX_LOJA+ZAX_ANOREF+ZAX_PRCDU+ZAX_ITEM

	DbSelectArea("ZAW")	// EXCECOES CORPORATIVO
	ZAW->(DbSetOrder(1))	// ZAW_FILIAL+ZAW_CLIENT+ZAW_LOJA+ZAW_CONTRA+ZAW_PROPOS+ZAW_VERSAO+ZAW_ITPR

	DbSelectArea("CN9")	// CONTRATOS
	CN9->(DbSetOrder(1))	// CN9_FILIAL+CN9_NUMERO+CN9_REVISA

	DbSelectArea("CNB")	// ITENS DAS PLANILHAS CONTRATOS
	CNB->(DbSetOrder(1))	// CNB_FILIAL+CNB_CONTRA+CNB_REVISA+CNB_NUMERO+CNB_ITEM

	DbSelectArea("CNC")	// FORNEC./CLIENTES X CONTRATOS.
	CNC->(DbSetOrder(1))	// CNC_FILIAL+CNC_NUMERO+CNC_REVISA+CNC_CODIGO+CNC_LOJA

	DbSelectArea("SB1")	// DESCRICAO GENERICA DO PRODUTO
	SB1->(DbSetOrder(1))	// B1_FILIAL+B1_COD

	DbSelectArea("SBM")	// GRUPO DE PRODUTO
	SBM->(DbSetOrder(1))	// BM_FILIAL+BM_GRUPO

	DbSelectArea("ZQJ")	// LINHA DE RECEITA DO CORPORATIVO
	ZQJ->(DbSetOrder(1))	// ZQJ_FILIAL+ZQJ_CODIGO+ZQJ_ATIVO

	DbSelectArea("ZQK")	// ITENS LINHA DE RECEITA CORP
	ZQK->(DbSetOrder(1))	// ZQK_FILIAL+ZQK_CODZQJ+ZQK_VERSAO+ZQK_GRUPO+ZQK_ITEM

	DbSelectArea("PH0")	// CABECALHO CALCULO CORPORATIVO
	PH0->(DbSetOrder(1))	// PH0_FILIAL+PH0_CODIGO

	DbSelectArea("PH1")	// ITENS DO CALCULO CORPORATIVO
	PH1->(DbSetOrder(1))	// PH1_FILIAL+PH1_CODIGO+PH1_ITEM+PH1_CODPRD

	DbSelectArea("PH2")	// MEMORIA CALCULO CORPORATIVO
	PH2->(DbSetOrder(1))	// PH2_FILIAL+PH2_CODIGO+PH2_TIPMET

Return

/*/{Protheus.doc} _fCloseTab
Função para fechar as tabelas utilizadas.

<AUTHOR> Ribeiro
@Since 10/05/15
/*/

Static Function _fCloseTab()

	SA1->(DbCloseArea());	ZTS->(DbCloseArea());	ZAL->(DbCloseArea())	
	
	ZAX->(DbCloseArea());	ZAW->(DbCloseArea());	CN9->(DbCloseArea())
	
	CNB->(DbCloseArea());	CNC->(DbCloseArea());	SB1->(DbCloseArea())

	SBM->(DbCloseArea());	ZQJ->(DbCloseArea());	ZQK->(DbCloseArea())

	/*
		As tabelas (PH0,PH1,PH2) do calculo não são encerradas para que permaneca posicionado nos registros gerados.
	*/
Return

/*/{Protheus.doc} _fValidInf
Função para validar o calculo

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cAnoRef, caracter, ano de referencia do calculo.
@Param _cTipCalc, caracter, qual o tipo de calculo (N=Normal;R=Revisao;I=Inclusao Cnpj;E=Exclusao Cnpj).
@Param _cCalculo, caracter, qual calculo (I=Incremento;D=Diferenca).
@Param _cMulta, caracter, se gera multa ou não.

@Return caracter, contem a mensagem de erro.
/*/

Static Function _fValidInf(_cContrato,_cRevisao,_cCliente,_cLoja,_cAnoRef,_cTipCalc,_cCalculo,_cMulta)

	Local _lContinua := .T.
	
	Local _aCalcDel := {}

	Local _nCount := 0

	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cAnoRef := ""
	Default _cTipCalc := ""
	Default _cCalculo := ""
	Default _cMulta := "N"
	
	_cAnoCorp := _cAnoRef
	
	If Empty(_cAnoCorp)
		_cAnoCorp := GetMv("TDI_ANOCOR")
	EndIf
	
	// Se não encontra o ano corporativo.
	If Empty(_cAnoCorp)
		_cMsgErro := STR0002 //"Não foi informado o ano corporativo para o calculo!"
	EndIf
	
	// Se o código do cliente não foi passado.
	If Empty(_cMsgErro) .And. (Empty(_cCliente) .Or. Empty(_cLoja))
		_cMsgErro := STR0003 + STR0004 //"Cliente "###"não informado!"
	EndIf
	
	// Se o código do cliente não foi encontrato no cadastro de clientes.
	SA1->(DbSetOrder(1))
	If Empty(_cMsgErro) .And. !SA1->(DbSeek(xFilial("SA1")+_cCliente+_cLoja))
		_cMsgErro := STR0003 + "[" + _cCliente + "/" + _cLoja + "] " + STR0005 // "Cliente "### "não cadastrados na tabela de clientes!"
	EndIf
	
	// Se o código do cliente não foi encontrato no cadastro de clientes corporativos. 
	ZTS->(DbSetOrder(1))
	If Empty(_cMsgErro) .And. !ZTS->(DbSeek(xFilial("ZTS")+_cCliente+_cLoja))
		_cMsgErro := STR0003 + "[" + _cCliente + "/" + _cLoja + "] " + STR0006 // "Cliente "###"não cadastrados na tabela de clientes!"
	Else
		_cSiteFat := ZTS->(ZTS_GRPFAT+ZTS_FILFAT)
		
		// Se o site de faturamente não estiver preenchido na ZTS, verifica qual site de faturamente o cliente tem contrato ativo. 
		If Empty(_cSiteFat)
			_cSiteFat := U_GV001X16(_cCliente,_cLoja,,,,,,.T.)
		EndIf
		
		// Se não foi encontrado em nenhum contrato, utiliza o site logado pelo usuário. 
		If Empty(_cSiteFat)
			_cSiteFat := cEmpAnt + cFilAnt
		Endif
	EndIf
	
	// Se o cliente esta inativo no cadastro de clientes do corporativo.
	If Empty(_cMsgErro) .And. ZTS->ZTS_STATUS <> "A"
		_cMsgErro := STR0003 + "[" + _cCliente + "/" + _cLoja + "] " + STR0007 // "Cliente "###"não esta ativo no cadastro de clientes corporativos!"
	EndIf 
	
	// Se o tipo de receita do cliente não foi definido.
	If Empty(_cMsgErro) .And. Empty(ZTS->ZTS_TIPREC)
		_cMsgErro := STR0003 + "[" + _cCliente + "/" + _cLoja + "] " + STR0008 // "Cliente "###"não tem o tipo de receita definido!"
	EndIf
	
	// Verifica se existe mais algum calculo em aberto no ano corporativo
	_aCalcDel := _fVerCalc(_cCliente,_cLoja,1)
	
	// Se existir, deleta
	If !Empty(_aCalcDel)
		For _nCount := 1 To Len(_aCalcDel)
			_fDelCalc(_aCalcDel[_nCount][1])
		Next
	EndIf

	If Empty(_cMsgErro) .And. _cTipCalc $ "NR"
		_fCalcGera(_cCliente,_cLoja,_cTipCalc,_cCalculo)
	EndIf
	
	// Cria as view's para utilização da query principal do cliente.
	If Empty(_cMsgErro) .And. !_fMakeView(_cCliente,_cLoja)
		_cMsgErro := STR0009 // "Ocorreu problemas para iniciar o cálculo! Procure o administrador do sistema."
	EndIf
	
	// Cria as view's para utilização da query principal do cliente.
	If Empty(_cMsgErro)
		If !_fMakeQry(_cCliente,_cLoja)
			_cMsgErro := STR0003 + "[" + _cCliente + "/" + _cLoja + "] " + STR0010 // "Cliente "###"não possui produtos do corporativo ativo no contrato."
		Else
			// 1=CDU;2=ET;3=AR;4=SMS
			// Se o cliente não possui os produtos de CDU e de SMS.
			If Ascan(_aProdCorp,{|x| AllTrim(x[1]) == "1"}) < 1 .Or. Ascan(_aProdCorp,{|x| AllTrim(x[1]) $ "2|3|4"}) < 1
				_cMsgErro := STR0003 + "[" + _cCliente + "/" + _cLoja + "] " + STR0011 // "Cliente "###"não possui item de CDU ou SMS ativo."
			Else
				// Se o cliente possuir item de ET ou de AR ativo e de SMS também, não continua.
				//If Ascan(_aProdCorp,{|x| AllTrim(x[1]) == "4"}) > 0 .And. Ascan(_aProdCorp,{|x| AllTrim(x[1]) $ "2|3"}) > 0
				//	_cMsgErro := STR0003 + "[" + _cCliente + "/" + _cLoja + "] " + STR0012 // "Cliente "###"possui item de SMS e (ET ou AR) ativo."
				//EndIf
			EndIf
		EndIf
	EndIf
	
	// Verifica se existe tabela de indices correspondente ao produto de CDU ativo.
	If Empty(_cMsgErro) .And. !_fVerIdCDU(_cContrato,_cRevisao,_cCliente,_cLoja)
		_cMsgErro := STR0003 + "[" + _cCliente + "/" + _cLoja + "] " + STR0013 // "Cliente "###"não possui tabela de indices correspondente ao produto de CDU ativo."
	EndIf
	
	If Empty(_cMsgErro) .And. _cTipCalc == "R" .And. _fMetInfor(_cContrato,_cRevisao,_cCliente,_cLoja,_cTipCalc,_cCalculo,@_cMulta) <= 0
		_cMsgErro := STR0014 //"O valor da revisão não foi informado!"
	EndIf

Return

/*/{Protheus.doc} _fVerCalc
Função para buscar os calculos em abertos do referencia.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _nOpcao, numerico, opcao para utilização.

@Return array, contem os codigos de calculos em abertos.
/*/

Static Function _fVerCalc(_cCliente,_cLoja,_nOpcao)

	Local _cQuery := ""
	
	Default _cCliente := ""
	Default _cLoja := ""
	
	Default _nOpcao := 1

	_cQuery := " SELECT PH0.PH0_CODIGO, PH0.R_E_C_N_O_ " + CRLF
	_cQuery += " FROM " + RetSqlName("PH0") + " PH0 " + CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	PH0.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND PH0.PH0_FILIAL = '" + xFilial("PH0") + "' " + CRLF
	_cQuery += "	AND PH0.PH0_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND PH0.PH0_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND PH0.PH0_ANOREF = '" + _cAnoCorp + "' " + CRLF
	If _nOpcao == 1
		_cQuery += "	AND PH0.PH0_STATUS = '1' " + CRLF
	Else
		_cQuery += "	AND PH0.PH0_STATUS<> '1' " + CRLF
	EndIf

Return U_GV001X23(_cQuery)

/*/{Protheus.doc} _fCalcGera
Função para verificar os calculos já gerados e faturados.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cTipCalc, caracter, qual o tipo de calculo (N=Normal;R=Revisao;I=Inclusao Cnpj;E=Exclusao Cnpj).
@Param _cCalculo, caracter, qual calculo (I=Incremento;D=Diferenca).

@Return array, contem os codigos de calculos em abertos.
/*/

Static Function _fCalcGera(_cCliente,_cLoja,_cTipCalc,_cCalculo)

	Local _aDados := {}
	
	Local _nCount := 0
	Local _nNivel := 0
	Local _nCalc := 0

	Default _cCliente := ""
	Default _cLoja := ""
	Default _cTipCalc := ""
	Default _cCalculo := ""
	
	// Retornar os calculos gerados e faturados.
	_aDados := _fVerCalc(_cCliente,_cLoja,2)
	
	For _nCount := 1 To Len(_aDados)
		PH0->(DbGoTo(_aDados[_nCount][2]))
		If _nNivel < 1 .And. PH0->PH0_TIPCAL == "N" .And. PH0->PH0_CALC == "I"
			_nNivel := 1
		ElseIf _nNivel < 2 .And. PH0->PH0_TIPCAL == "R" .And. PH0->PH0_CALC == "I"
			_nNivel := 2
		ElseIf _nNivel < 3 .And. PH0->PH0_TIPCAL == "N" .And. PH0->PH0_CALC == "D"
			_nNivel := 3
		ElseIf _nNivel < 4 .And. PH0->PH0_TIPCAL == "R" .And. PH0->PH0_CALC == "D"
			_nNivel := 4
			Exit
		EndIf
	Next

	If _cTipCalc == "N" .And. _cCalculo == "I"
		_nCalc := 1
	ElseIf _cTipCalc == "R" .And. _cCalculo == "I"
		_nCalc := 2
	ElseIf _cTipCalc == "N" .And. _cCalculo == "D"
		_nCalc := 3
	ElseIf _cTipCalc == "R" .And. _cCalculo == "D"
		_nCalc := 4
	EndIf
	
	If _nNivel >= _nCalc
		_cMsgErro := STR0003 + "[" + _cCliente + "/" + _cLoja + "] " + STR0015 // "Cliente "###"já possui calculo de "
		
		If _nNivel == 1
			_cMsgErro += STR0016 + CRLF // "incremento!"
		ElseIf _nNivel == 2
			_cMsgErro += STR0017 + CRLF // "revisão do incremento!"
		ElseIf _nNivel == 3
			_cMsgErro += STR0018 + CRLF // "diferença!"
		ElseIf _nNivel == 4
			_cMsgErro += STR0019 + CRLF // "revisão da diferença!"
		EndIf
		
		If _nNivel > _nCalc
			_cMsgErro += "Não será possivel realizar um calculo "
			
			If _nCalc == 1
				_cMsgErro += STR0016 + CRLF // "incremento!"
			ElseIf _nCalc == 2
				_cMsgErro += STR0017 + CRLF // "revisão do incremento!"
			ElseIf _nCalc == 3
				_cMsgErro += STR0018 + CRLF // "diferença!"
			ElseIf _nCalc == 4
				_cMsgErro += STR0019 + CRLF // "revisão da diferença!"
			EndIf
		EndIf
	EndIf

Return

/*/{Protheus.doc} _fDelCalc
Função para deletar o calculo.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCodigo, caracter, codigo do calculo.
/*/

Static Function _fDelCalc(_cCodigo)

	Default _cCodigo := ""
	
	If !Empty(_cCodigo)
		Begin Transaction
			// Deleta o cabeçalho do calculo
			PH0->(DbSetOrder(1))
			If PH0->(DbSeek(xFilial("PH0")+_cCodigo))
			While PH0->(!Eof()) .And. xFilial("PH0")+_cCodigo == PH0->(PH0_FILIAL+PH0_CODIGO)
				If !Empty(PH0->PH0_CONDPG)
					_cCondPag := PH0->PH0_CONDPG
				EndIf
				RecLock("PH0",.F.)
					PH0->(DbDelete())
				PH0->(MsUnLock())
				PH0->(DbSkip())
				EndDo
			EndIf
			
			// Itens do calculo corporativo		
			PH1->(DbSetOrder(1))
			If PH1->(DbSeek(xFilial("PH1")+_cCodigo))
				While PH1->(!Eof()) .And. xFilial("PH1")+_cCodigo == PH1->(PH1_FILIAL+PH1_CODIGO)
					RecLock("PH1",.F.)
						PH1->(DbDelete())
					PH1->(MsUnLock())
					PH1->(DbSkip())
				EndDo
			EndIf
		
			// Memória de calculo corporativo
			PH2->(DbSetOrder(1))
			If PH2->(DbSeek(xFilial("PH2")+_cCodigo))
				While PH2->(!Eof()) .And. xFilial("PH2")+_cCodigo == PH2->(PH2_FILIAL+PH2_CODIGO)
					RecLock("PH2",.F.)
						PH2->(DbDelete())
					PH2->(MsUnLock())
					PH2->(DbSkip())
				EndDo
			EndIf
		End Transaction
	EndIf

Return

/*/{Protheus.doc} _fMakeView
Função para criar as views utilizadas no programa.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.

@Return logico, se conseguiu criar as views.
/*/

Static Function _fMakeView(_cCliente,_cLoja)

	Local _aQuery := {}
	
	Local _nPosicao := 0
	
	Local _lContinua := .T.
	
	Default _cCliente := ""
	Default _cLoja := ""

	_cNomeView := "TGCVA033" + STRTRAN(TIME(),':','') + AllTrim(STR(Aleatorio(999,3))) + __cUserId

	// View para buscar os contratos ativos
	_nPosicao := AddArray(@_aQuery)

	_aQuery[_nPosicao] := "CREATE OR REPLACE VIEW " + _cNomeView + "01 AS " + CRLF
	_aQuery[_nPosicao] += " SELECT " + CRLF
	_aQuery[_nPosicao] += "	 CN9.CN9_FILIAL FILIAL " + CRLF
	_aQuery[_nPosicao] += "	,CNC.CNC_CLIENT CLIENTE " + CRLF
	_aQuery[_nPosicao] += "	,CNC.CNC_LOJACL LOJA " + CRLF
	_aQuery[_nPosicao] += "	,CN9.CN9_NUMERO NUMERO " + CRLF
	_aQuery[_nPosicao] += "	,CN9.CN9_REVISA REVISA " + CRLF
	_aQuery[_nPosicao] += " FROM " + RetSqlName("CN9") + " CN9 " + CRLF
	_aQuery[_nPosicao] += CRLF 
	_aQuery[_nPosicao] += " INNER JOIN " + RetSqlName("CNC") + " CNC ON " + CRLF
	_aQuery[_nPosicao] += "	CNC.D_E_L_E_T_ = ' ' " + CRLF
	_aQuery[_nPosicao] += "	AND CNC.CNC_FILIAL = '" + xFilial("CNC") + "' " + CRLF
	_aQuery[_nPosicao] += "	AND CNC.CNC_NUMERO = CN9.CN9_NUMERO " + CRLF
	_aQuery[_nPosicao] += "	AND CNC.CNC_REVISA = CN9.CN9_REVISA " + CRLF
	_aQuery[_nPosicao] += "	AND CNC.CNC_CLIENT = '" + _cCliente + "' " + CRLF
	_aQuery[_nPosicao] += "	AND CNC.CNC_LOJACL = '" + _cLoja + "' " + CRLF
	_aQuery[_nPosicao] += "	AND CNC.CNC_TIPCLI = '01' " + CRLF
	_aQuery[_nPosicao] += CRLF	
	_aQuery[_nPosicao] += " WHERE " + CRLF
	_aQuery[_nPosicao] += "	CN9.D_E_L_E_T_ = ' ' " + CRLF
	_aQuery[_nPosicao] += "	AND CN9.CN9_FILIAL = '" + xFilial("CN9") + "' " + CRLF
	_aQuery[_nPosicao] += "	AND CN9.CN9_SITUAC = '05' " + CRLF
	_aQuery[_nPosicao] += "	AND CN9.CN9_ESPCTR = '2' " + CRLF
	_aQuery[_nPosicao] += "	AND CN9.CN9_TPCTO = '013' " + CRLF
	
	// View para buscar os produtos do corporativo
	_nPosicao := AddArray(@_aQuery)
 
	_aQuery[_nPosicao] := "CREATE OR REPLACE VIEW " + _cNomeView + "02 AS " + CRLF
	_aQuery[_nPosicao] += " SELECT " + CRLF
	_aQuery[_nPosicao] += "	 SB1.B1_COD PRODUTO " + CRLF
	_aQuery[_nPosicao] += "	,ZQJ.ZQJ_TIPREC TIPO " + CRLF
	_aQuery[_nPosicao] += " FROM " + RetFullName("ZQJ","00") + " ZQJ " + CRLF
	_aQuery[_nPosicao] += CRLF
	_aQuery[_nPosicao] += " INNER JOIN " + RetFullName("ZQK","00") + " ZQK ON " + CRLF
	_aQuery[_nPosicao] += "	ZQK.ZQK_FILIAL = '" + xFilial("ZQK") + "' " + CRLF
	_aQuery[_nPosicao] += "	AND ZQK.ZQK_CODZQJ = ZQJ.ZQJ_CODIGO " + CRLF
	_aQuery[_nPosicao] += "	AND ZQK.D_E_L_E_T_ = ' ' " + CRLF
	_aQuery[_nPosicao] += CRLF
	_aQuery[_nPosicao] += " INNER JOIN " + RetSqlName("SBM") + " SBM ON " + CRLF
	_aQuery[_nPosicao] += "	SBM.D_E_L_E_T_ = ' ' " + CRLF
	_aQuery[_nPosicao] += "	AND SBM.BM_FILIAL = '" + xFilial("SBM") + "' " + CRLF
	_aQuery[_nPosicao] += "	AND SBM.BM_GRUPO = ZQK.ZQK_GRUPO " + CRLF
	_aQuery[_nPosicao] += CRLF
	_aQuery[_nPosicao] += " INNER JOIN " + RetSqlName("SB1") + " SB1 ON " + CRLF
	_aQuery[_nPosicao] += "	SB1.D_E_L_E_T_ = ' ' " + CRLF
	_aQuery[_nPosicao] += "	AND SB1.B1_FILIAL = '" + xFilial("SB1") + "' " + CRLF
	_aQuery[_nPosicao] += "	AND SB1.B1_GRUPO = ZQK.ZQK_GRUPO " + CRLF
	_aQuery[_nPosicao] += CRLF
	_aQuery[_nPosicao] += " WHERE ZQJ.ZQJ_FILIAL = '" + xFilial("ZQJ") + "' " + CRLF
	_aQuery[_nPosicao] +=    "AND ZQJ_CODESP = '000001' " + CRLF
	_aQuery[_nPosicao] +=    "AND ZQJ.ZQJ_MSBLQL <> '1' " + CRLF
	_aQuery[_nPosicao] +=    "AND ZQJ.ZQJ_ATIVO <> 'N' " + CRLF
	_aQuery[_nPosicao] +=    "AND ZQJ.D_E_L_E_T_ = ' ' " + CRLF
	
	// Executa as views
	_lContinua := Empty(U_GV001X26(,,_aQuery))
	
	If _lContinua
		_nQtdeView := Len(_aQuery)
	EndIf
	
Return _lContinua

/*/{Protheus.doc} AddArray
Constrói os dados da GetDados

<AUTHOR> Ribeiro
@since 06/11/13
@version 11.5

@Param _aArray, array, contem o array que será adicionado. O parametro é passado por referencia.

@Return Numerico, o tamanho do array.
/*/

Static Function AddArray(_aArray)

	Default _aArray := {}
	
	Aadd(_aArray,)

Return Len(_aArray)

/*/{Protheus.doc} _fMakeQry
Função para montar a query principal.

<AUTHOR> Ribeiro
@Since 06/08/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.

@Return logica, se a query possui informações.
/*/

Static Function _fMakeQry(_cCliente,_cLoja)

	Local _cQuery		:= ""
	Local _aSetField	:= {}
	Local nI			:= 1
	Local aProdFinal	:= {}
	Local cIndCli		:= ""
	
	Default _cCliente	:= ""
	Default _cLoja		:= ""

	cIndCli := U_GC001XIC(_cCliente, _cLoja)
	
	If !Empty(_cCliente) .And. !Empty(_cLoja)
		_cQuery := " SELECT " + CRLF
		_cQuery += "	 PRDC.TIPO ZQJ_TIPREC " + CRLF
		_cQuery += "	,CNB.CNB_PRODUT " + CRLF
		_cQuery += "	,MIN(CNB.CNB_INDCTR) CNB_INDCTR " + CRLF
		_cQuery += "	,'F' CDUZAX " + CRLF
		_cQuery += "	,UVCT.NUMERO CN9_NUMERO " + CRLF
		_cQuery += "	,UVCT.REVISA CN9_REVISA " + CRLF
		_cQuery += "	,MAX(CNB.CNB_DATASS) CNB_DATASS " + CRLF
		_cQuery += " FROM " + _cNomeView + "01 UVCT " + CRLF
		_cQuery += CRLF
		_cQuery += " INNER JOIN " + RetSqlName("CNB") + " CNB ON " + CRLF
		_cQuery += "	CNB.D_E_L_E_T_ = ' ' " + CRLF
		_cQuery += "	AND CNB.CNB_FILIAL = UVCT.FILIAL " + CRLF
		_cQuery += "	AND CNB.CNB_CONTRA = UVCT.NUMERO " + CRLF
		_cQuery += "	AND CNB.CNB_REVISA = UVCT.REVISA " + CRLF
		_cQuery += "	AND CNB.CNB_SITUAC NOT IN ('C','S','M') " + CRLF
		_cQuery += CRLF
		_cQuery += " INNER JOIN " + _cNomeView + "02 PRDC ON " + CRLF
		_cQuery += "	PRDC.PRODUTO = CNB.CNB_PRODUT " + CRLF
		_cQuery += CRLF
		_cQuery += " WHERE " + CRLF
		_cQuery += "	UVCT.CLIENTE = '" + _cCliente + "' " + CRLF
		_cQuery += "	AND UVCT.LOJA = '" + _cLoja + "' " + CRLF
		_cQuery += CRLF
		_cQuery += " GROUP BY " + CRLF
		_cQuery += "	 PRDC.TIPO " + CRLF
		_cQuery += "	,CNB.CNB_PRODUT " + CRLF
		_cQuery += "	,'F' " + CRLF
		_cQuery += "	,UVCT.NUMERO " + CRLF
		_cQuery += "	,UVCT.REVISA " + CRLF
		_cQuery += CRLF
		_cQuery += " ORDER BY " + CRLF
		_cQuery += "	PRDC.TIPO, CNB_DATASS DESC " + CRLF
		
		// Preenche o array que será utilizado na função GV001X23 para a chamada do TcSetField.
		Aadd(_aSetField,{"CDUZAX","L",1,0})
		
		// Função para retornar o resultado de uma query em um array.
		_aProdCorp := aClone(U_GV001X23(_cQuery,,,.T.,_aSetField))
		If Len(_aProdCorp) < 2
			_aProdCorp := aClone(U_GV001X23(_cQuery,,,.T.,_aSetField))
		EndIf
	EndIf
	
	
	If !Empty(_aProdCorp)
		If Len(_aProdCorp) < 2
			_aProdCorp := {}
		Else 
			If _aProdCorp[1][1] == _aProdCorp[2][1]
				For nI := 2 To Len(_aProdCorp)
					If _aProdCorp[nI][1] == _aProdCorp[nI-1][1]
						If _aProdCorp[nI][7] > _aProdCorp[nI-1][7]
							Aadd(aProdFinal,_aProdCorp[nI])
						Else
							Aadd(aProdFinal,_aProdCorp[nI-1])
						EndIf
					ElseIf nI < Len(_aProdCorp)
						If !(_aProdCorp[nI][1] == _aProdCorp[nI+1][1])
							Aadd(aProdFinal,_aProdCorp[nI])
						EndIf
					Else
						Aadd(aProdFinal,_aProdCorp[nI])
					EndIf	
				Next	
				_aProdCorp := {}
				_aProdCorp := aProdFinal
			EndIf
		EndIf
	EndIf

	For Ni:= 1 to Len(_aProdCorp)
		_aProdCorp[ni,3] := cIndCli
	Next

Return !Empty(_aProdCorp)

/*/{Protheus.doc} _fVerIdCDU
Verifica o indice do CDU.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.

@Return logica, se encontrou o indice.
/*/

Static Function _fVerIdCDU(_cContrato,_cRevisao,_cCliente,_cLoja)

	Local _cQuery := ""
	Local _cAuxiliar := ""
	
	Local _nCount := 0
	Local _nCount2 := 0
	
	Local _lMaisCDU := .F.
	Local _lContinua := .F.
	
	Local _aIndZAX := {}
	Local cAuxQry	:= ""
	
	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""

	For _nCount := 1 To 2
		_cQuery := " SELECT ZAX.ZAX_PRCDU " + CRLF
		_cQuery += " FROM " + RetSqlName("ZAX") + " ZAX " + CRLF
		_cQuery += CRLF
		_cQuery += " WHERE ZAX.D_E_L_E_T_ = ' ' " + CRLF
		_cQuery += "	AND ZAX.ZAX_FILIAL = '" + xFilial("ZAX") + "' " + CRLF
		_cQuery += "	AND ZAX.ZAX_CONTRA = '" + _cContrato + "' " + CRLF
		_cQuery += "	AND ZAX.ZAX_REVISA = '" + _cRevisao + "' " + CRLF
		_cQuery += "	AND ZAX.ZAX_CLIENT = '" + _cCliente + "' " + CRLF
		_cQuery += "	AND ZAX.ZAX_LOJA = '" + _cLoja + "' " + CRLF
		_cQuery += "	AND ZAX.ZAX_STATUS <> 'I' " + CRLF
			
		For _nCount2 := 1 To Len(_aProdCorp) 
			If AllTrim(_aProdCorp[_nCount2][1]) == "1" .And. !(_aProdCorp[_nCount2][2] $ _cAuxiliar) // Produto de CDU
				_cAuxiliar += _aProdCorp[_nCount2][2] + "|"

				If _lMaisCDU
					cAuxQry += CRLF + "OR "
				EndIf
				
				cAuxQry += "ZAX.ZAX_PRCDU = '" + _aProdCorp[_nCount2][2] + "' "
				
				_lMaisCDU := .T.
			EndIf
		Next
			
		
		If !Empty(cAuxQry)
			_cQuery += "	AND("
			_cQuery += cAuxQry
			_cQuery += "	) " + CRLF
		EndIf
		_cQuery += "	FETCH FIRST 1 ROWS ONLY " + CRLF
		
		_aIndZAX := U_GV001X23(_cQuery)
		
		If Empty(_aIndZAX)
			// Incluido tratamento para gerar os indices ocnforme a tabela existente mas com o codigo do produto ativo nos itens de contrato
			// Apos inclusão volta no loop para carregar novamente a posição do codigo do produto CDU
			U_GV001X15(_cCliente,SubStr(_cSiteFat,1,TamSx3("ZTS_GRPFAT")[1]),SubStr(_cSiteFat,TamSx3("ZTS_GRPFAT")[1]+1,TamSx3("ZTS_FILFAT")[1]))
		Else
			_nCount2 := Ascan(_aProdCorp,{|x| AllTrim(x[2]) == AllTrim(_aIndZAX[1][1]) })
			
			If _nCount2 > 0
				_aProdCorp[_nCount2][4] := .T.
				_lContinua := .T.
			EndIf
			
			Exit
		EndIf
	Next

Return _lContinua

/*/{Protheus.doc} _fGetContr
Função para buscar a revisão do contrato gerada pela proposta.

<AUTHOR> Ribeiro
@since 05/11/2015

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
/*/

Static Function _fGetContr(_cCliente,_cLoja,_cContrato,_cRevisao)

	Local _cQuery := ""
	
	Local _aDados := {}
	
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cContrato := ""
	Default _cRevisao := ""
	
	_cQuery := " SELECT " + CRLF
	_cQuery += "	 CN9.CN9_NUMERO " + CRLF
	_cQuery += "	,CN9.CN9_REVISA " + CRLF
	_cQuery += " FROM " + RetSqlName("CN9") + " CN9 " + CRLF
	_cQuery += CRLF
	_cQuery += " INNER JOIN " + RetSqlName("CNC") + " CNC ON " + CRLF
	_cQuery += "	CNC.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND CNC.CNC_FILIAL = '" + xFilial("CNC") + "' " + CRLF
	_cQuery += "	AND CNC.CNC_NUMERO = CN9.CN9_NUMERO " + CRLF
	_cQuery += "	AND CNC.CNC_REVISA = CN9.CN9_REVISA " + CRLF
	_cQuery += "	AND CNC.CNC_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND CNC.CNC_LOJACL = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND CNC.CNC_TIPCLI = '01' " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	CN9.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND CN9.CN9_FILIAL = '" + xFilial("CNB") + "' " + CRLF
	_cQuery += "	AND CN9.CN9_SITUAC = '05' " + CRLF
	_cQuery += "	AND CN9.CN9_ESPCTR = '2' " + CRLF
	_cQuery += "	AND CN9.CN9_TPCTO = '013' " + CRLF
	
	
	_aDados := U_GV001X23(_cQuery)
	
	If !Empty(_aDados)
		_cContrato := PadR(_aDados[1][1],TamSx3("CN9_NUMERO")[1])
		_cRevisao := PadR(_aDados[1][2],TamSx3("CN9_REVISA")[1])
	EndIf

Return

/*/{Protheus.doc} _fAtualiza
Verifica o indice do CDU.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
/*/

Static function _fAtualiza(_cContrato,_cRevisao,_cCliente,_cLoja)

	Local _cAnoIni := ""
	
	Local _aAnosRef 	:= {} 
	Local _aAnoIni 	:= {}
	Local _aIndices 	:= {}
	
	Local _lValidAll	:= .T.
	
	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""

	// Função para inativar os indices não utilizados.
	_fInatiZAX(_cCliente,_cLoja,_cContrato,_cRevisao)
	
	// Função para buscar o ano inicial
	_cAnoIni := _fZAXInic(_cCliente,_cLoja,_cContrato,_cRevisao)
	
	// Função para retornar todos os anos desde o inicial até o corporativo.
	_aAnosRef := _fMakeAnoR(_cAnoIni)
	
	// Funcao para carregar os indices correspondentes ao contrato
	_aIndices := U_GV001X03(,_aProdCorp[1][3],_aAnosRef,SubStr(_cSiteFat,1,TamSx3("ZTS_GRPFAT")[1]),SubStr(_cSiteFat,TamSx3("ZTS_GRPFAT")[1]+1,TamSx3("ZTS_FILFAT")[1]))[1]
	
	// Função para atualizar as metricas do cliente (ZAL)
	_fAtualZAL(_cContrato,_cRevisao,_cCliente,_cLoja,_aAnosRef,_aIndices)
	
	// Função para buscar as informações do indice de um ano especifico.
	_aAnoIni := _fDadosAno(_cContrato,_cRevisao,_cCliente,_cLoja,_cAnoIni)
	
	If GetMv("CORP_VALID",,.T.)
		If !Empty(_cAnoIni) .And. !Empty(_aAnoIni)
			_fReajTInd(_cContrato,_cRevisao,_cCliente,_cLoja,_aAnosRef,_aAnoIni,_aIndices)
		Else
			MsgStop("Existem divergências entre o ano do primeiro CNPJ ativo no contrato e o ano da primeira tabela de índice no contrato. Favor Validar!")
			_lValidAll := .F.
		EndIf
		
	EndIf
Return _lValidAll

/*/{Protheus.doc} _fInatiZAX
Inativa os indices que não estão mais vinculados com o contrato.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
/*/

Static Function _fInatiZAX(_cCliente,_cLoja,_cContrato,_cRevisao)

	Local _cQuery := ""
	Local _cAuxiliar := ""
	
	Local _nCount := 0
	
	Local _lMaisCDU := .F.
	
	Local _aRecnoZAX := {}

	Default _cCliente := ""
	Default _cLoja := ""
	Default _cContrato := ""
	Default _cRevisao := ""

	_cQuery := " SELECT " + CRLF
	_cQuery += "	ZAX.R_E_C_N_O_ " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAX") + " ZAX " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	ZAX.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAX.ZAX_FILIAL = '" + xFilial("ZAX") + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_STATUS = 'A' " + CRLF
	_cQuery += "	AND "
	For _nCount := 1 To Len(_aProdCorp) 
		If AllTrim(_aProdCorp[_nCount][1]) == "1" .And. !(_aProdCorp[_nCount][2] $ _cAuxiliar) // Produto de CDU
			_cAuxiliar += _aProdCorp[_nCount][2] + "|"
			
			If _lMaisCDU
				_cQuery += CRLF + "	AND "
			EndIf
			
			_cQuery += "ZAX.ZAX_PRCDU <> '" + _aProdCorp[_nCount][2] + "' "
			
			_lMaisCDU := .T.
		EndIf
	Next
	
	_aRecnoZAX := U_GV001X23(_cQuery)
	
	For _nCount := 1 To Len(_aRecnoZAX)
		ZAX->(DbGoTo(_aRecnoZAX[_nCount][1]))
		RecLock("ZAX",.F.)
			ZAX->ZAX_STATUS := "I"
		ZAX->(MsUnLock())
	Next

Return

/*/{Protheus.doc} _fZAXInic
Busca o ano inicial do corporativo

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.

@Return caracter, retorna o ano inicial
/*/

Static Function _fZAXInic(_cCliente,_cLoja,_cContrato,_cRevisao)

	Local _cQuery := ""
	Local _cAnoIni := ""
	
	Local _nCount := 0
	Local _nCount2 := 0
	Local _nPosic := 0
	
	Local _aAnoIni := {}
	Local _aSetField := {}

	Default _cCliente := ""
	Default _cLoja := ""
	Default _cContrato := ""
	Default _cRevisao := ""
	
	For _nCount := 1 to Len(_aProdCorp)
		If 	_aProdCorp[_nCount][1] == "1"
			_cQuery := " SELECT DISTINCT " + CRLF
			_cQuery += "	 ZAX.ZAX_ANOREF " + CRLF
			_cQuery += "	,CASE WHEN ZAX.ZAX_INDCDU > 0 AND ZAX.ZAX_VLMCDU > 0 " + CRLF 
			_cQuery += "	 THEN 'T' ELSE 'F' END AS INDVLRCDU " + CRLF
			_cQuery += " FROM " + RetSqlName("ZAX") + " ZAX " + CRLF
			_cQuery += CRLF
			_cQuery += " WHERE " + CRLF
			_cQuery += "	ZAX.D_E_L_E_T_ = ' ' " + CRLF
			_cQuery += "	AND ZAX.ZAX_FILIAL = '" + xFilial("ZAX") + "'" + CRLF
			_cQuery += "	AND ZAX.ZAX_CONTRA = '" + _cContrato + "' " + CRLF
			_cQuery += "	AND ZAX.ZAX_REVISA = '" + _cRevisao + "' " + CRLF
			_cQuery += "	AND ZAX.ZAX_CLIENT = '" + _cCliente + "'" + CRLF
			_cQuery += "	AND ZAX.ZAX_LOJA = '" + _cLoja + "'" + CRLF
			_cQuery += "	AND ZAX.ZAX_PRCDU = '" + _aProdCorp[_nCount][2] + "'" + CRLF
			_cQuery += "	AND ZAX.ZAX_ANOREF < '" + _cAnoCorp + "' " + CRLF
			_cQuery += "	AND ZAX.ZAX_STATUS <> 'I' " + CRLF
			_cQuery += CRLF
			_cQuery += " ORDER BY ZAX_ANOREF DESC" + CRLF
	
			// Preenche o array que será utilizado na função TDIFUN01 para a chamada do TcSetField.
			Aadd(_aSetField,{"INDVLRCDU","L",1,0})
			
			// Função para retornar o resultado de uma query em um array.
			_aAnoIni := U_GV001X23(_cQuery,,,.T.,_aSetField)
			
			If Empty(_aAnoIni)
				_aProdCorp[_nCount][4] := .F.
			Else
				For _nCount2 := 1 To Len(_aAnoIni)
					If _aAnoIni[_nCount2][2]
						_cAnoIni := _aAnoIni[_nCount2][1]
					Else
						Exit
					EndIf
				Next
				If !Empty(_cAnoIni)
					Exit
				EndIf
			EndIf
		EndIf
	Next
	
	_nPosic := Ascan(_aProdCorp,{|x| x[4] == .T.})
	
	If Empty(_cAnoIni) .And. _nPosic > 0
		_cQuery := " SELECT DISTINCT ZAL_ANOREF "
		_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL "
		_cQuery += CRLF
		_cQuery += " INNER JOIN "+ RetSqlName("ZAX") +" ZAX ON "
		_cQuery += "	ZAX.D_E_L_E_T_ = ' ' "
		_cQuery += "	AND ZAX.ZAX_FILIAL = '" + xFilial("ZAX") + "'"
		_cQuery += "	AND ZAX.ZAX_CONTRA = ZAL.ZAL_CONTRA "
		_cQuery += "	AND ZAX.ZAX_REVISA = ZAL.ZAL_REVISA "
		_cQuery += "	AND ZAX.ZAX_CLIENT = ZAL.ZAL_CLIENT "
		_cQuery += "	AND ZAX.ZAX_LOJA = ZAL.ZAL_LOJA "
		_cQuery += "	AND ZAX.ZAX_ANOREF = ZAL.ZAL_ANOREF "
		_cQuery += "	AND ZAX.ZAX_PRCDU = '" + _aProdCorp[_nPosic][2] + "' "
		_cQuery += "	AND ZAX.ZAX_STATUS <> 'I' "
		_cQuery += "	AND ZAX.ZAX_INDCDU > 0 "
		_cQuery += "	AND ZAX.ZAX_VLMCDU > 0 "
		_cQuery += CRLF
		_cQuery += " WHERE"
		_cQuery += "	ZAL.D_E_L_E_T_ = ' ' "
		_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' "
		_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' "
		_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' "
		_cQuery += "	AND ZAL.ZAL_STATUS = 'A' "
		_cQuery += CRLF
		_cQuery += " ORDER BY ZAL_ANOREF "
		_cQuery += " FETCH FIRST 1 ROWS ONLY "
	
		_aAnoIni := U_GV001X23(_cQuery)
	
		If !Empty(_aAnoIni)
			_cAnoIni := _aAnoIni[1][1]
		EndIf
	Endif
	
	If Empty(_cAnoIni)
		_cQuery := "SELECT DISTINCT ZAL_ANOREF "
		_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL "
		_cQuery += CRLF
		_cQuery += " WHERE "
		_cQuery += "	ZAL.D_E_L_E_T_ = ' ' "
		_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' "
		_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' "
		_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' "
		_cQuery += "	AND ZAL.ZAL_STATUS = 'A' "
		_cQuery += CRLF
		_cQuery += " ORDER BY ZAL_ANOREF "
	
		_aAnoIni := U_GV001X23(_cQuery)
	
		If !Empty(_aAnoIni)
			_cAnoIni := _aAnoIni[1][1]
		EndIf
	Endif

Return _cAnoIni

/*/{Protheus.doc} _fMakeAnoR
Função para retornar todos os anos do inicio até o ano corporativo.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cAnoIni, caracter, ano inicial.

@Return array, informações do ano
/*/

Static Function _fMakeAnoR(_cAnoIni)

	Local _aAnosRef := {} 

	If !Empty(_cAnoIni)
		While _cAnoIni <= _cAnoCorp
			Aadd(_aAnosRef,_cAnoIni )
			_cAnoIni := Soma1(_cAnoIni)
		Enddo
	Endif

Return _aAnosRef

/*/{Protheus.doc} _fAtualZAL
Função para atualizar a ZAL.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _aAnosRef, array, contem todos os anos.
@Param _aIndices, array, contem os indices de reajuste do corporativo
/*/

Static Function _fAtualZAL(_cContrato,_cRevisao,_cCliente,_cLoja,_aAnosRef,_aIndices)

	Local _nMetrica := 0
	Local _nPosic := 0
	
	Local _aMtrMaior := {}

	Local _lMaiorMtr := GetMv("MV_CPMAIOR",,.T.)
	
	Local _cIndice := ""
	
	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""

	Default _aAnosRef := {}
	Default _aIndices := {}

	_cIndice := _aProdCorp[1][3]
	
	ZAL->(DbSetOrder(5))	// ZAL_FILIAL+ZAL_CONTRA+ZAL_REVISA+ZAL_CLIENT+ZAL_LOJA
	If ZAL->(DbSeek(xFilial("ZAL") + _cContrato + _cRevisao + _cCliente + _cLoja))
		
		While ZAL->(!Eof()) .And. xFilial("ZAL") + _cContrato + _cRevisao + _cCliente + _cLoja == ZAL->(ZAL_FILIAL + ZAL_CONTRA + ZAL_REVISA + ZAL_CLIENT + ZAL_LOJA)
			If ZAL->ZAL_ANOREF < _cAnoCorp .And. ZAL->ZAL_STATUS == "A"
				If _lMaiorMtr
					_aMtrMaior := _fMetriAno(_cContrato,_cRevisao,_cCliente,_cLoja)
					
					_nPosic := Ascan(_aMtrMaior,{|x| AllTrim(x[1]) == AllTrim(ZAL->ZAL_ANOREF)})
						 
					If _nPosic > 0
						_nMetrica := If(AllTrim(_aMtrMaior[_nPosic][4]) == "D",ZAL->ZAL_MTRDIP,ZAL->ZAL_MTRINF)
					EndIf
				Else
					_nMetrica := If(!Empty(ZAL->ZAL_MTRDIP),ZAL->ZAL_MTRDIP,ZAL->ZAL_MTRINF)
				EndIf
				
				RecLock("ZAL",.F.)
					ZAL->ZAL_INDICE := _cIndice
					ZAL->ZAL_ANOREA := _cAnoCorp
					If !Empty(_nMetrica)
						ZAL->ZAL_MTRREA := U_GV001X01(_nMetrica,ZAL->ZAL_ANOREF,_cAnoCorp,_aIndices,,,,_cCliente,_cLoja)
					EndIf
				ZAL->(MsUnLock())
			EndIf
			ZAL->(DbSkip())
		EndDo
	EndIf

Return

/*/{Protheus.doc} _fMetriAno
Função para retornar as metricas referente a cada ano.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.

@Return array, contem as informações das metricas de cada ano.
/*/

Static Function _fMetriAno(_cContrato,_cRevisao,_cCliente,_cLoja)

	Local _cQuery := ""
	
	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	
	_cQuery := " SELECT " + CRLF
	_cQuery += "	 ANOREF " + CRLF
	_cQuery += "	,INFORME " + CRLF
	_cQuery += "	,DIFERENCA " + CRLF
	_cQuery += "	,CASE WHEN INFORME < DIFERENCA THEN 'D' " + CRLF
	_cQuery += "	 ELSE 'I' END MAIOR " + CRLF
	_cQuery += " FROM ( " + CRLF
	_cQuery += "	 SELECT " + CRLF
	_cQuery += "		 ZAL.ZAL_ANOREF ANOREF " + CRLF
	_cQuery += "		,SUM(ZAL.ZAL_MTRINF) INFORME " + CRLF
	_cQuery += "		,SUM(ZAL.ZAL_MTRDIP) DIFERENCA " + CRLF
	_cQuery += "	 FROM " + RetSqlName("ZAL") + " ZAL " + CRLF 
	_cQuery += "	 WHERE ZAL.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "		AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
	_cQuery += "		AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "		AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "		AND ZAL.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "		AND ZAL.ZAL_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "		AND ZAL.ZAL_STATUS = 'A' " + CRLF 
	_cQuery += "	 GROUP BY " + CRLF
	_cQuery += "		 ZAL.ZAL_ANOREF " + CRLF
	_cQuery += " ) " + CRLF
	_cQuery += " ORDER BY " + CRLF
	_cQuery += "	 ANOREF " + CRLF

Return U_GV001X23(_cQuery)

/*/{Protheus.doc} _fDadosAno
Função para buscar as informações do indice de um ano especifico.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cAnoRef, caracter, ano do corporativo.

@Return array, contem as informações do indice do ano.
/*/

Static Function _fDadosAno(_cContrato,_cRevisao,_cCliente,_cLoja,_cAnoRef)

	Local _cQuery := ""
	
	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cAnoRef := ""
	
	_cQuery := " SELECT " + CRLF
	_cQuery += "	 ZAX.ZAX_ANOREF " + CRLF
	_cQuery += "	,ZAX.ZAX_VLMCDU " + CRLF
	_cQuery += "	,ZAX.ZAX_VLMET " + CRLF
	_cQuery += "	,ZAX.ZAX_VLMAR " + CRLF
	_cQuery += "	,ZAX.ZAX_REFMT1 " + CRLF
	_cQuery += "	,ZAX.ZAX_REFMT2 " + CRLF
	_cQuery += "	,ZAX.ZAX_INDCDU " + CRLF
	_cQuery += "	,ZAX.ZAX_INDET " + CRLF
	_cQuery += "	,ZAX.ZAX_INDAR " + CRLF
	_cQuery += "	,ZAX.R_E_C_N_O_ " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAX") + " ZAX " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	ZAX.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAX.ZAX_FILIAL = '" + xFilial("ZAX") + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_ANOREF = '" + _cAnoRef + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_STATUS <> 'I' " + CRLF

Return U_GV001X23(_cQuery)

/*/{Protheus.doc} _fReajTInd
Função para reajustar os indices.

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _aAnosRef, caracter, ano do corporativo.
@Param _aAnoIni, array, contem todos os anos.
@Param _aIndices, array, contem os indices de reajuste do corporativo.
/*/

Static Function _fReajTInd(_cContrato,_cRevisao,_cCliente,_cLoja,_aAnosRef,_aAnoIni,_aIndices)

	Local _nCount := 0
	Local _nCount2 := 0
	Local _nCount3 := 0
	Local _nItem := 0
	Local _nVlrAntMT := 0
	
	Local _bCampo := {|_nCpo| Field(_nCpo)}

	Default _cCliente := ""
	Default _cLoja := ""
	
	Default _aAnosRef := {}
	Default _aAnoIni := {}
	
	ZAX->(DbSetOrder(4))	// ZAX_FILIAL+ZAX_CONTRA+ZAX_REVISA+ZAX_ANOREF+ZAX_PRCDU+ZAX_ITEM

	For _nCount := 1 to Len(_aAnosRef)
		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³Atualiza os campos a serem reajustados³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
		// Inicia a partir do 2a.ano de metrica os reajustes dos indices conforme regra
		If _nCount > 1 .And. _aAnosRef[_nCount] >= _aAnoIni[1][1]
			If ZAX->(DbSeek(xFilial("ZAX") + _cContrato + _cRevisao + _aAnosRef[_nCount]))
				_nItem := 0
				_nVlrAntMT	:= 0.00

				While ZAX->(!Eof()) .And. xFilial("ZAX") + _cContrato + _cRevisao + _aAnosRef[_nCount] == ZAX->(ZAX_FILIAL + ZAX_CONTRA + ZAX_REVISA + ZAX_ANOREF)
					If ZAX->ZAX_STATUS == "I"
						ZAX->(dbSkip())
						Loop
					EndIf
					//1 = CDU ; 2 = ET ; 3 = AR ; 4 = SMS ; 5 = CDU Adicional ; 6 = ET Adicional ; 7 = AR Adicional ; 8 = SMS Adicional
					If	Ascan(_aProdCorp,{|x| AllTrim(x[1]) == "1" .And. x[2] == ZAX->ZAX_PRCDU }) > 0
						_nItem ++
						If _nItem > Len(_aAnoIni)
							Exit
						Endif
						
						RecLock("ZAX",.F.)
							ZAX->ZAX_VLMCDU := U_GV001X02(_aAnoIni[_nItem][2],ZAX->ZAX_ANOREF,_aAnoIni[_nItem][1],_aIndices)
							ZAX->ZAX_VLMET  := U_GV001X02(_aAnoIni[_nItem][3],ZAX->ZAX_ANOREF,_aAnoIni[_nItem][1],_aIndices)
							ZAX->ZAX_VLMAR  := U_GV001X02(_aAnoIni[_nItem][4],ZAX->ZAX_ANOREF,_aAnoIni[_nItem][1],_aIndices)
							ZAX->ZAX_ORIGEM := "A"
							ZAX->ZAX_AUTMAN := "A"
							ZAX->ZAX_STATUS := "A"
	
							If ZTS->ZTS_TIPREC == "1"	// Receita Bruta
								ZAX->ZAX_INDCDU := _aAnoIni [_nItem][7]
								ZAX->ZAX_INDET  := _aAnoIni [_nItem][8]
								ZAX->ZAX_INDAR  := _aAnoIni [_nItem][9]
	
								// Reajusta primeiro o valor "ate" para servidor como "de" + 0,01 para proxima faixa
								If _nItem < Len(_aAnoIni)
									ZAX->ZAX_REFMT2 := U_GV001X02(_aAnoIni[_nItem][6],ZAX->ZAX_ANOREF,_aAnoIni[_nItem][1],_aIndices)
								Else
									ZAX->ZAX_REFMT2 := _aAnoIni[_nItem][6]
								Endif
	
								// Reajusta o valor "De"
								If _nItem > 1 .And. _nVlrAntMT > 0
									ZAX->ZAX_REFMT1 := _nVlrAntMT + 0.01
								Endif
								_nVlrAntMT := ZAX->ZAX_REFMT2
	
							Else	// Receita Especifica
								ZAX->ZAX_INDCDU := U_GV001X02(_aAnoIni[_nItem][7],ZAX->ZAX_ANOREF,_aAnoIni[_nItem][1],_aIndices)
								ZAX->ZAX_INDET := U_GV001X02(_aAnoIni[_nItem][8],ZAX->ZAX_ANOREF,_aAnoIni[_nItem][1],_aIndices)
								ZAX->ZAX_INDAR := U_GV001X02(_aAnoIni[_nItem][9],ZAX->ZAX_ANOREF,_aAnoIni[_nItem][1],_aIndices)
							Endif
						ZAX->(MsUnLock())

					Else
						RecLock("ZAX",.F.)
							ZAX->ZAX_STATUS := "I"
						ZAX->(MsUnLock())
					Endif
					ZAX->(DbSkip())
				Enddo

				// Caso nao tenha nenhum item ativo gerar os valores dos indices do ano de correspondente
				If _nItem == 0
					_nVlrAntMT := 0.00
					For _nCount2 := 1 to Len(_aAnoIni)
						//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
						//³Carrega em variavel de memoria o 1o.indice da tabela ZAX       ³
						//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
						ZAX->(DbGoTo(_aAnoIni[_nCount2][10]))
						RegToMemory("ZAX")

						//1 = CDU ; 2 = ET ; 3 = AR ; 4 = SMS ; 5 = CDU Adicional ; 6 = ET Adicional ; 7 = AR Adicional ; 8 = SMS Adicional
						If	Ascan(_aProdCorp,{|x| AllTrim(x[1]) == "1" .And. x[2] == ZAX->ZAX_PRCDU }) > 0

							//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
							//³Grava a tabela de Indice do Ano correspondente a sequencia atual ³
							//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
							RecLock("ZAX",.T.)
								For _nCount3 := 1 TO ZAX->(FCount())
									FieldPut(_nCount3,M->&(EVAL(_bCampo,_nCount3)))
								Next
	
								ZAX->ZAX_ANOREF := _aAnosRef[_nCount]
								ZAX->ZAX_VLMCDU := U_GV001X02(_aAnoIni[_nCount2][2],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
								ZAX->ZAX_VLMET	:= U_GV001X02(_aAnoIni[_nCount2][3],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
								ZAX->ZAX_VLMAR  := U_GV001X02(_aAnoIni[_nCount2][4],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
								ZAX->ZAX_AUTMAN := "A"
								ZAX->ZAX_ORIGEM := "A"
								ZAX->ZAX_STATUS := "A"
	
								If ZTS->ZTS_TIPREC == "1"	// Receita Bruta
									// Reajusta primeiro o valor "ate" para servidor como "de" + 0,01 para proxima faixa
									If _nCount2 < Len(_aAnoIni)
										ZAX->ZAX_REFMT2 := U_GV001X02(_aAnoIni[_nCount2][6],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
									Else
										ZAX->ZAX_REFMT2 := _aAnoIni[_nCount2][6]
									Endif
	
									// Reajusta o valor "De"
									If _nCount2 > 1 .And. _nVlrAntMT > 0
										ZAX->ZAX_REFMT1 := _nVlrAntMT + 0.01
									EndIf
									
									_nVlrAntMT	 := ZAX->ZAX_REFMT2
								Else	// Receita Especifica
									ZAX->ZAX_INDCDU := U_GV001X02(_aAnoIni[_nCount2][7],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
									ZAX->ZAX_INDET := U_GV001X02(_aAnoIni[_nCount2][8],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
									ZAX->ZAX_INDAR := U_GV001X02(_aAnoIni[_nCount2][9],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
								Endif
							ZAX->(MsUnLock())
						Endif
					Next
				Endif
			Else
				_nVlrAntMT := 0.00
				For _nCount2 := 1 to Len(_aAnoIni)
					//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
					//³Carrega em variavel de memoria o 1o.indice da tabela ZAX       ³
					//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
					ZAX->(DbGoTo(_aAnoIni[_nCount2][10]))

					RegToMemory("ZAX")
					//1 = CDU ; 2 = ET ; 3 = AR ; 4 = SMS ; 5 = CDU Adicional ; 6 = ET Adicional ; 7 = AR Adicional ; 8 = SMS Adicional
					If	Ascan(_aProdCorp,{|x| AllTrim(x[1]) == "1" .And. x[2] == ZAX->ZAX_PRCDU }) > 0
						//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
						//³Grava a tabela de Indice do Ano correspondente a sequencia atual ³
						//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
						RecLock( "ZAX" , .T. )
						For _nCount3 := 1 TO ZAX->(FCount())
							FieldPut(_nCount3,M->&(EVAL(_bCampo,_nCount3)))
						Next

						ZAX->ZAX_ANOREF := _aAnosRef[_nCount]
						ZAX->ZAX_VLMCDU := U_GV001X02( _aAnoIni[_nCount2][2],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
						ZAX->ZAX_VLMET := U_GV001X02( _aAnoIni[_nCount2][3],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
						ZAX->ZAX_VLMAR := U_GV001X02( _aAnoIni[_nCount2][4],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
						ZAX->ZAX_AUTMAN := "A"
						ZAX->ZAX_ORIGEM := "A"
						ZAX->ZAX_STATUS := "A"

						If ZTS->ZTS_TIPREC == "1"	// Receita Bruta
							// Reajusta primeiro o valor "ate" para servidor como "de" + 0,01 para proxima faixa
							If _nCount2 < Len(_aAnoIni)
								ZAX->ZAX_REFMT2 := U_GV001X02(_aAnoIni[_nCount2][6],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
							Else
								ZAX->ZAX_REFMT2 := _aAnoIni[_nCount2][6]
							EndIf

							// Reajusta o valor "De"
							If _nCount2 > 1 .And. _nVlrAntMT > 0
								ZAX->ZAX_REFMT1 := _nVlrAntMT + 0.01
							EndIf
							_nVlrAntMT := ZAX->ZAX_REFMT2
						Else	// Receita Especifica
							ZAX->ZAX_INDCDU := U_GV001X02(_aAnoIni[_nCount2][7],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
							ZAX->ZAX_INDET := U_GV001X02(_aAnoIni[_nCount2][8],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
							ZAX->ZAX_INDAR := U_GV001X02(_aAnoIni[_nCount2][9],_aAnosRef[_nCount],_aAnoIni[_nCount2][1],_aIndices)
						Endif
						ZAX->(MsUnLock())
					Endif
				Next
			Endif
		Endif
	Next

Return

/*/{Protheus.doc} _fCalcula
Função para realizar o calculo do corporativo. 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cTipCalc, caracter, qual o tipo de calculo (N=Normal;R=Revisao;I=Inclusao Cnpj;E=Exclusao Cnpj).
@Param _cCalculo, caracter, qual calculo (I=Incremento;D=Diferenca).
@Param _cMulta, caracter, se gera multa ou não.
@Param _nVlrDesc, numerico, valor de desconto. Variavel utilizada para o calculo de inclusão e exclusão de CNPJ.
@Param _cMensCalc, caracter, mensagem no calculo.
/*/

Static Function _fCalcula(_cContrato,_cRevisao,_cCliente,_cLoja,_cTipCalc,_cCalculo,_cMulta,_nVlrDesc,_cMensCalc)

	Local _nOutroVlr := 0
	
	Private _aIndCalc := {}
	Private _aIndHist := {}
	Private _aDadosPH0 := {}
	Private _aDadosPH1 := {}
	Private _aDadosPH2 := {}
	
	Private _nMetInfor := 0
	Private _nMaiorMet := 0
	Private _nCalcCDU := 0
	Private _nVlrCDU := 0
	Private _nCalcHist := 0
	Private _nVlrHist := 0
	Private _nCalcSMS := 0
	Private _nVlrSMS := 0
	Private _nCalcET := 0
	Private _nVlrET := 0
	Private _nCalcAR := 0
	Private _nVlrAR := 0
	Private _nVlrCob := 0

	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cTipCalc := ""
	Default _cCalculo := ""
	Default _cMulta := "N"
	Default _cMensCalc := ""
	
	Default _nVlrDesc := 0

	#Define _NINDCDU 1
	#Define _NMINCDU 2
	#Define _NINDET 3
	#Define _NMINET 4
	#Define _NINDAR 5
	#Define _NMINAR 6
	
	ZTS->(DbSeek(xFilial("ZTS")+_cCliente+_cLoja))
	// Busca a métrica informada
	_nMetInfor := _fMetInfor(_cContrato,_cRevisao,_cCliente,_cLoja,_cTipCalc,_cCalculo,@_cMulta)
	
	If _nMetInfor == 0
		MsgStop("Não foi encontrado valor de métrica para cálculo")
		_cMsgErro := "Não foi encontrado valor de métrica para cálculo"
		Return
	EndIf
	
	// Busca o indice para o calculo
	_aIndCalc := _fIndicMet(_cContrato,_cRevisao,_cCliente,_cLoja,_nMetInfor)
	
	// Realiza os calculos conforme a receita.
	If !Empty(_aIndCalc)
		If ZTS->ZTS_TIPREC == "2"
			_nCalcCDU := _nMetInfor*_aIndCalc[_NINDCDU]
			_nCalcSMS := _nMetInfor*_aIndCalc[_NINDET]
			_nCalcET := _nMetInfor*_aIndCalc[_NINDET]
			_nCalcAR := _nMetInfor*_aIndCalc[_NINDAR]
		Else
			_nCalcCDU := _nMetInfor*(_aIndCalc[_NINDCDU]/100)
			_nCalcSMS := _nMetInfor*(_aIndCalc[_NINDET]/100)
			_nCalcET := _nMetInfor*(_aIndCalc[_NINDET]/100)
			_nCalcAR := _nMetInfor*(_aIndCalc[_NINDAR]/100)
		EndIf
	Else
		MsgStop("Não foram encontrados índices para cálculo")
		_cMsgErro := "Não foram encontrados índices para cálculo"
		Return
	EndIf
	
	// Se o valor do calculo for menor ou igual que o valor minimo, considera o valor minimo. 
	If _nCalcCDU <= _aIndCalc[_NMINCDU]
		_nVlrCDU := _aIndCalc[_NMINCDU]
	Else
		_nVlrCDU := _nCalcCDU
	EndIf 
	
	If _nCalcSMS <= _aIndCalc[_NMINET]
		_nVlrSMS := _aIndCalc[_NMINET]
	Else
		_nVlrSMS := _nCalcSMS
	EndIf 

	If _nCalcET <= _aIndCalc[_NMINET]
		_nVlrET := _aIndCalc[_NMINET]
	Else
		_nVlrET := _nCalcET
	EndIf 

	If _nCalcAR <= _aIndCalc[_NMINAR]
		_nVlrAR := _aIndCalc[_NMINAR]
	Else
		_nVlrAR := _nCalcAR
	EndIf 

	// Busca a maior métrica histórica
	_nMaiorMet := _fMaiorMet(_cContrato,_cRevisao,_cCliente,_cLoja)
	
	// Busca o indice para o calculo
	_aIndHist := _fIndicMet(_cContrato,_cRevisao,_cCliente,_cLoja,_nMaiorMet)

	If Len(_aIndHist) > 0
		// Realiza os calculos conforme a receita.
		If ZTS->ZTS_TIPREC == "2"
			_nCalcHist := _nMaiorMet*_aIndHist[_NINDCDU]
		Else
			_nCalcHist := _nMaiorMet*(_aIndHist[_NINDCDU]/100)
		EndIf
	
		// Se o valor do calculo menor ou igual que o valor minimo, considera o valor minimo. 
		If _nCalcHist <= _aIndHist[_NMINCDU]
			_nVlrHist := _aIndHist[_NMINCDU]
		Else
			_nVlrHist := _nCalcHist
		EndIf
	Else
		MsgStop("Não foram encontrados índices para cálculo")
		_cMsgErro := "Não foram encontrados índices para cálculo"
		Return
	EndIf
	
	
	_nOutroVlr := _fOutroVlr(_cCliente,_cLoja,_cAnoCorp)
	
	// Valor a ser pago
	_nVlrCob := _nVlrCDU
	
	// Desconta o valor pago historicamente
	_nVlrCob -= _nVlrHist
	
	// Desconta o valor já pago no ano
	_nVlrCob -= _nOutroVlr
	
	// Desconta o valor passado no calculo de inclusão ou exclusão de CNPJ's.
	_nVlrCob -= _nVlrDesc
	
	If Empty(_cMensCalc)
		_cMensCalc := "[" + Upper(X3Combo("PH0_TIPCAL",_cTipCalc)) + "] " + STR0020 + Lower(X3Combo("PH0_CALC",_cCalculo)) // "Calculo de "
		If _cMulta == "S"
			_cMensCalc += STR0021 + Lower(X3Combo("PH0_MULTA",_cMulta))
		EndIf	
		_cMensCalc += STR0022
	Else
		_cMensCalc := "[" + Upper(X3Combo("PH0_TIPCAL",_cTipCalc)) + "][" + _cMensCalc + "]"
	EndIf
	If _nVlrCob < 1
		_nVlrCob := 0.01
	EndIf
		

	// Monta os dados do cabeçalho do calculo
	_fDadosPH0(_cContrato,_cRevisao,_cCliente,_cLoja,_cTipCalc,_cCalculo,_cMulta,_cMensCalc,_nVlrCob)
	
	// Monta os dados dos itens do calculo
	_fDadosPH1()
	
	// Monta os dados da memória de calculo
	_fDadosPH2(_cCliente,_cLoja,_cTipCalc,_cCalculo)
	
	// Grava o calculo
	_fGravCalc(_cCliente,_cLoja)
	
Return

/*/{Protheus.doc} _fMetInfor
Função para buscar a metrica informada. 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cTipCalc, caracter, qual o tipo de calculo (N=Normal;R=Revisao;I=Inclusao Cnpj;E=Exclusao Cnpj).
@Param _cCalculo, caracter, qual calculo (I=Incremento;D=Diferenca).
@Param _cMulta, caracter, se gera multa ou não.

@Return numerico, retorna a metrica informada.
/*/

Static Function _fMetInfor(_cContrato,_cRevisao,_cCliente,_cLoja,_cTipCalc,_cCalculo,_cMulta)

	Local _cQuery := ""
	
	Local _aDados := {}
	
	Local _nMetInfor := 0
	Local _nMaiorMet := 0

	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cTipCalc := ""
	Default _cCalculo := ""
	Default _cMulta := "N"
	
	// Se não for multa, busca a metrica.
	If _cMulta <> "S"
		_cQuery := " SELECT " + CRLF
		If _cTipCalc == "N" .And. _cCalculo == "I"
			_cQuery += "	SUM(ZAL.ZAL_MTRINF) ZAL_MTRINF " + CRLF
		ElseIf _cTipCalc == "N" .And. _cCalculo == "D"
			_cQuery += "	SUM(ZAL.ZAL_MTRDIP) ZAL_MTRDIP " + CRLF
		ElseIf _cTipCalc == "R" .And. _cCalculo == "I"
			_cQuery += "	SUM(ZAL.ZAL_MTINFR) ZAL_MTINFR " + CRLF
		ElseIf _cTipCalc == "R" .And. _cCalculo == "D"
			_cQuery += "	SUM(ZAL.ZAL_MTDIFR) ZAL_MTDIFR " + CRLF
		ElseIf _cTipCalc $ "IE"
			_cQuery += "	 SUM(CASE " + CRLF
			_cQuery += "		WHEN ZAL.ZAL_MTDIFR <> 0 THEN ZAL.ZAL_MTDIFR " + CRLF
			_cQuery += "		WHEN ZAL.ZAL_MTRDIP <> 0 THEN ZAL.ZAL_MTRDIP " + CRLF
			_cQuery += "		WHEN ZAL.ZAL_MTINFR <> 0 THEN ZAL.ZAL_MTINFR " + CRLF
			_cQuery += "		WHEN ZAL.ZAL_MTRINF <> 0 THEN ZAL.ZAL_MTRINF " + CRLF
			_cQuery += "	 END) AS ZAL_MTDIFR " + CRLF
		EndIf
		_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL " + CRLF
		_cQuery += CRLF
		_cQuery += " WHERE " + CRLF
		_cQuery += "	ZAL.D_E_L_E_T_ = ' ' " + CRLF
		_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_ANOREF = '" + _cAnoCorp + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_STATUS <> 'I' " + CRLF
		
		_aDados := U_GV001X23(_cQuery,,,.T.)
	
		If !Empty(_aDados)

			_nMetInfor := _aDados[1][1]

		EndIf
	EndIf
	
	// Se não existe metrica e a data é superior a data limite de apresentação, ou se foi multa obrigatório, aplica a multa.
	If _cTipCalc == "N" .And. ((Empty(_nMetInfor) .And. ZTS->ZTS_DATA1 < dDataBase) .Or. _cMulta == "S")
		// Busca a maior métrica histórica
		_nMaiorMet := _fMaiorMet(_cContrato,_cRevisao,_cCliente,_cLoja)
		
		// Calcula a métrica de multa
		If _cCalculo == "I"
			_nMetInfor := _nMaiorMet+((_nMaiorMet/100)*ZTS->ZTS_MULTA1)
		Else
			_nMetInfor := _nMaiorMet+((_nMaiorMet/100)*ZTS->ZTS_MULTA2)
		EndIf
		
		_fAlterZAL(_cContrato,_cRevisao,_cCliente,_cLoja,_cTipCalc,_cCalculo,_nMetInfor)
		
		_cMulta := "S"
	Else
		_cMulta := "N"
	EndIf

Return _nMetInfor

/*/{Protheus.doc} _fAlterZAL
Função para incluir a metrica na ZAL. 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cTipCalc, caracter, qual o tipo de calculo (N=Normal;R=Revisao;I=Inclusao Cnpj;E=Exclusao Cnpj).
@Param _cCalculo, caracter, qual calculo (I=Incremento;D=Diferenca).
@Param _nMetInfor, numerico, metrica informada.
/*/

Static Function _fAlterZAL(_cContrato,_cRevisao,_cCliente,_cLoja,_cTipCalc,_cCalculo,_nMetInfor)

	Local _cQuery := ""
	
	Local _aDados := {}
	
	Local _nCount := 0
	
	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cTipCalc := ""
	Default _cCalculo := ""
	
	Default _nMetInfor := 0
	
	_cQuery := " SELECT ZAL.R_E_C_N_O_ " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	ZAL.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_ANOREF = '" + _cAnoCorp + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_STATUS <> 'I' " + CRLF
	
	_aDados := U_GV001X23(_cQuery,,,.T.)
	
	For _nCount := 1 To Len(_aDados)
		ZAL->(DbGoTo(_aDados[_nCount][1]))
		RecLock("ZAL",.F.)
			If _nCount == 1
				If _cCalculo == "I" 
					ZAL->ZAL_MTRINF := _nMetInfor
					ZAL->ZAL_DTMINF := dDataBase
				Else
					ZAL->ZAL_MTRDIP := _nMetInfor
					ZAL->ZAL_DTDIPJ := dDataBase
				EndIf
			Else
				If _cCalculo == "I" 
					ZAL->ZAL_MTRINF := 0
				Else
					ZAL->ZAL_MTRDIP := 0
				EndIf
			EndIf
			ZAL->ZAL_ORIGEM := IIf(_cCalculo=="I","U","V")
			ZAL->ZAL_PREVIS := "2"
		ZAL->(MsUnLock())
	Next

Return

/*/{Protheus.doc} _fMaiorMet
Função para buscar a maior metrica historica. Se não existir metrica historica retorna a metrica inicial. 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cContrato, caracter, codigo do contrato.
@Param _cRevisao, caracter, codigo da revisao do contrato.
@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.

@Return numerico, retorna a maior metrica historica ou a metrica inicial.
/*/

Static Function _fMaiorMet(_cContrato,_cRevisao,_cCliente,_cLoja)

	Local _cQuery := ""
	
	Local _aDados := {}
	
	Local _nMaiorMet := 0
	
	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	
	// Busca a maior metrica historica
	_cQuery := " SELECT " + CRLF
	_cQuery += "	 SUM(ZAL.ZAL_MTRREA) ZAL_MTRREA " + CRLF
	_cQuery += "	,ZAL.ZAL_ANOREF " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	ZAL.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_ANOREF < '" + _cAnoCorp + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_STATUS <> 'I' " + CRLF 
	_cQuery += CRLF
	_cQuery += " GROUP BY " + CRLF
	_cQuery += "	 ZAL.ZAL_ANOREF " + CRLF
	_cQuery += CRLF
	_cQuery += " ORDER BY " + CRLF
	_cQuery += "	 ZAL_MTRREA DESC " + CRLF
	_cQuery += CRLF
	_cQuery += " FETCH FIRST 1 ROWS ONLY " + CRLF
	
	_aDados := U_GV001X23(_cQuery,,,.T.)
	
	// Se não encontrou a maior metrica historica, busca a metrica inicial.
	If Empty(_aDados)
		_cQuery := " SELECT " + CRLF
		_cQuery += "	SUM(ZAL.ZAL_MTRINI) ZAL_MTRINI " + CRLF
		_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL " + CRLF
		_cQuery += CRLF
		_cQuery += " WHERE " + CRLF
		_cQuery += "	ZAL.D_E_L_E_T_ = ' ' " + CRLF
		_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_ANOREF = '" + _cAnoCorp + "' " + CRLF
		_cQuery += "	AND ZAL.ZAL_STATUS <> 'I' " + CRLF 

		_aDados := U_GV001X23(_cQuery,,,.T.)
	Else
		If _aDados[1,1] == 0
			_cQuery := " SELECT " + CRLF
			_cQuery += "	SUM(ZAL.ZAL_MTRINI) ZAL_MTRINI " + CRLF
			_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL " + CRLF
			_cQuery += " WHERE " + CRLF
			_cQuery += "	ZAL.D_E_L_E_T_ = ' ' " + CRLF
			_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
			_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
			_cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisao + "' " + CRLF
			_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
			_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' " + CRLF
			_cQuery += "	AND ZAL.ZAL_STATUS <> 'I' " + CRLF 
			_cQuery += "	AND ZAL.ZAL_ANOREF = "
			_cQuery += "	(SELECT MIN(ZAX_ANOREF) "
			_cQuery += "	FROM " + RetSqlName("ZAX") + " ZAX"
			_cQuery += "	WHERE ZAX.ZAX_FILIAL = ZAL.ZAL_FILIAL"
			_cQuery += "	AND ZAX.ZAX_CONTRA = ZAL.ZAL_CONTRA"
			_cQuery += "	AND ZAX.ZAX_CLIENT = ZAL.ZAL_CLIENT"
			_cQuery += "	AND ZAX.ZAX_LOJA = ZAL.ZAL_LOJA"
			_cQuery += "	AND ZAX.ZAX_STATUS <> 'I'"
			_cQuery += "	AND ZAX.D_E_L_E_T_ = ' ' )"
	
			_aDados := U_GV001X23(_cQuery,,,.T.)
		EndIf
	EndIf

	If !Empty(_aDados)
		_nMaiorMet := _aDados[1][1]
	EndIf

Return _nMaiorMet

/*/{Protheus.doc} _fIndicMet
Função para buscar as informações do indice da faixa da metrica. 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _nMetInfor, numerico, metrica informada.

@Return array, contem as informações do indice da faixa da metrica.
/*/

Static Function _fIndicMet(_cContrato,_cRevisao,_cCliente,_cLoja,_nMetInfor)

	Local _cQuery := ""
	
	Local _aDados := {}
	Local _aIndicMet := {}
	
	Default _cContrato := ""
	Default _cRevisao := ""
	Default _cCliente := ""
	Default _cLoja := ""
	
	Default _nMetInfor := 0
	
	_cQuery := " SELECT " + CRLF
	_cQuery += "	 ZAX.ZAX_INDCDU " + CRLF
	_cQuery += "	,ZAX.ZAX_VLMCDU " + CRLF
	_cQuery += "	,ZAX.ZAX_INDET " + CRLF
	_cQuery += "	,ZAX.ZAX_VLMET " + CRLF
	_cQuery += "	,ZAX.ZAX_INDAR " + CRLF
	_cQuery += "	,ZAX.ZAX_VLMAR " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAX") + " ZAX " + CRLF
	_cQuery += CRLF 
	_cQuery += " WHERE " + CRLF
	_cQuery += "	ZAX.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAX.ZAX_FILIAL = '" + xFilial("ZAX") + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_ANOREF = '" + _cAnoCorp + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_STATUS <> 'I' " + CRLF
	_cQuery += "	AND ROUND(ZAX.ZAX_REFMT1,2) <= " + AllTrim(Str(_nMetInfor)) + " " + CRLF 
	_cQuery += "	AND ROUND(ZAX.ZAX_REFMT2,2) >= " + AllTrim(Str(_nMetInfor)) + " " + CRLF
	
	_aDados := U_GV001X23(_cQuery,,,.T.)
	
	If !Empty(_aDados)
		_aIndicMet := _aDados[1]
	EndIf

Return _aIndicMet

/*/{Protheus.doc} _fOutroVlr
Função para retornar a somatória paga de CDU pelo cliente. 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.

@Return numerico, valor cobrado de CDU do ano corporativo.
/*/

Static Function _fOutroVlr(_cCliente,_cLoja,_cAnoCorp)

	Local _cQuery := ""
	
	Local _aDados := {}
	
	Local _nOutroVlr := 0
	
	Default _cCliente := ""
	Default _cLoja := ""
	
	_cQuery := " SELECT " + CRLF
	_cQuery += "	SUM(PH1.PH1_VLRCAL) PH1_VLRCAL " + CRLF
	_cQuery += " FROM " + RetSqlName("PH0") + " PH0 " + CRLF
	_cQuery += CRLF
	_cQuery += " INNER JOIN " + RetSqlName("PH1") + " PH1 ON " + CRLF
	_cQuery += "	PH1.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND PH1.PH1_FILIAL = '" + xFilial("PH1") + "' " + CRLF
	_cQuery += "	AND PH1.PH1_CODIGO = PH0.PH0_CODIGO " + CRLF
	_cQuery += "	AND PH1.PH1_TIPPRD = '1' " + CRLF
	_cQuery += "	AND PH1.PH1_VLRCAL <> 0.01 " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE PH0.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND PH0.PH0_FILIAL = '" + xFilial("PH0") + "' " + CRLF
	_cQuery += "	AND PH0.PH0_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND PH0.PH0_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND PH0.PH0_ANOREF = '" + _cAnoCorp + "' " + CRLF
	
	_aDados := U_GV001X23(_cQuery,,,.T.)
	
	If !Empty(_aDados)
		_nOutroVlr := _aDados[1][1]
	EndIf

Return _nOutroVlr

/*/{Protheus.doc} _fDadosPH0
Função para montar o cabeçalho do calculo. 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
@Param _cTipCalc, caracter, qual o tipo de calculo (N=Normal;R=Revisao;I=Inclusao Cnpj;E=Exclusao Cnpj).
@Param _cCalculo, caracter, qual calculo (I=Incremento;D=Diferenca).
@Param _cMulta, caracter, se gera multa ou não.
@Param _cMensCalc, caracter, mensagem no calculo.
/*/

Static Function _fDadosPH0(_cContrato,_cRevisao,_cCliente,_cLoja,_cTipCalc,_cCalculo,_cMulta,_cMensCalc,_nVlrCob)

	Local _aDados := {}
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cTipCalc := ""
	Default _cCalculo := ""
	Default _cMulta := "N"
	Default _cMensCalc := ""

	_cCondPag := ""
		
	If _cCalculo = "D"
		_cQuery := " SELECT " + CRLF
		_cQuery += " DISTINCT(ZOE.ZOE_CONDPG) FROM " + RetSqlName("ZOE") + " ZOE "
		_cQuery += " INNER JOIN " + RetSqlName("ZOD") + " ZOD ON "
		_cQuery += " 		ZOD.ZOD_COMPET = 'N' AND ZOD.ZOD_CODIGO = ZOE.ZOE_CODIGO "
		_cQuery += " 		AND ZOD.ZOD_VALDE <= " + cValtoChar(_nVlrCob) + " AND ZOD.ZOD_VALATE >= " + cValtoChar(_nVlrCob) + " AND ZOD.D_E_L_E_T_ = ' ' "
		_cQuery += " WHERE ZOE.D_E_L_E_T_ = ' ' "
		
		_aDados := U_GV001X23(_cQuery,,,.T.)
	
		If !Empty(_aDados)
			If Len(_aDados) == 1
				_cCondPag := _aDados[1][1]
			EndIf
		EndIf
			
	EndIf
	
	If _cCalculo = "I"
		_cQuery := " SELECT " + CRLF
		_cQuery += " DISTINCT(ZOE.ZOE_CONDPG) FROM " + RetSqlName("ZOE") + " ZOE "
		_cQuery += " INNER JOIN " + RetSqlName("ZOD") + " ZOD ON "
		_cQuery += " 		ZOD.ZOD_COMPET = 'I' AND ZOD.ZOD_CODIGO = ZOE.ZOE_CODIGO "
		_cQuery += " 		AND ZOD.ZOD_VALDE <= " + cValtoChar(_nVlrCob) + " AND ZOD.ZOD_VALATE >= " + cValtoChar(_nVlrCob) + " AND ZOD.D_E_L_E_T_ = ' ' "
		_cQuery += " WHERE ZOE.D_E_L_E_T_ = ' ' "
		
		_aDados := U_GV001X23(_cQuery,,,.T.)
	
		If !Empty(_aDados)
			If Len(_aDados) == 1
				_cCondPag := _aDados[1][1]
			EndIf
		EndIf		
	EndIf
	
	Aadd(_aDadosPH0,{"PH0_CLIENT",_cCliente})
	Aadd(_aDadosPH0,{"PH0_LOJA",_cLoja})
	Aadd(_aDadosPH0,{"PH0_ANOREF",_cAnoCorp})
	Aadd(_aDadosPH0,{"PH0_TIPCAL",_cTipCalc})
	Aadd(_aDadosPH0,{"PH0_CALC",_cCalculo})
	Aadd(_aDadosPH0,{"PH0_MULTA",_cMulta})
	Aadd(_aDadosPH0,{"PH0_EXCECA",_fFindZAW(_cContrato,_cRevisao,_cCliente,_cLoja)})
	Aadd(_aDadosPH0,{"PH0_TIPREC",ZTS->ZTS_TIPREC})
	Aadd(_aDadosPH0,{"PH0_OBSCAL",_cMensCalc})
	Aadd(_aDadosPH0,{"PH0_CONDPG",_cCondPag})
	Aadd(_aDadosPH0,{"PH0_GRPFAT",SubStr(_cSiteFat,1,TamSx3("ZTS_GRPFAT")[1])})
	Aadd(_aDadosPH0,{"PH0_FILFAT",SubStr(_cSiteFat,TamSx3("ZTS_GRPFAT")[1]+1,TamSx3("ZTS_FILFAT")[1])})
	Aadd(_aDadosPH0,{"PH0_CONTRA",_aProdCorp[1][5]})
	Aadd(_aDadosPH0,{"PH0_VERATU",_aProdCorp[1][6]})
		
Return

/*/{Protheus.doc} _fFindZAW
Função para verificar se existe exceção. 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.
/*/

Static Function _fFindZAW(_cContrato,_cRevisao,_cCliente,_cLoja)

	Local _cQuery := ""
	
	Local _cRetorno := "N"
	
	Default _cCliente := ""
	Default _cLoja := ""

	_cQuery := " SELECT 1 " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAW") + " ZAW " + CRLF
	_cQuery += CRLF 
	_cQuery += " WHERE " + CRLF
	_cQuery += "	ZAW.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAW.ZAW_FILIAL = '" + xFilial("ZAW") + "' " + CRLF
	_cQuery += "	AND ZAW.ZAW_CONTRA = '" + _cContrato + "' " + CRLF
	_cQuery += "	AND ZAW.ZAW_REVISA = '" + _cRevisao + "' " + CRLF
	_cQuery += "	AND ZAW.ZAW_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAW.ZAW_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND ZAW.ZAW_CODMOT = '99' " + CRLF
	_cQuery += "	AND ZAW.ZAW_VIGEDE <= '" + DtoS(dDataBase) + "' " + CRLF
	_cQuery += "	AND ZAW.ZAW_VIGENC >= '" + DtoS(dDataBase) + "' " + CRLF
	
	_cRetorno := IIf(!Empty(U_GV001X23(_cQuery)),"S","N")

Return _cRetorno

/*/{Protheus.doc} _fDadosPH1
Função para montar os itens do calculo. 

<AUTHOR> Ribeiro
@Since 10/05/15
/*/

Static Function _fDadosPH1()
	
	Local _dVencto := CtoD("")
	
	Local _nPosicao := 0
	Local _nCount := 0
	Local _nValor := 0
	
	Local _cTipPrd := ""
	
	Local _aAuxiliar := {}
	
	Local _lETMain := .F.
	Local _lARMain := .F.
	Local _lSMSMain := .F.
	
	While Empty(_dVencto) .And. _nCount < 3
		If _nCount == 0
			_cTipPrd := "4"	// SMS
		ElseIf _nCount == 1
			_cTipPrd := "2"	// ET
		ElseIf _nCount == 2
			_cTipPrd := "3"	// AR
		EndIf

		_nPosicao := Ascan(_aProdCorp,{|x| AllTrim(x[1]) == _cTipPrd})
		
		If _nPosicao > 0
			_dVencto := _aProdCorp[_nPosicao][7]
		EndIf
		
		_nCount++ 
	EndDo
	
	// Ordena pelo tipo do produto - 1=CDU; 2=SMS; 3=ET; 4=AR; 5=CDU ADICIONAL; 6=SMS ADICIONAL; 7=ET ADICIONAL; 8=AR ADICIONAL;	
	ASort(_aProdCorp,,,{|x,y| x[1] < y[1]})
	
	For _nCount := 1 To Len(_aProdCorp)
	
		_aAuxiliar := {}
		
		If _aProdCorp[_nCount][1] == "1"		// CDU
			_nValor := _nVlrCob
			
		ElseIf _aProdCorp[_nCount][1] == "2"	// ET
			_nValor := _nVlrET
			_lETMain := .T.
			
		ElseIf _aProdCorp[_nCount][1] == "3"	// AR
			_nValor := _nVlrAR
			_lARMain := .T.
			
		ElseIf _aProdCorp[_nCount][1] == "4"	// SMS
			_nValor := _nVlrSMS
			_lSMSMain := .T.
			
		ElseIf _aProdCorp[_nCount][1] == "5"	// CDU ADICIONAL
			_nValor := (_nVlrCob/100)*ZTS->ZTS_PERCAD
			
		ElseIf _aProdCorp[_nCount][1] == "6"	// ET ADICIONAL
			_nValor := (_nVlrET/100)*ZTS->ZTS_PERCAD
			
		ElseIf _aProdCorp[_nCount][1] == "7"	// AR ADICIONAL
			_nValor := (_nVlrAR/100)*ZTS->ZTS_PERCAD
			
		ElseIf _aProdCorp[_nCount][1] == "8"	// SMS ADICIONAL
			If _lETMain .And. _lARMain .And. !_lSMSMain
				_nValor := ((_nVlrET/100)*ZTS->ZTS_PERCAD) + ((_nVlrAR/100)*ZTS->ZTS_PERCAD)
			ElseIf _lSMSMain
				_nValor := (_nVlrSMS/100)*ZTS->ZTS_PERCAD
			EndIf
		EndIf
		
		// Se o valor for menor, grava como gratuito
		If _nValor < 1
			_nValor := 0.01
		EndIf

		If _aProdCorp[_nCount][1] $ "1/5"
			Aadd(_aAuxiliar,{"PH1_VENCTO",dDataBase+30})
		Else
			Aadd(_aAuxiliar,{"PH1_VENCTO",_dVencto})
		EndIf

		Aadd(_aAuxiliar,{"PH1_TIPPRD",_aProdCorp[_nCount][1]})
		Aadd(_aAuxiliar,{"PH1_CODPRD",_aProdCorp[_nCount][2]})
		Aadd(_aAuxiliar,{"PH1_VLRCAL",_nValor})
		
		Aadd(_aDadosPH1,AClone(_aAuxiliar))
	Next
	
Return

/*/{Protheus.doc} _fDadosPH2
Função para montar a memória de cálculo 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cTipCalc, caracter, qual o tipo de calculo (N=Normal;R=Revisao;I=Inclusao Cnpj;E=Exclusao Cnpj).
@Param _cCalculo, caracter, qual calculo (I=Incremento;D=Diferenca).
/*/

Static Function _fDadosPH2(_cCliente,_cLoja, _cTipCalc,_cCalculo)

	Local _cTipMet := ""	// 1=Metrica Informada;2=Metrica Comprovada;3=Maior Metrica Reajustada;4=Maior Metrica;5=ET;6=AR;7=ET/AR

	Local _nCount := 0
	Local _nRecno := 0
	Local _nReceit := 0
	Local _nIndice := 0
	Local _nVlrMin := 0
	Local _nVlrCal := 0
	Local _nVlrCon := 0
	Local _nVlrPag := 0
	
	Local _aAuxiliar := {}
	
	Local _lContinua := .F.
	Local _lIncr := .T.
	
	Default _cCalculo := ""

	For _nCount := 1 To 5
		_aAuxiliar := {}
		_lContinua := .F.
		_nReceit := _nMetInfor

		If _cCalculo == "D" .And. _lIncr
		
			_nRecno := _fMemoCalc(_cCliente,_cLoja)
			
			If !Empty(_nRecno)
				PH2->(DbGoTo(_nRecno))
				
				_cTipMet := PH2->PH2_TIPMET
				_nReceit := PH2->PH2_RECEIT
				_nIndice := PH2->PH2_INDICE
				_nVlrMin := PH2->PH2_VLRMIN
				_nVlrCal := PH2->PH2_VLRCAL
				_nVlrCon := PH2->PH2_VLRCON
				_nVlrPag := PH2->PH2_VLRPAG
			EndIf
		
			_nCount--
			_lIncr := .F.
			
		ElseIf _nCount == 1		// Metrica informada - CDU
			_cTipMet := IIf(_cCalculo=="I","1","2")
			_nIndice := _aIndCalc[_NINDCDU]
			_nVlrMin := _aIndCalc[_NMINCDU]
			_nVlrCal := _nCalcCDU
			_nVlrCon := _nVlrCDU
			_nVlrPag := _nVlrCob
			_lContinua := .T.
		ElseIf _nCount == 2	// Metrica Historica
			_cTipMet := IIf(ZTS->ZTS_TIPREC=="1","3","4")
			_nReceit := _nMaiorMet
			_nIndice := _aIndHist[_NINDCDU]
			_nVlrMin := _aIndHist[_NMINCDU]
			_nVlrCal := _nCalcHist
			_nVlrCon := _nVlrHist
			_nVlrPag := 0
			_lContinua := .T.
		ElseIf _nCount == 3	// Metrica de ET
			_cTipMet := "5"
			_nIndice := _aIndCalc[_NINDET]
			_nVlrMin := _aIndCalc[_NMINET]
			_nVlrCal := _nCalcET
			_nVlrCon := _nVlrET
			_nVlrPag := _nVlrET
			_lContinua := Ascan(_aProdCorp,{|x| AllTrim(x[1]) == "2"}) > 0
		ElseIf _nCount == 4	// Metrica de AR
			_cTipMet := "6"
			_nIndice := _aIndCalc[_NINDAR]
			_nVlrMin := _aIndCalc[_NMINAR]
			_nVlrCal := _nCalcAR
			_nVlrCon := _nVlrAR
			_nVlrPag := _nVlrAR
			_lContinua := Ascan(_aProdCorp,{|x| AllTrim(x[1]) == "3"}) > 0
		ElseIf _nCount == 5	// Metrica de SMS
			_cTipMet := "7"
			_nIndice := _aIndCalc[_NINDET]
			_nVlrMin := _aIndCalc[_NMINET]
			_nVlrCal := _nCalcET
			_nVlrCon := _nVlrET
			_nVlrPag := _nVlrET
			_lContinua := Ascan(_aProdCorp,{|x| AllTrim(x[1]) == "4"}) > 0
		EndIf
		
		If _lContinua
			Aadd(_aAuxiliar,{"PH2_TIPMET",_cTipMet})
			Aadd(_aAuxiliar,{"PH2_RECEIT",_nReceit})
			Aadd(_aAuxiliar,{"PH2_INDICE",_nIndice})
			Aadd(_aAuxiliar,{"PH2_VLRMIN",_nVlrMin})
			Aadd(_aAuxiliar,{"PH2_VLRCAL",_nVlrCal})
			Aadd(_aAuxiliar,{"PH2_VLRCON",_nVlrCon})
			Aadd(_aAuxiliar,{"PH2_VLRPAG",_nVlrPag})
	
			Aadd(_aDadosPH2,AClone(_aAuxiliar))
		EndIf
	Next
	
Return

/*/{Protheus.doc} _fMemoCalc
Função para buscar a memoria de calculo. 

<AUTHOR> Ribeiro
@Since 10/05/15

@Param _cCliente, caracter, codigo do cliente.
@Param _cLoja, caracter, codigo da loja do cliente.

@Return numerico, valor cobrado de CDU do ano corporativo.
/*/

Static Function _fMemoCalc(_cCliente,_cLoja)

	Local _cQuery := ""
	
	Local _aDados := {}
	
	Local _nRecno := 0
	
	Default _cCliente := ""
	Default _cLoja := ""
	
	_cQuery := " SELECT PH2.R_E_C_N_O_ " + CRLF
	_cQuery += " FROM " + RetSqlName("PH0") + " PH0 " + CRLF
	_cQuery += CRLF
	_cQuery += " INNER JOIN " + RetSqlName("PH2") + " PH2 ON " + CRLF
	_cQuery += "	PH2.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND PH2.PH2_FILIAL = '" + xFilial("PH2") + "' " + CRLF
	_cQuery += "	AND PH2.PH2_CODIGO = PH0.PH0_CODIGO " + CRLF
	_cQuery += "	AND PH2.PH2_TIPMET = '1' " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE PH0.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND PH0.PH0_FILIAL = '" + xFilial("PH2") + "' " + CRLF
	_cQuery += "	AND PH0.PH0_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND PH0.PH0_LOJA = '" + _cLoja + "' " + CRLF
	_cQuery += "	AND PH0.PH0_ANOREF = '" + _cAnoCorp + "' " + CRLF
	_cQuery += "	AND PH0.PH0_TIPCAL = 'N' " + CRLF
	_cQuery += "	AND PH0.PH0_CALC = 'I' " + CRLF
	_cQuery += "	AND PH0.PH0_STATUS = '2' " + CRLF
	
	_aDados := U_GV001X23(_cQuery)
	
	If !Empty(_aDados)
		_nRecno := _aDados[1][1]
	EndIf

Return _nRecno

/*/{Protheus.doc} _fGravCalc
Função para gravar o calculo. 

<AUTHOR> Ribeiro
@Since 10/05/15
/*/

Static Function _fGravCalc(_cCliente,_cLoja)

	Local _cCodigo := ""
	
	Local _nCount := 0
	Local _nCount2 := 0
	
	Local _lAtuaZTS := AllTrim(_cAnoCorp) == AllTrim(GetMv("TDI_ANOCOR"))
	
	Default _cCliente := ""
	Default _cLoja := ""
	
	If !Empty(_aDadosPH0) .And. !Empty(_aDadosPH1) .And. !Empty(_aDadosPH2)
		_cCodigo := GetSxeNum("PH0","PH0_CODIGO")
		//PH0->(ConfirmSX8())
		
		While PH0->(DBSeek( xFilial("PH0") + _cCodigo , .f. ))
           PH0->(ConfirmSX8())
           _cCodigo := GetSxeNum("PH0","PH0_CODIGO")
		EndDo
		PH0->(ConfirmSX8())
		
		Begin Transaction
			// Inclui o cabeçalho do cálculo
			RecLock("PH0",.T.)
				PH0->PH0_FILIAL := xFilial("PH0")
				PH0->PH0_CODIGO := _cCodigo
				PH0->PH0_STATUS := "1"
				PH0->PH0_STAREP := "2"
				PH0->PH0_DATCAL := dDataBase
				PH0->PH0_HORCAL := Time()
				PH0->PH0_USRCAL := cUserName
				PH0->PH0_MAQCAL := GetClientIp() + " / " + GetComputerName()

				For _nCount := 1 To Len(_aDadosPH0)
					PH0->(FieldPut(FieldPos(_aDadosPH0[_nCount][01]),_aDadosPH0[_nCount][02]))
				Next
			PH0->(MsUnLock())
			 
			// Inclui os itens do cálculo
			For _nCount := 1 To Len(_aDadosPH1)
				RecLock("PH1",.T.)
					PH1->PH1_FILIAL := xFilial("PH1")
					PH1->PH1_CODIGO := _cCodigo
					PH1->PH1_ITEM := StrZero(_nCount,2)
					
					For _nCount2 := 1 To Len(_aDadosPH1[_nCount])
						PH1->(FieldPut(FieldPos(_aDadosPH1[_nCount][_nCount2][01]),_aDadosPH1[_nCount][_nCount2][02]))
					Next
				PH1->(MsUnLock())
			Next
		
			// Inclui a memória de cálculo
			For _nCount := 1 To Len(_aDadosPH2)
				RecLock("PH2",.T.)
					PH2->PH2_FILIAL := xFilial("PH2")
					PH2->PH2_CODIGO := _cCodigo
	
					For _nCount2 := 1 To Len(_aDadosPH2[_nCount])
						PH2->(FieldPut(FieldPos(_aDadosPH2[_nCount][_nCount2][01]),_aDadosPH2[_nCount][_nCount2][02]))
					Next
				PH2->(MsUnLock())
			Next
			
			// Atualiza o cadastro de clientes corporativos.
			If _lAtuaZTS
				ZTS->(DbSetOrder(1))
				If ZTS->(DbSeek(xFilial("ZTS")+_cCliente+_cLoja))
					RecLock("ZTS",.F.)
												
						If !IsBlind() .Or. IsInCallStack("_sCalcula") .Or. IsInCallStack("U_TGCVA011") .Or. IsInCallStack("U_TGCVA034")
							ZTS->ZTS_PASSO := "M"
						EndIf
						ZTS->ZTS_ANOPAS := _cAnoCorp
						ZTS->ZTS_TIPPAS := IIf(dDataBase <= ZTS->ZTS_DATA1,"1","2")
					ZTS->(MsUnLock())
				EndIf
			EndIf
		End Transaction
		
	
		
		// Posiciona nas tabelas
		PH0->(DbSetOrder(1))
		PH0->(DbSeek(xFilial("PH0")+_cCodigo))

		PH1->(DbSetOrder(1))
		PH1->(DbSeek(xFilial("PH1")+_cCodigo))

		PH2->(DbSetOrder(1))
		PH2->(DbSeek(xFilial("PH2")+_cCodigo))
		
	EndIf
	
Return

/*/{Protheus.doc} GV033A02

<AUTHOR> Ribeiro
@Since 10/05/15
@Return caracter, mensagem de erro.
/*/

User Function GV033A02(_cCodigo,_cCliente,_cLoja,_cStatus,_cProposta,_cVerGer,_cPedCDU,_cPedRep)

	Local _cRetorno := ""

	Local _lContinua := .T.
	
	Default _cCodigo := ""
	Default _cCliente := ""
	Default _cLoja := ""
	Default _cStatus := ""
	Default _cProposta := ""
	Default _cVerGer := ""
	Default _cPedCDU := ""
	Default _cPedRep := ""
	
	DbSelectArea("PH0")	// CABECALHO CALCULO CORPORATIVO
	PH0->(DbSetOrder(1))	// PH0_FILIAL+PH0_CODIGO
	
	_cStatus := AllTrim(_cStatus)
	
	If Empty(_cCodigo) .And. (Empty(_cCliente) .Or. Empty(_cLoja))
		_lContinua := .F.
	ElseIf _cStatus == "1" .And. Empty(_cProposta)
		_lContinua := .F.	
	ElseIf _cStatus == "2" .And. Empty(_cProposta) .And. Empty(_cVerGer)
		_lContinua := .F.
	ElseIf _cStatus == "3" .And. Empty(_cProposta) .And. Empty(_cVerGer) .And. Empty(_cPedCDU)
		_lContinua := .F.
	ElseIf _cStatus $ "4|5" .And. Empty(_cProposta) .And. Empty(_cVerGer) .And. Empty(_cPedCDU)
		_lContinua := .F.
	EndIf
	
	If _lContinua
		If Empty(_cCodigo)
			_cCodigo := _GetCodPH0(_cCliente,_cLoja,_cProposta,_cVerGer,_cPedCDU,_cPedRep)
		EndIf
		
		If PH0->(DbSeek(xFilial("PH0")+_cCodigo))
			RecLock("PH0",.F.)
				Do Case
					Case _cStatus == "1" // Gerou proposta
						PH0->PH0_PROPOS := ""
						PH0->PH0_VERGER := ""
						PH0->PH0_PEDCDU := ""
					Case _cStatus == "2" // Gerou proposta
						If !Empty(_cProposta)
							PH0->PH0_PROPOS := _cProposta
						EndIf
						PH0->PH0_VERGER := ""
						PH0->PH0_PEDCDU := ""
					Case _cStatus == "3" // Gerou Contrato
						If !Empty(_cVerGer)
							PH0->PH0_VERGER := _cVerGer
						EndIf
						PH0->PH0_PEDCDU := ""
					Case _cStatus == "4" .And. !Empty(_cPedCDU) // Gerou pedido de CDU
						PH0->PH0_PEDCDU := _cPedCDU
					Case _cStatus == "5" // Libera pedido de CDU
						PH0->PH0_BLQTEL := If(Empty(PH0->PH0_BLQTEL) .Or. PH0->PH0_BLQTEL == "B","B","D")
				EndCase
				PH0->PH0_STATUS := _cStatus // Grava o status
			PH0->(MsUnLock())
		EndIf
	EndIf
	
Return

/*/{Protheus.doc} _GetCodPH0

<AUTHOR> Ribeiro
@Since 10/05/15
@Return caracter, mensagem de erro.
/*/

Static Function _GetCodPH0(_cCliente,_cLoja,_cProposta,_cVerGer,_cPedCDU,_cPedRep)

	Local _cQuery := ""
	Local _cCodigo := ""
	
	Local _aDados := {}

	Default _cCliente := ""
	Default _cLoja := ""
	Default _cProposta := ""
	Default _cVerGer := ""
	Default _cPedCDU := ""
	Default _cPedRep := ""

	_cQuery := " SELECT PH0.PH0_CODIGO " + CRLF
	_cQuery += " FROM " + RetSqlName("PH0") + " PH0 " + CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += " 	PH0.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += " 	AND PH0.PH0_FILIAL = '" + xFilial("PH0") + "' " + CRLF
	_cQuery += " 	AND PH0.PH0_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += " 	AND PH0.PH0_LOJA = '" + _cLoja + "' " + CRLF
	If !Empty(_cProposta)
		_cQuery += " 	AND PH0.PH0_PROPOS = '" + _cProposta + "' " + CRLF
	EndIf
	If !Empty(_cVerGer) .And. Empty(_cProposta)
		_cQuery += " 	AND PH0.PH0_VERGER = '" + _cVerGer + "' " + CRLF
	EndIf
	If !Empty(_cPedCDU) .And. Empty(_cVerGer)
		_cQuery += " 	AND PH0.PH0_PEDCDU = '" + _cPedCDU + "' " + CRLF
	EndIf
	If !Empty(_cPedRep)
		_cQuery += " 	AND PH0.PH0_PEDREP = '" + _cPedRep + "' " + CRLF
	EndIf
	
	_aDados := U_GV001X23(_cQuery)
	
	If !Empty(_aDados)
		_cCodigo := _aDados[1][1]
	EndIf
	
Return _cCodigo
