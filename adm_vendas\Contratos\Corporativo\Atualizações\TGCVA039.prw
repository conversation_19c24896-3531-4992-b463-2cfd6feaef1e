#Include "Protheus.ch"
#Include "FWMVCDef.ch"
#Include "TGCVA039.ch"

/*/{Protheus.doc} TGCVA039
Função para mostrar as memórias de calculo do cliente.

<AUTHOR>
@since 29/08/2013
/*/

User Function TGCVA039(_cCliente,_cLoja)

	Local _oExecView
	
	Local _aArea := {}
	Local _aAreaZTS := {}
	
	Local _lContinua := .T.
	
	Default _cCliente := ""
	Default _cLoja := ""
	
	_aArea := GetArea()
	_aAreaZTS := ZTS->(GetArea())
	
	If !Empty(_cCliente)
		DbSelectArea("ZTS")
		ZTS->(DbSetOrder(1))
		If !ZTS->(DbSeek(xFilial("ZTS")+_cCliente+_cLoja))
			Help(,,"Help",,STR0004 + " [" + _cCliente + "/" + _cLoja + "] " + STR0005,1,0)	// "Cliente"###"não encontrado!"
		EndIf
	EndIf
	
	If _lContinua
		_oExecView := FWViewExec():New()
		_oExecView:setTitle(STR0002 + " - " + ZTS->(ZTS_CODCLI + "/" + ZTS_LOJA))	// "Memoria de calculo do cliente"
		_oExecView:setSource("TGCVA039")
		_oExecView:setModal(.F.)               
		_oExecView:setOperation(1)
		_oExecView:openView(.F.)
	EndIf
	
	RestArea(_aAreaZTS)
	RestArea(_aArea)
	
Return

/*/{Protheus.doc} ModelDef
Definição do modelo de dados.

<AUTHOR> Ribeiro
@since 04/12/2015
/*/

Static Function ModelDef()

	Local oModel
	Local oStrZTS
	Local oStrPH0
	Local oStrPH1
	Local oStrPH2
	
	oStrZTS := FWFormStruct(1,"ZTS")
	
	oStrPH0 := FWFormStruct(1,"PH0")

	oStrPH1 := FWFormStruct(1,"PH1")

	oStrPH1:RemoveField("PH1_CODIGO")
	oStrPH1:RemoveField("PH1_MSBLQL")
	
	oStrPH2 := FWFormStruct(1,"PH2")

	oStrPH2:RemoveField("PH2_CODIGO")
	oStrPH2:RemoveField("PH2_MSBLQL")
	
	oModel := MPFormModel():New("TGCVM039")
	
	oModel:AddFields("ZTSMASTER",,oStrZTS)
		
	oModel:addGrid("PH0MASTER","ZTSMASTER",oStrPH0)
	oModel:SetPrimaryKey({"PH0_FILIAL","PH0_CODIGO"})
	oModel:SetRelation("PH0MASTER",{{"PH0_FILIAL","ZTS_FILIAL"},{"PH0_CLIENT","ZTS_CODCLI"},{"PH0_LOJA","ZTS_LOJA"}},PH0->(IndexKey(2)))

	oModel:addGrid("PH1DETAIL","PH0MASTER",oStrPH1)
	oModel:SetPrimaryKey({"PH0_FILIAL","PH0_CODIGO"})
	oModel:SetRelation("PH1DETAIL",{{"PH1_FILIAL","PH0_FILIAL"},{"PH1_CODIGO","PH0_CODIGO"}},PH1->(IndexKey(1)))

	oModel:addGrid("PH2DETAIL","PH0MASTER",oStrPH2)
	oModel:SetPrimaryKey({"PH0_FILIAL","PH0_CODIGO"})
	oModel:SetRelation("PH2DETAIL",{{"PH2_FILIAL","PH0_FILIAL"},{"PH2_CODIGO","PH0_CODIGO"}},PH2->(IndexKey(1)))

Return oModel

/*/{Protheus.doc} ModelDef
Definição de interface

<AUTHOR> Ribeiro
@since 04/12/2015
/*/

Static Function ViewDef()

	Local oView
	Local oModel
	Local oStrPH0
	Local oStrPH1
	Local oStrPH2
	
	oStrPH0 := FWFormStruct(2,"PH0")

	oStrPH0:RemoveField("PH0_CLIENT")
	oStrPH0:RemoveField("PH0_LOJA")
	oStrPH0:RemoveField("PH0_NOME")
	oStrPH0:RemoveField("PH0_MSBLQL")

	oStrPH1 := FWFormStruct(2,"PH1")

	oStrPH1:RemoveField("PH1_CODIGO")
	oStrPH1:RemoveField("PH1_MSBLQL")

	oStrPH2 := FWFormStruct(2,"PH2")

	oStrPH2:RemoveField("PH2_CODIGO")
	oStrPH2:RemoveField("PH2_MSBLQL")
	
	oModel := FWLoadModel("TGCVA039")
	
	oView := FWFormView():New()
	
	oView:SetModel(oModel)
	oView:EnableControlBar(.T.)
	oView:AddGrid("VIEW_PH0",oStrPH0,"PH0MASTER")
	oView:AddGrid("VIEW_PH1",oStrPH1,"PH1DETAIL")
	oView:AddGrid("VIEW_PH2",oStrPH2,"PH2DETAIL")
	oView:CreateHorizontalBox("UP",30)
	oView:CreateHorizontalBox("CENTER",35)
	oView:CreateHorizontalBox("DOWN",35)
	oView:SetOwnerView("VIEW_PH0","UP")
	oView:SetOwnerView("VIEW_PH1","CENTER")
	oView:SetOwnerView("VIEW_PH2","DOWN")
	
	// Habilita filtro
	oView:SetViewProperty("VIEW_PH0","GRIDFILTER",{.T.}) 
	
	// Adiciona Açoes
	oView:AddUserButton(STR0003,"",{|oView| U_TGCVR008(oView:oModel:GetModel("PH0MASTER"):GetValue("PH0_CLIENT"),,oView:oModel:GetModel("PH0MASTER"):GetValue("PH0_CODIGO")) })	// "Exporta Memoria"

	oView:AddIncrementField("VIEW_PH1","PH1_ITEM")

Return oView