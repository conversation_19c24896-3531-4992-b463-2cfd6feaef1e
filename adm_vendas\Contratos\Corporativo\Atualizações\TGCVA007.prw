#Include "TGCVA007.ch"
#Include "Protheus.ch"
#Include "Folder.ch"
#Include "RwMake.ch"
#INCLUDE "TbiConn.ch"
#INCLUDE "Ap5Mail.ch"

#Define _LF_	chr(13)+chr(10)


	
/*----------------------------------------------/
Nome.: U_TGCVA007()
Data.: 09.2009
Autor: Denardi

Descricao:
Chama a tela de contratos corporativos por marca
/-----------------------------------------------*/
User Function TGCVA007(_cContrato,_cRevisa,_nTipo,_cCliente,_cLoja)

	Local aArea := GetArea()

	Local _lCDU := .F.
	Local _lOut := .F.

	Local cQuery := ""
	Local cZALAlias := GetNextAlias()

	Local nTotReg := 0
	Local _nCount := 0
	Local lContinua	:= .F.
	Local lNoContra	:= .F.

	Private _Contrato	:= ""
	Private _Versao := ""
	Private _Cliente	:= ""
	Private _Loja := ""
	Private _Picpad := ""
	Private cSiteFat := cEmpAnt+cFilAnt

	Private aProdCorp := {}
	
	Private _nOpcao := 0

	Default _cContrato := ""
	Default _cRevisa := Space(TamSx3("CN9_REVISA")[1])
	Default _cCliente := ""
	Default _cLoja := ""

	Default _nTipo := 1 // 1 - Visualizacao || 2 - Manutencao || 6 - CRM
	
	
	DbSelectArea("ZTS")
	DbSetOrder(1) //ZTS_FILIAL+ZTS_CODCLI+ZTS_LOJA
	
	
	DbSelectArea("ZAL")
	
	DbSetOrder(5) // ZAL_FILIAL+ZAL_CONTRA+ZAL_REVISA+ZAL_CLIENT+ZAL_LOJA
	
	If !DbSeek(xFilial('ZAL') + _cContrato)
		
		If ZTS->(DbSeek(xFilial("ZTS") + _cCliente))
			If ZTS->ZTS_STATUS = "A"
				lNoContra := .T.
			Else
				Aviso( "Atencao" , "Cadastro de cliente corporativo Inativo"  , { "OK" } )	
			EndIf
		Else
			Aviso( "Atencao" , "Cadastrar o cliente no cadastro de clientes corporativos"  , { "OK" } )	
		EndIf
		
	EndIf
	
	DbSetOrder(10) //ZAL_FILIAL+ZAL_STATUS+ZAL_CONTRA+ZAL_REVISA+ZAL_CGCPRI+ZAL_ANOREF
	
	
	If DbSeek(xFilial('ZAL') + "A" + _cContrato) .oR. DbSeek(xFilial('ZAL') + "C" + _cContrato) .Or. lNoContra
		_nOpcao := _nTipo
		
		lContinua := _fPosContr(_cContrato,_cRevisa,_cCliente,_cLoja)
		
		If lContinua
			If _nOpcao > 0
				If _nOpcao <> 2
					INCLUI := .F.
					ALTERA := .F.
				EndIf
				
				_Contrato := CN9->CN9_NUMERO
				_Versao := CN9->CN9_REVISA
				_Cliente := CNC->CNC_CLIENT
				_Loja := CNC->CNC_LOJACL
				DbSelectArea('ZAX')
				DbSetOrder(3) //	ZAX_FILIAL+ZAX_CONTRA+ZAX_REVISA+ZAX_CLIENT+ZAX_LOJA+ZAX_ITEM
				
				If !ZAX->(DbSeek(xFilial("ZAX") + _cContrato ))
					DbSelectArea("ZTS")
					DbSetOrder(1) // ZTS_FILIAL+ZTS_CODCLI+ZTS_LOJA 
					If ZTS->(DbSeek(xFilial("ZTS") + _cCliente + _cLoja))
						U_GV001X09(_cCliente, _cLoja, ZTS->ZTS_PROPOS)
					EndIf
				EndIf  
				
				
				//Atualiza a revisão do contrato do cliente  dentro do corporativo, a fim de evitar problemas de duplicidade e problemas com tabela de índices inexistente
				U_TCORP_REV(_Cliente,@_Versao)//aqui ja tem um trat meu  Oswaldo
		
				M->ZAL_CLIENT := _Cliente
				M->ZAL_LOJA   := _Loja
				M->ZAL_CONTRA := _Contrato
				M->ZAL_REVISA := _Versao
				
				M->ZAX_CLIENT := _Cliente
				M->ZAX_LOJA   := _Loja
				M->ZAX_CONTRA := _Contrato
				M->ZAX_REVISA := _Versao
				
				M->ZAW_CLIENT := _Cliente
				M->ZAW_LOJA   := _Loja
				M->ZAW_CONTRA := _Contrato
				M->ZAW_REVISA := _Versao
		
				cSiteFat := U_GV001X16(_Cliente,_Loja,,,,,,.T.)
		
				If Empty(cSiteFat)
					cSiteFat := cEmpAnt+cFilAnt
				Endif
				
				aProdCorp := U_GV001X06( _Cliente, _Loja, SubStr(cSiteFat,1,Len(cEmpAnt)), SubStr(cSiteFat,Len(cEmpAnt)+1,Len(cFilAnt)) )
		
				U_GV001X12(_Cliente,_Loja)
				U_GV001X13(_Cliente,_Loja, aProdCorp)
		
				cQuery := " SELECT ZAL.* "
				cQuery += " FROM " + RetSQLName("ZAL") + " ZAL "
				cQuery += " WHERE "
				cQuery += "	ZAL.D_E_L_E_T_ = ' ' " 
				cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' "
				cQuery += "	AND ZAL.ZAL_CLIENT = '" + _Cliente + "' "
				cQuery += "	AND ZAL.ZAL_LOJA = '" + _Loja + "' "
				cQuery += "	AND ZAL.ZAL_CONTRA = '" + _Contrato + "' "
				cQuery += "	AND ZAL.ZAL_REVISA = '" + _Versao + "' "
				cQuery += "	AND ZAL.ZAL_STATUS IN ('A','C') "
				cQuery += " ORDER BY ZAL_CGCAGR "
		
				cZALAlias := GetNextAlias()
				DbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cZALAlias, .T., .F. )
				
				DbSelectArea( cZALAlias )
				AEval( ZAL->( dbStruct() ), { | x | IIf( x[ 2 ] != 'C' , TcSetField( ( cZALAlias ) ,AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil  ) } )
				( cZALAlias )->( dbEval( { || nTotReg ++ },,{ || !Eof() } ) )
		
				IIf( Select( cZALAlias ) > 0, ( cZALAlias )->( DbCloseArea() ), Nil )
		
				If Len(aProdCorp) > 0
					For _nCount:= 1 to Len(aProdCorp)
						If aProdCorp[_nCount,4] == "1"
							_lCDU := .T.
						ElseIf aProdCorp[_nCount,4] $ ("2/3/4")
							_lOut := .T.
						EndIf
					Next
					If !_lCDU
						Help(,,"Help",,STR0030,1,0)	// "Cliente não possui item de CDU ativo!"
					EndIf
					If !_lOut
						Help(,,"Help",,STR0031,1,0)	// "Cliente não possui item de ET/AR/SMS ativo!"
					EndIf
		
					If nTotReg == 0
						U_GV007A21(_Cliente,_Loja)
					EndIf
		
					U_GV007A01(_cContrato)
				Else
					Help(,,"Help",,STR0032,1,0)	// "Cliente não possui item de contrato ativo!"
					If nTotReg <> 0
						U_GV007A01(_cContrato)
					EndIf
				EndIf
			Else
				Help(,,"Help",,STR0009,1,0)	// "Parâmetros incorretos"
			EndIf
		Else
			Help(,,"Help",,"Contrato não encontrado ou posicionar no cliente principal do contrato!",1,0)	
		EndIf
	Else
		Help(,,"Help",,"Contrato corporativo inativo. Integrar nova proposta!",1,0)	
	EndIf
	

	RestArea(aArea)
Return

/*/{Protheus.doc} _fPosContr
Função para posicionar no contrato.

<AUTHOR> Ribeiro
@since 29/08/2013
/*/

Static Function _fPosContr(_cContrato,_cRevisa,_cCliente,_cLoja)

	Local _cQuery := ""
	
	Local _aDados := {} 

	Local _lAchou := .F.
	
	Default _cContrato := ""
	Default _cRevisa := Space(TamSx3("CN9_REVISA")[1])
	Default _cCliente := ""
	Default _cLoja := ""
	
	_cQuery := " SELECT " + CRLF
	_cQuery += "	 CN9.R_E_C_N_O_ CN9REC " + CRLF
	_cQuery += "	,CNC.R_E_C_N_O_ CNCREC " + CRLF
	_cQuery += " FROM " + RetSqlName("CN9") + " CN9 " + CRLF
	_cQuery += CRLF
	_cQuery += " INNER JOIN " + RetSqlName("CNC") + " CNC ON " + CRLF
	_cQuery += "	CNC.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND CNC.CNC_FILIAL = '" + xFilial("CNC") + "' " + CRLF
	_cQuery += "	AND CNC.CNC_NUMERO = CN9.CN9_NUMERO " + CRLF
	_cQuery += "	AND CNC.CNC_REVISA = CN9.CN9_REVISA " + CRLF
	If !Empty(_cCliente) .And. !Empty(_cLoja)
		_cQuery += "	AND CNC.CNC_CLIENT = '" + _cCliente + "' " + CRLF
		_cQuery += "	AND CNC.CNC_LOJACL = '" + _cLoja + "' " + CRLF
	EndIf
	_cQuery += "	AND CNC.CNC_TIPCLI = '01' " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	CN9.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND CN9.CN9_FILIAL = '" + xFilial("CN9") + "' " + CRLF
	If !Empty(_cContrato)
		_cQuery += "	AND CN9.CN9_NUMERO = '" + _cContrato + "' " + CRLF
		_cQuery += "	AND CN9.CN9_REVISA = '" + _cRevisa + "' " + CRLF
	EndIf
	
	_aDados := U_GV001X23(_cQuery)
	
	If !Empty(_aDados)
		_lAchou := .t.
		CN9->(DbGoTo(_aDados[1][1]))
		RegToMemory("CN9",.F.,.F.)

		CNC->(DbGoTo(_aDados[1][2]))
		RegToMemory("CNC",.F.,.F.)
	EndIf
	
Return _lAchou

/*----------------------------------------------/
Nome:  U_GV007A01
Data.: 09.2009
Autor: Denardi

Descricao:
Tela
----------------- Alteracao----------------------
Data.: 18/03/10
Autor: Denardi
Descricao: Alteracao nas variaveis cSeek e cWhile
para tambem contemplar no filtro o no.do contrato
/-----------------------------------------------*/
User Function GV007A01(_cContrato)

	Local aSize := {}
	Local aButtons := {}
	Local aObjects := {}
	Local aInfo := {}
	Local aPosObj := {}
	Local aAlter := {}  // Campos que poderão ser alterados na tela de manutenção de contratos

	Local lConfirm := .F.

	Local nPosDel := 0
	Local nGetd := 0

	Local cLinOk := "AllwaysTrue"    // Funcao executada para validar o contexto da linha atual do aCols
	Local cTudoOk := "AllwaysTrue"    // Funcao executada para validar o contexto geral da MsNewGetDados (todo aCols)
	Local cAnoRef := StrZero(Year(dDataBase),4)
	Local cProposta := ""

	Local oGroup

	Private oDlg
	Private oGetAno
	Private oGetInd
	Private oSrvMax
	Private oGetMet
	Private oGetExc
	
	Private lCancel := .F.

	Private aLog := {}
	Private aHeadAno := {}
	Private aColsAno := {}
	Private aHeadInd := {}
	Private aColsInd := {}
	Private aHeadMet := {}
	Private aColsMet := {}
	Private aHeadExc := {}
	Private aColsExc := {}
	Private aMetricas := {}
	Private aRecExcec := {}

	Private nPosAno := 1

	Private cCN9Alias := GetNextAlias()
	Private cSYPAlias := GetNextAlias()
	Private cTpReceita := "1"
	Private cAnoInicial := StrZero(Year(dDataBase),4)

	Private _lExcecOk := .T.
	Private _lMetriAlt := .F.
	Private _lChangZAL := .F.
	Private _lChangZAX := .F.
	Private _lChangZAW := .F.
	Private _lDocEntCp := .T.
	
	Default _cContrato	:= "CON" + _Cliente

	// Estrutura do array aMetricas
	// 1 - Excecoes
	// 1,1 a 1,99 - Campos da tabela ZAW
	// 2 - Ano de Referencia
	// 2,1, - Ano
	// 2,1,1 - Ano
	// 2,1,2 - Total das Metricas Informadas
	// 2,1,3 - Total das Metricas Reajustadas
	// 2,1,2 - Indices das Metricas
	// 2,1,2,1 a 2,1,2,99 - Campos do tabela ZAX
	// 2,1,3 - Metricas
	// 2,1,3,1 a 2,1,3,99 - Campos da tabela ZAL

	// Monta Tela
	If _nOpcao <> 1 .And. _nOpcao <> 6 		// Alteracao
		nGetd := GD_INSERT+GD_UPDATE+GD_DELETE
	Else
		nGetd := 0
	EndIf
	
	U_TCORP_REV(_Cliente,@_Versao)
	
	// Carrega os itens de contrato atuais
	If Empty(aProdCorp)
		aProdCorp := U_GV001X06(_Cliente,_Loja,SubStr(cSiteFat,1,Len(cEmpAnt)),SubStr(cSiteFat,Len(cEmpAnt)+1,Len(cFilAnt)))
	EndIf
	
	If !Empty(aProdCorp)
		cProposta := aProdCorp[1][9]
	EndIf

	If Empty(cProposta)
		cProposta := U_GV001X11(_Cliente,_Loja)
	EndIf

	// Verifica o tipo de receita para calcular os reajustes e incremento
	cTpReceita := U_GV001X10( _Cliente, _Loja, cProposta )

	If Empty(cTpReceita)
		cTpReceita := "1"
	Endif

	// Seleciona o ano inicial do reajuste dos indices
	cAnoInicial := U_GV001X14(_Cliente, _Loja, aProdCorp)

	// Faz o calculo automatico de dimensoes de objetos
	aSize := MsAdvSize()
	AAdd( aObjects, { 100, 100, .T., .T. } )
	aInfo := { aSize[ 1 ], aSize[ 2 ], aSize[ 3 ], IIf(SetMdiChild(),aSize[ 4 ]-30,aSize[ 4 ]), 5, 5 }
	aPosObj 	:= MsObjSize( aInfo, aObjects,.T.)

	// Montagem da Tela
	DEFINE MSDIALOG oDlg TITLE STR0014 + " - " + _Cliente + "-" + Posicione("SA1",1,xFilial('SA1') + _Cliente + _Loja,"A1_NOME")  FROM aSize[7],0 To IIf(SetMdiChild(),aSize[6]-30,aSize[6]),aSize[5] PIXEL	// "Contratos Modalidade Corporativo"

	// Carrega as variaveis aHeader e aCols para Excecoes, Ano Metrica, Metricas e Indices
	If _nOpcao == 6
		U_GV001X12(_Cliente,_Loja)
		U_GV001X13(_Cliente,_Loja,aProdCorp)
	EndIf

	U_GV007A10()			// Carrega os lancamentos de Excecoes
	U_GV007A07(@cAnoRef)	// Carrega os lancamentos totalizadores dos anos

	// Montagem da GetDados - Ano Referencia
	@ aPosObj[1,1], aPosObj[1,2] GROUP oGroup TO aPosObj[1,3]*0.37,((aPosObj[1,4]/3)-5) LABEL STR0034 + " " + cAnoRef + " " OF oDlg PIXEL	// "Ano Referencia"
	oGetAno := MsNewGetDados():New(aPosObj[1,1]+7,aPosObj[1,2]+2,(aPosObj[1,3]*0.37)-2,((aPosObj[1,4]/3)-7),nGetd,"U_GV007A28()",,,aAlter,,,,,"U_GV007A06(2,oGetAno)",oDlg,aHeadAno,@aColsAno)
	oGetAno:oBrowse:bChange:={|| U_GV007A11(@cAnoRef),oGroup:cCaption:= STR0034 + " " + cAnoRef + " " ,oGroup:Refresh() }	// "Ano Referencia"


	// Montagem da GetDados - Indices para Calculo
	@ aPosObj[1,1], ((aPosObj[1,4]/3)+5) TO aPosObj[1,3]*0.37,aPosObj[1,4] LABEL STR0035 PIXEL OF oDlg 	// "Índices para Cálculo"
	oGetInd := MsNewGetDados():New((aPosObj[1,1]+7),((aPosObj[1,4]/3)+7),(aPosObj[1,3]*0.37)-2,aPosObj[1,4]-2,nGetd,,,,,,,"U_GV007A29()",,"U_GV007A06(4,oGetInd)",oDlg,aHeadInd,@aColsInd)


	// Montagem da GetDados - Metricas
	@ (aPosObj[1,3]*0.37)+5 ,aPosObj[1,2] TO aPosObj[1,3]*0.8,aPosObj[1,4] LABEL STR0011 PIXEL OF oDlg	//"Métricas"
	oGetMet := MsNewGetDados():New((aPosObj[1,3]*0.37)+12,aPosObj[1,2]+2,(aPosObj[1,3]*0.8)-2,aPosObj[1,4]-2,nGetd,,,,,,9999,"U_GV007A27()",,"U_GV007A06(1,oGetMet)",oDlg,aHeadMet,@aColsMet) //
	oGetMet:oBrowse:BLDBLCLICK := {|x,y| MySetField(x,y,oGetMet) }

	// Montagem da GetDados - Excecoes
	@ (aPosObj[1,3]*0.8)+5 ,aPosObj[1,2] TO aPosObj[1,3]+7,aPosObj[1,4] LABEL STR0013 PIXEL OF oDlg	// "Exceções"
	oGetExc := MsNewGetDados():New((aPosObj[1,3]*0.8)+12,aPosObj[1,2]+2,aPosObj[1,3]+5,aPosObj[1,4]-2,nGetd,"_lExcecOk := U_GV007A26()",,,,,,"U_GV007A30()",,"U_GV007A06(3,oGetExc)",oDlg,aHeadExc,@aColsExc)

	// CRM
	If _nOpcao<>6
		Aadd( aButtons, {"FORM" 	,{ || MsgRun( STR0037 , STR0038, ;	// "Recalculo Indices de Reajustes e Métricas" ### "Processando ... "
		( (cAnoRef := oGetAno:aCols [ 1, 1 ],oGroup:cCaption:= (STR0034 + " " + cAnoRef + " ") ,oGroup:Refresh()), { || U_GV007A14(.T.) } )) },STR0039 } )	// "Ano Referencia" ### "Recalc Reaj"
		Aadd( aButtons, {"OBJETIVO"	,{ || (U_GV007A17(_Cliente,_Loja),U_GV007A11(@cAnoRef),U_GV007A24(@cAnoRef),oGroup:cCaption:= STR0034 + " " + cAnoRef + " " ,oGroup:Refresh()) 	},STR0040} )	// "Ano Referencia" ### "Calculos"
		Aadd( aButtons, {"OBJETIVO"	,{ || U_TGCVA013() },"Log Métricas"})
		Aadd( aButtons, {"OBJETIVO"	,{ || U_GV007A31() },"Exporta Metrica"})	// "Ano Referencia" ### "Calculos"
	EndIf

	Aadd(aButtons,{"EDIT",{||U_TGCVA039(_Cliente,_Loja)},"Memoria de calculo"} )
	Aadd(aButtons,{"EDIT",{|| GC007INAT(oGetMet, oGetInd)},"Inativar contrato corporativo"} )

	VericaExce(_Cliente,_Loja,"05")

	ACTIVATE MSDIALOG oDlg ON INIT EnchoiceBar( oDlg,{|| ( If(_fVerifica() .And. U_GV007A13(@lConfirm,.F.,_nOpcao,cAnoRef),oDlg:End(),) )},{||If(MsgNoYes("Deseja realmente sair?","Contrato Corporativo"),oDlg:End(),)},,aButtons) CENTERED

	If lConfirm .And. nGetd <> 1
		U_GV007A02()
		If _lMetriAlt
			MsgRun( STR0079,STR0080 ,{|| U_TGCVA034(_Cliente,_Loja)} )	// "Recalculo Incremento" ### "Aguarde ..."
		EndIf
	EndIf
	
	If _nOpcao == 6
		IIf(Select("CN9")>0,CN9->(DbCloseArea()),)
		IIf(Select("SYP")>0,SYP->(DbCloseArea()),)
	EndIf

Return

/*/{Protheus.doc} U_GV007A02
Função para gravar os registros nas tabelas do corporativo (ZAL,ZAW,ZAX)

<AUTHOR> Ribeiro
@Since 14/04/2014
@Since 11.5

@obs Função foi alterada de Static Function para function e refeita para atender o projeto do corporativo. Porem seu conceito inicial não foi alterado.

@Param _lGravaZAW, logico, se grava a ZAW.
@Param _lGravaZAX, logico, se grava a ZAX.
@Param _lGravaZAL, logico, se grava a ZAL.
/*/

User Function GV007A02(_lGravaZAW,_lGravaZAX,_lGravaZAL,_lVA011)

	Local _nCount 	:= 0

	Default _lGravaZAW := .T.
	Default _lGravaZAX := .T.
	Default _lGravaZAL := .T.

	Begin Transaction
		SA1->(DbSetOrder(1))
		SA1->(DbSeek(xFilial("SA1")+_Cliente+_Loja))

		If _lGravaZAW .And. _lChangZAW
			SfGravaZAW()
			_lChangZAW := .F.
		EndIf

		If _lGravaZAX .And. _lChangZAX
			SfGravaZAX()
			_lChangZAX := .F.
		EndIf

		If _lGravaZAL .And. _lChangZAL
			SfGravaZAL(_lVA011)
			_lChangZAL := .F.
		EndIf

		For _nCount := 1 to Len(aLog)
			// VERIFICAR DE VAI SER NECESSARIA A EXECUÇÃO
			//TDISaveMotivo(aLog[_nCount][1],aLog[_nCount][2],aLog[_nCount][3],aLog[_nCount][4],CN9->CN9_NUMERO,CN9->CN9_REVISA,FunName())
		Next
	End Transaction

Return

/*/{Protheus.doc} SfGravaZAW
Função para gravar a tabela de exceções ZAW.

<AUTHOR> Ribeiro
@Since 14/04/2014
@Since 11.5
/*/

Static Function SfGravaZAW()

	Local lChangZAW := .F.

	Local nCount1 := 0
	Local nCount2 := 0

	// Gravacao da tabela de excecoes - aMetricas[1]
	For nCount1 := 1 To Len(aMetricas[1])
		// Se o registro não foi deletado
		If !aMetricas[1][nCount1][Len(aHeadExc)+1]
			If !Empty(aMetricas [1][nCount1][2])
				// Verifca se e alteracao ou inclusao.
				lChangZAW := nCount1 <= Len(aRecExcec)
				If lChangZAW
					ZAW->(DbGoTo(aRecExcec[nCount1]))

					// Se a data de geração já foi preenchida, significa já existe bonificação.
					// Por isso deve ser alterado a data de vigencia lá tambem.
					If !Empty(ZAW->ZAW_DATGER) .And. AllTrim(ZAW->ZAW_CODMOT) == "02"
						AtualizPbf(aMetricas[1][nCount1][aScan(aHeadExc,{|x| Alltrim(x[2])=="ZAW_VIGENC"})])
					EndIf

				EndIf

				RecLock("ZAW",!lChangZAW)
					ZAW->ZAW_FILIAL := xFilial("ZAW")
					ZAW->ZAW_CLIENT := _Cliente
					ZAW->ZAW_LOJA := _Loja
					ZAW->ZAW_CONTRA := _Contrato
					ZAW->ZAW_REVISA := _Versao
					ZAW->ZAW_INCLUS := DATE()
	
					For nCount2 := 1 To Len(aHeadExc)
						If (aHeadExc[nCount2][10] <> "V" .And. aHeadExc[nCount2][10] <> "M")
							ZAW->(FieldPut(FieldPos(Trim(aHeadExc[nCount2][2])),aMetricas[1][nCount1][nCount2]))
						EndIf
					Next
				ZAW->(MsUnlock())
				FkCommit()
			EndIf
		Else // Se foi deletado
			// Verifica se registro já existia na tabela.
			If nCount1 <= Len(aRecExcec)
				ZAW->(DbGoTo(aRecExcec[nCount1]))
				RecLock("ZAW",.F.)
					ZAW->(DbDelete())
				ZAW->(MsUnlock())
				FkCommit()
			EndIf
		Endif
	Next

Return

/*/{Protheus.doc} SfGravaZAX
Função para gravar a tabela de indice ZAX.

<AUTHOR> Ribeiro
@Since 14/04/2014
@Since 11.5
/*/

Static Function SfGravaZAX()

	Local cAnoRef := ""

	Local lFoundZAX := .F.

	Local nCount1 := 0
	Local nCount2 := 0
	Local nCount3 := 0
	Local nPosProd := aScan(aHeadInd,{|x| Alltrim( x[2] ) == "ZAX_PRCDU" } )
	Local nPosMtr1 := aScan(aHeadInd,{|x| Alltrim( x[2] ) == "ZAX_REFMT1"} )
	Local nPosItem := aScan(aHeadInd,{|x| Alltrim( x[2] ) == "ZAX_ITEM"} )
	Local nPosZAXrec := aScan(aHeadInd,{|x| Alltrim( x[2] ) == "ZAX_REC_WT"} )

	ZAX->(DbSetOrder(6))	// ZAX_FILIAL+ZAX_STATUS+ZAX_CONTRA+ZAX_REVISA+ZAX_ANOREF+ZAX_PRCDU+ZAX_ITEM                                                                                       
	For nCount1 := 1 To Len(aMetricas[2])
		cAnoRef := aMetricas[2][nCount1][1][1]

		// Gravacao da tabela de Indices - aMetricas[2]
		For nCount2 := 1 To Len(aMetricas[2][nCount1][2])
			If !aMetricas[2][nCount1][2][nCount2][Len(aHeadInd)+1] .And. !Empty(aMetricas[2][nCount1][2][nCount2][nPosProd]) .And.;
					aMetricas[2][nCount1][2][nCount2][nPosMtr1] > 0.00
				
				If nPosZAXrec == 0 
					lFoundZAX := ZAX->(DbSeek(xFilial("ZAX")+"A"+_Contrato+_Versao+cAnoRef+aMetricas[2][nCount1][2][nCount2][nPosProd]+aMetricas[2][nCount1][2][nCount2][nPosItem]))
				Else
					If Empty(AMETRICAS[2][NCOUNT1][2][NCOUNT2][nPosZAXrec])
						lFoundZAX := ZAX->(DbSeek(xFilial("ZAX")+"A"+_Contrato+_Versao+cAnoRef+aMetricas[2][nCount1][2][nCount2][nPosProd]+aMetricas[2][nCount1][2][nCount2][nPosItem]))
					Else
						ZAX->(DbGoto(AMETRICAS[2][NCOUNT1][2][NCOUNT2][nPosZAXrec]))
						lFoundZAX	:= .T.
					EndIf
				EndIf
				
				If RecLock("ZAX",!lFoundZAX)
					If !lFoundZAX
						ZAX->ZAX_FILIAL := xFilial("ZAX")
						ZAX->ZAX_ANOREF := cAnoRef
						ZAX->ZAX_CLIENT := _Cliente
						ZAX->ZAX_LOJA := _Loja
						ZAX->ZAX_CONTRA := _Contrato
						ZAX->ZAX_REVISA := _Versao
					EndIf
	
					For nCount3 := 1 To Len(aHeadInd)
						If aHeadInd[nCount3][10] <> "V"
							ZAX->(FieldPut(FieldPos(Trim(aHeadInd[nCount3][2])),aMetricas[2][nCount1][2][nCount2][nCount3]))
						EndIf
					Next
					ZAX->(MsUnlock())
				EndIf
				FkCommit()
			EndIf
		Next
	Next

Return

/*/{Protheus.doc} SfGravaZAL
Função para gravar a tabela de metricas ZAL.

<AUTHOR> Ribeiro
@Since 14/04/2014
@Since 11.5
/*/

Static Function SfGravaZAL(_lVA011)

	Local cAnoRef := ""
	Local _cCnpjAgr := ""

	Local lFoundZAL := .F.

	Local nCount1 := 0
	Local nCount2 := 0
	Local nCount3 := 0
	Local nPosCGC 	:= aScan(aHeadMet,{|x| Alltrim( x[2] ) == "ZAL_CGCAGR"} )
	Local lAjustRot     := GetMv("TI_RV2EMP",,.T.) 
	Local nTamCGCAGR	:= TamSx3("ZAL_CGCAGR")[1]
	Local nTamContra	:= TamSx3("ZAL_CONTRA")[1]
	Local nTamRevisa	:= TamSx3("ZAL_REVISA")[1]
	Local nPosZALrec := aScan(aHeadMet,{|x| Alltrim( x[2] ) == "ZAL_REC_WT"} )
	Default _lVA011 := .F.
	
	_cContrato	:= PadR(_cContrato,nTamContra)
	If _lVA011
		_cRevisa	:= PadR(_cRevisa,nTamRevisa)
	Else
		_Versao		:= PadR(_Versao,nTamRevisa)
	EndIf
		
	ZAL->(DbSetOrder(12))	// ZAL_FILIAL+ZAL_STATUS+ZAL_CONTRA+ZAL_REVISA+ZAL_CGCAGR+ZAL_ANOREF
	For nCount1 := 1 To Len(aMetricas[2])
		cAnoRef := aMetricas[2][nCount1][1][1]

		// Gravacao da tabela de Metricas - aMetricas[3]
		For nCount2 := 1 To Len(aMetricas[2][nCount1][3])
			If !aMetricas[2][nCount1][3][nCount2][Len(aHeadMet)+1]
			
				_cCnpjAgr := PadR(StrTran(StrTran(StrTran(aMetricas[2][nCount1][3][nCount2][nPosCGC],'.',''),'/',''),'-',''),nTamCGCAGR)
				/*
				If _lVA011
					lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+_cContrato+_cRevisa+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
				Else
					lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+_Contrato+_Versao+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
				EndIf*/
				
				If nPosZALrec == 0
					If _lVA011
						lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+ "A" + _cContrato+_cRevisa+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
						
						If !lFoundZAL
							lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+ "C" + _cContrato+_cRevisa+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
						EndIf
					Else
						lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+ "A" + _Contrato+_Versao+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
						If !lFoundZAL
							lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+ "C" + _Contrato+_Versao+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
						EndIf
					EndIf
				Else
					If Empty(aMetricas[2][nCount1][3][nCount2][nPosZALrec])
						If _lVA011
							lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+ "A" + _cContrato+_cRevisa+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
							If !lFoundZAL
								lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+ "C" + _cContrato+_cRevisa+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
							EndIf
						Else
							lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+ "A" + _Contrato+_Versao+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
							If !lFoundZAL
								lFoundZAL := ZAL->(DbSeek(xFilial("ZAL")+ "C" + _Contrato+_Versao+_cCnpjAgr+cAnoRef+aMetricas[2][nCount1][3][nCount2][6]))
							EndIf
						EndIf
					Else
						lFoundZAL := .T.
						ZAL->(DbGoTo(aMetricas[2][nCount1][3][nCount2][nPosZALrec]))
					EndIf
				EndIf
				
				If RecLock("ZAL",!lFoundZAL)
					If !lFoundZAL
						ZAL->ZAL_FILIAL := xFilial("ZAL")
						ZAL->ZAL_ANOREF := cAnoRef
						ZAL->ZAL_CLIENT := _Cliente
						ZAL->ZAL_LOJA := _Loja

						If _lVA011
							If lAjustRot .And. empty(_cRevisa)
								_cRevisa := U_TiGetContr(_cContrato)
							EndIf
							_cRevisa	:= PadR(_cRevisa,nTamRevisa)
							
							ZAL->ZAL_CONTRA := _cContrato
							ZAL->ZAL_REVISA := _cRevisa
						Else
							If lAjustRot .And. empty(_Versao)
								_Versao := U_TiGetContr(_Contrato)
							EndIf
							_Versao		:= PadR(_Versao,nTamRevisa)
							
							ZAL->ZAL_CONTRA := _Contrato
							ZAL->ZAL_REVISA := _Versao
						EndIf
	
						ZAL->ZAL_CGCPRI := SA1->A1_CGC
					EndIf
	
					For nCount3 := 1 To Len(aHeadMet)
						If aHeadMet[nCount3][10] <> "V"
							If aHeadMet[nCount3][2] <> "ZAL_CGCAGR"
								ZAL->(FieldPut(FieldPos(Trim(aHeadMet[nCount3][2])),aMetricas[2][nCount1][3][nCount2][nCount3]))
							Else
								ZAL->(FieldPut(FieldPos(Trim(aHeadMet[nCount3][2])),_cCnpjAgr))
							EndIf
						Endif
					Next
					ZAL->(MsUnlock())
				EndIf
				FkCommit()
			EndIf
		Next

	Next

Return

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A03³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Validação da Metrica Informada e Totalizadores do Ano      ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A03()                                              ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Parametros³   				                                          ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/

User Function GV007A03()

	Local lRet 		:= .T.
	Local nPosAnoR 	:= aScan(aHeadAno,{|x| Alltrim(x[2])=="ZAL_ANOREF"})
	Local nPosMTIA 	:= aScan(aHeadAno,{|x| Alltrim(x[2])=="ZAL_MTRINF"})
	Local nPosMTRA 	:= aScan(aHeadAno,{|x| Alltrim(x[2])=="ZAL_MTRREA"})
	Local nPosMTIM 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRINF"})
	Local nPosMTRM 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRREA"})
	Local nPosMTDM 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRDIP"})
	Local nPosNome 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_NOMAGR"})
	Local nPosCGC	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_CGCAGR"})
	Local nPosInd	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_INDICE"})
	Local nPosStat 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_STATUS"})
	Local nPosTp 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_TPCNPJ"})
	Local cAnoCorp	:= GETMV("TDI_ANOCOR",,"2010")
	Local cMaiorMT	:= GETMV("MV_CPMAIOR",,.T.)
	Local nY 		:= 0
	Local nMTReaAtu	:= 0
	Local nMTInfAtu	:=0
	Local nMTInfCIM	:= 0
	Local nMTInfCDM	:= 0

	If oGetMet:aCols[oGetMet:nAT][nPosMTDM] > 0
		Help(,,"Help",,STR0041,1,0)	// "A métrica informada não poderá ser alterada"
		Return(.F.)
	Else
		For nY := 1 to Len(oGetMet:aCols)
			If !Atail(oGetMet:aCols[nY])
				If oGetMet:nAT <> nY
					If oGetMet:aCols[nY][nPosStat] == 'A'
						nMTInfCIM := nMTInfCIM + oGetMet:aCols[nY][nPosMTIM]
						nMTInfCDM := nMTInfCDM + oGetMet:aCols[nY][nPosMTDM]
					EndIf
				Else
					If oGetMet:aCols[oGetMet:nAT][nPosStat] == 'A'
						nMTInfCIM := nMTInfCIM + &(READVAR())
						nMTInfCDM := nMTInfCDM + oGetMet:aCols[oGetMet:nAT][nPosMTDM]
					EndIf
				EndIf
			EndIf
		Next

		If nMTInfCIM > nMTInfCDM
			For nY := 1 to Len(oGetMet:aCols)
				If !Atail(oGetMet:aCols[nY])
					If oGetMet:nAT <> nY
						If oGetMet:aCols[nY][nPosStat] == 'A'
							oGetMet:aCols[nY][nPosMTRM] := IIf(oGetAno:aCols[oGetAno:nAT][nPosAnoR] < cAnoCorp,U_GV001X01(oGetMet:aCols[nY][nPosMTIM],oGetAno:aCols[oGetAno:nAT][nPosAnoR],cAnoCorp,,oGetMet:aCols[nY][nPosInd]),oGetMet:aCols[nY][nPosMTIM])
							nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTIM]
							nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
						EndIf
					Else
						If oGetMet:aCols[oGetMet:nAT][nPosStat] == 'A'
							oGetMet:aCols[nY][nPosMTRM] := IIf(oGetAno:aCols[oGetAno:nAT][nPosAnoR] < cAnoCorp,U_GV001X01(&(READVAR()),oGetAno:aCols[oGetAno:nAT][nPosAnoR],cAnoCorp,,oGetMet:aCols[oGetMet:nAT][nPosInd]),&(READVAR()))
							nMTInfAtu := nMTInfAtu + &(READVAR())
							nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
						EndIf
					EndIf
				EndIf
			Next
		Else
			For nY := 1 to Len(oGetMet:aCols)
				If !Atail(oGetMet:aCols[nY])
					If oGetMet:nAT <> nY
						If oGetMet:aCols[nY][nPosStat] == 'A'
							oGetMet:aCols[nY][nPosMTRM] := IIf(oGetAno:aCols[oGetAno:nAT][nPosAnoR] < cAnoCorp,U_GV001X01(oGetMet:aCols[nY][nPosMTDM],oGetAno:aCols[oGetAno:nAT][nPosAnoR],cAnoCorp,,oGetMet:aCols[nY][nPosInd]),oGetMet:aCols[nY][nPosMTDM])
							nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTDM]
							nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
						EndIf
					Else
						If oGetMet:aCols[oGetMet:nAT][nPosStat] == 'A'
							oGetMet:aCols[nY][nPosMTRM] := IIf(oGetAno:aCols[oGetAno:nAT][nPosAnoR] < cAnoCorp,U_GV001X01(oGetMet:aCols[nY][nPosMTDM],oGetAno:aCols[oGetAno:nAT][nPosAnoR],cAnoCorp,,oGetMet:aCols[oGetMet:nAT][nPosInd]),oGetMet:aCols[nY][nPosMTDM])
							nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTDM]
							nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
						EndIf
					EndIf
				EndIf
			Next
		EndIf

		oGetAno:aCols[oGetAno:nAT][nPosMTIA] := nMTInfAtu
		oGetAno:aCols[oGetAno:nAT][nPosMTRA] := nMTReaAtu

		For nY := 1 to Len(oGetMet:aCols)
			If !Atail(oGetMet:aCols[nY])
				oGetMet:aCols[nY,nPosCGC] := STRTRAN(STRTRAN(STRTRAN(oGetMet:aCols[nY,nPosCGC],'.',''),'/',''),'-','')
				If oGetMet:aCols[ nY ][ nPosTP ] == "1"
					oGetMet:aCols[ nY ][nPosCGC] := Transform(oGetMet:aCols[ nY ][nPosCGC],IIF(cPaisLoc="BRA","@R 99.999.999/9999-99",IIF(cPaisloc="ARG","@R 99-99999999-9","@!")))
				ElseIf oGetMet:aCols[ nY ][ nPosTP ] == "2"
					oGetMet:aCols[ nY ][nPosCGC] := Transform(oGetMet:aCols[ nY ][nPosCGC],"@R 999.999.999-99")
				Else
					oGetMet:aCols[ nY ][nPosCGC] := Transform(oGetMet:aCols[ nY ][nPosCGC],"@!")
				EndIf
			EndIf
		Next nY

		oGetMet:Refresh()
		oGetAno:Refresh()
	EndIf

Return( lRet )

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A04³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Validação da Metrica DIPJ  e Totalizadores do Ano          ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A04()                                              ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Parametros³   				                                          ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A04()

	Local lRet 		:= .T.
	Local nPosAnoR 	:= aScan(aHeadAno,{|x| Alltrim(x[2])=="ZAL_ANOREF"})
	Local nPosMTIA 	:= aScan(aHeadAno,{|x| Alltrim(x[2])=="ZAL_MTRINF"})
	Local nPosMTRA 	:= aScan(aHeadAno,{|x| Alltrim(x[2])=="ZAL_MTRREA"})
	Local nPosMTIM 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRINF"})
	Local nPosMTRM 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRREA"})
	Local nPosMTDM 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRDIP"})
	Local nPosCGC	 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_CGCAGR"})
	Local nPosTp 		:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_TPCNPJ"})
	Local nPosNome 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_NOMAGR"})
	Local nPosInd 		:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_INDICE"})
	Local nPosStat 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_STATUS"})
	Local nPosOri	 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_ORIGEM"})
	Local cAnoCorp		:= GETMV("TDI_ANOCOR",,"2010")
	Local cMaiorMT		:= GETMV("MV_CPMAIOR",,.T.)
	Local nY 			:= 0
	Local nMTReaAtu := 0
	Local nMTInfAtu := 0
	Local nMTInfCIM := 0
	Local nMTInfCDM := 0

	U_GV007A27()

	If FunName() == "U_TGCVA011"
		U_GV011A01()
	EndIf

	For nY := 1 to Len(oGetMet:aCols)
		If !Atail(oGetMet:aCols[nY])
			If oGetMet:nAT <> nY
				If oGetMet:aCols[nY][nPosStat] == 'A'
					nMTInfCIM := nMTInfCIM + oGetMet:aCols[nY][nPosMTIM]
					nMTInfCDM := nMTInfCDM + oGetMet:aCols[nY][nPosMTDM]
				EndIf
			Else
				If oGetMet:aCols[oGetMet:nAT][nPosStat] == 'A'
					nMTInfCIM := nMTInfCIM + oGetMet:aCols[oGetMet:nAT][nPosMTIM]
					nMTInfCDM := nMTInfCDM + &(READVAR())
					oGetMet:aCols[oGetMet:nAT][nPosOri] := 'M'
				EndIf
			EndIf
		EndIf
	Next

	If nMTInfCIM > nMTInfCDM
		For nY := 1 to Len(oGetMet:aCols)
			If !Atail(oGetMet:aCols[nY])
				If oGetMet:nAT <> nY
					If oGetMet:aCols[nY][nPosStat] == 'A'
						oGetMet:aCols[nY][nPosMTRM] := IIf(oGetAno:aCols[oGetAno:nAT][nPosAnoR] < cAnoCorp,U_GV001X01(oGetMet:aCols[nY][nPosMTIM],oGetAno:aCols[oGetAno:nAT][nPosAnoR],cAnoCorp,,oGetMet:aCols[nY][nPosInd]),oGetMet:aCols[nY][nPosMTIM])
						nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTIM]
						nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
					EndIf
				Else
					If oGetMet:aCols[oGetMet:nAT][nPosStat] == 'A'
						oGetMet:aCols[nY][nPosMTRM] := IIf(oGetAno:aCols[oGetAno:nAT][nPosAnoR] < cAnoCorp,U_GV001X01(oGetMet:aCols[nY][nPosMTIM],oGetAno:aCols[oGetAno:nAT][nPosAnoR],cAnoCorp,,oGetMet:aCols[oGetMet:nAT][nPosInd]),oGetMet:aCols[nY][nPosMTIM])
						nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTIM]
						nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
					EndIf
				EndIf
			EndIf
		Next
	Else
		For nY := 1 to Len(oGetMet:aCols)
			If !Atail(oGetMet:aCols[nY])
				If oGetMet:nAT <> nY
					If oGetMet:aCols[nY][nPosStat] == 'A'
						oGetMet:aCols[nY][nPosMTRM] := IIf(oGetAno:aCols[oGetAno:nAT][nPosAnoR] < cAnoCorp,U_GV001X01(oGetMet:aCols[nY][nPosMTDM],oGetAno:aCols[oGetAno:nAT][nPosAnoR],cAnoCorp,,oGetMet:aCols[nY][nPosInd]),oGetMet:aCols[nY][nPosMTDM])
						nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTDM]
						nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
					EndIf
				Else
					If oGetMet:aCols[oGetMet:nAT][nPosStat] == 'A'
						oGetMet:aCols[nY][nPosMTRM] := IIf(oGetAno:aCols[oGetAno:nAT][nPosAnoR] < cAnoCorp,U_GV001X01(&(READVAR()),oGetAno:aCols[oGetAno:nAT][nPosAnoR],cAnoCorp,,oGetMet:aCols[oGetMet:nAT][nPosInd]),&(READVAR()))
						nMTInfAtu := nMTInfAtu + &(READVAR())
						nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
					EndIf
				EndIf
			EndIf
		Next
	EndIf

	oGetAno:aCols[oGetAno:nAT][nPosMTIA] := nMTInfAtu
	oGetAno:aCols[oGetAno:nAT][nPosMTRA] := nMTReaAtu

	For nY := 1 to Len(oGetMet:aCols)
		If !Atail(oGetMet:aCols[nY])
			oGetMet:aCols[nY,nPosCGC] := STRTRAN(STRTRAN(STRTRAN(oGetMet:aCols[nY,nPosCGC],'.',''),'/',''),'-','')
			If oGetMet:aCols[ nY ][ nPosTP ] == "1"
				oGetMet:aCols[ nY ][nPosCGC] := Transform(oGetMet:aCols[ nY ][nPosCGC],IIF(cPaisLoc="BRA","@R 99.999.999/9999-99",IIF(cPaisloc="ARG","@R 99-99999999-9","@!")))//Transform(oGetMet:aCols[ nY ][nPosCGC],"@R 99.999.999/9999-99")
			ElseIf oGetMet:aCols[ nY ][ nPosTP ] == "2"
				oGetMet:aCols[ nY ][nPosCGC] := Transform(oGetMet:aCols[ nY ][nPosCGC],"@R 999.999.999-99")
			Else
				oGetMet:aCols[ nY ][nPosCGC] := Transform(oGetMet:aCols[ nY ][nPosCGC],"@!")
			EndIf
		EndIf
	Next nY

	oGetMet:Refresh()
	oGetAno:Refresh()

Return( lRet )

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A05  ³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Validação(tudoOk) das Metricas na alteração do Ano         ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A05(cAnoVld)                                           ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Parametros³ Ano a ser validado                                         ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A05(cAnoVld)

	Local lRet 		:= .T.
	Local nPosCGC 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_CGCAGR"})
	Local nPosStat 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_STATUS"})
	Local nPosTPCNPJ:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_TPCNPJ"})
	Local nPosNom   := aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_NOMAGR"})
	Local nPosMINF  := aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRINF"})
	Local nPosMDIP  := aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRDIP"})
	Local nPosMDtI  := aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_DTMINF"})
	Local nPosMDtD  := aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_DTDIPJ"})
	Local nPosStatIn:= aScan(aHeadInd,{|x| Alltrim(x[2])=="ZAX_STATUS"})
	Local nPosINDCDU:= aScan(aHeadInd,{|x| Alltrim(x[2])=="ZAX_INDCDU"})
	Local nPosVLMCDU:= aScan(aHeadInd,{|x| Alltrim(x[2])=="ZAX_VLMCDU"})
	Local nPosINDET := aScan(aHeadInd,{|x| Alltrim(x[2])=="ZAX_INDET"})
	Local nPosVLMET := aScan(aHeadInd,{|x| Alltrim(x[2])=="ZAX_VLMET"})
	Local nI		:= 0
	Local nId		:= 0
	Local nId2    := 0
	
	Default cAnoVld := ""

	For nI := 1 to Len(oGetMet:aCols)
		If oGetMet:aCols[nI][nPosStat] == 'A' .And. !(oGetMet:aCols[nI][Len(oGetMet:aCols[nI])]) // .And. (Empty(oGetMet:aCols[nI][nPosCGC]) .And. Empty(oGetMet:aCols[nI][nPosNom]) .And. Empty(oGetMet:aCols[nI][nPosMINF]))
			If !Empty(oGetMet:aCols[nI][nPosCGC]) .Or. !Empty(oGetMet:aCols[nI][nPosNom]) .Or. !Empty(oGetMet:aCols[nI][nPosMINF]) .Or. !Empty(oGetMet:aCols[nI][nPosMDtI]) .Or. !Empty(oGetMet:aCols[nI][nPosMDtD])

				If lRet
					If Empty(oGetMet:aCols[nI][nPosTPCNPJ])
						Help(,,"Help",,STR0047+" "+AllTrim(Str(nI))+" "+STR0048,1,0)	// "O Tipo de CNPJ da linha ### "deve ser preenchido"
						Return(.F.)
					EndIf

					If Empty(oGetMet:aCols[nI][nPosCGC])
						Help(,,"Help",,STR0049+" "+AllTrim(Str(nI))+" "+STR0048,1,0)	// "O CNPJ da linha" ### "deve ser preenchido"
						Return(.F.)
					ElseIf !U_GV007A15(oGetMet:aCols[nI][nPosCGC],nI,cAnoVld)
						If oGetMet:aCols[nI,nPosStat] == 'A'
							Help(,,"Help",,STR0050+" "+AllTrim(Str(nI))+" Ano: " + cAnoVld + "  " +STR0051+" "+oGetMet:aCols[nI][nPosCGC] + " - " + oGetMet:aCols[nI][nPosNom],1,0)// "Corrija o n. do CNPJ da linha" ### "CNPJ"
							Return(.F.)
						EndIf
					EndIf
					If Empty(oGetMet:aCols[nI][nPosNom])
						Help(,,"Help",,STR0052+" "+AllTrim(Str(nI))+" "+STR0048,1,0)	// "O Nome do Agregado da linha" ### "deve ser preenchido"
						Return(.F.)
					EndIf
				EndIf
			EndIf
		EndIf
	Next

	For nId := 1 to Len(oGetInd:aCols)
		If oGetInd:aCols[nId][nPosStatIn] == 'A' .And. !(oGetInd:aCols[nId][Len(oGetInd:aCols[nId])])
			If lRet
				If cAnoInicial == cAnoVld
					If oGetInd:aCols[nId][nPosVLMCDU] <= 0 .And. oGetInd:aCols[nId][nPosVLMET] <= 0 .And. oGetInd:aCols[nId][nPosINDCDU] <= 0 .And. oGetInd:aCols[nId][nPosINDET] <= 0
						Help(,,"Help",,STR0053,1,0)	// "Os Valores Minimos e Indices de CDU/ET não podem ser inválidos"
						Return(.F.)
					EndIf
				EndIf
			EndIf
		EndIf
	Next
Return( lRet )



/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³ U_GV007A06³ Autor ³ Lucas  	      		³ Data ³ 07.10.09 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A06(nGD,oGet1)

	Local nRec 		:= 0
	Local nRetDel	:= .T.
	Local nPosStat 	:= aScan(aHeader,{|x| Alltrim(x[2])=="ZAL_STATUS"})
	Local nPosDtEx 	:= aScan(aHeader,{|x| Alltrim(x[2])=="ZAL_DTEXAG"})
	Local nPosNome 	:= aScan(aHeader,{|x| Alltrim(x[2])=="ZAL_NOMAGR"})
	Local nPosCGCA	:= aScan(aHeader,{|x| Alltrim(x[2])=="ZAL_CGCAGR"})
	Local nZAXPCDU	:= AScan(aHeadInd,{|x| Alltrim(x[2])== "ZAX_PRCDU" })
	Local nZAXMETR	:= AScan(aHeadInd,{|x| Alltrim(x[2])== "ZAX_METRIC"})
	Local _nPsVlrAno := 0
	Local _nPsVlrMet := 0

	Local _dDtAtual := dDataBase //Date()

// Incluido esta verificação devido ao erro que acontece quando se utilizada prepare environment via Perfil do Cliente
// no momento da visualizacao pelo CRM
	If "_REC_WT" $ aHeader[Len(aHeader),2 ]
		nRec := aCols[oGet1:nAT, LEN(aHeader)]
	Else
		nRec := Len(oGet1:aCols)
	Endif

	If nGD == 1 .AND. (nRec > 0)		// Apenas se tiver RECNO

		If !ATail(aCols[oGet1:nAT])
			If Empty(aCols[oGet1:nAT,nPosDtEx]) .And. aCols[oGet1:nAT,nPosStat] == 'A'
				If MsgYesNo(STR0054)	// "Este registro não poderá ser excluído. Deseja torna-lo Inativo?"
					GDFieldPut( "ZAL_DTEXAG",DDATABASE, oGet1:nAT )
					GDFieldPut( "ZAL_STATUS", 'I'  , oGet1:nAT )
					oGet1:oBrowse:Refresh(.T.)
					nRetDel	:= .F.
					aadd(aLog,{STR0042,STR0055," "," ",_Versao,_Picpad,"",oGet1:aCols[oGet1:nAT][nPosCGCA],oGet1:aCols[oGet1:nAT][nPosNome],"","",oGetAno:aCols[oGetAno:oBrowse:nAT,1],""})	// "ALTERAÇÃO DE MÉTRICA" ### "Status alterado para Inativo"
				Else
					nRetDel	:= .F.
				EndIf
			Else
				If MsgYesNo(STR0056)	// "Deseja tornar este registro Ativo?"
					GDFieldPut( "ZAL_DTEXAG",CTOD(""), oGet1:nAT )
					GDFieldPut( "ZAL_STATUS",'A'     , oGet1:nAT )
					oGet1:oBrowse:Refresh(.T.)
					nRetDel	:= .F.
					aadd(aLog,{STR0042,STR0057," "," ",_Versao,_Picpad,"",oGetMet:aCols[oGetMet:nAT][nPosCGCA],oGetMet:aCols[oGetMet:nAT][nPosNome],"","",oGetAno:aCols[oGetAno:oBrowse:nAT,1],""})	// "ALTERAÇÃO DE MÉTRICA" ### "Status alterado para Ativo"
				Else
					nRetDel	:= .F.
				EndIf
			EndIf
		Else
			If MsgYesNo(STR0056)	// "Deseja tornar este registro Ativo?"
				GDFieldPut( "ZAL_DTEXAG", CTOD(""), oGet1:nAT )
				GDFieldPut( "ZAL_STATUS", 'A'     , oGet1:nAT )
				nRetDel	:= .F.
				aadd(aLog,{STR0042,STR0057," "," ",_Versao,_Picpad,"",oGetMet:aCols[oGetMet:nAT][nPosCGCA],oGetMet:aCols[oGetMet:nAT][nPosNome],"","",oGetAno:aCols[oGetAno:oBrowse:nAT,1],""})	//  "ALTERAÇÃO DE MÉTRICA" ### "Status alterado para Ativo"
			Else
				nRetDel	:= .F.
			EndIf
		EndIf

	ElseIf nGD == 1

		_nPsVlrAno := Ascan(aHeadAno,{|x| AllTrim(x[2]) == "ZAL_MTRINF" })
		_nPsVlrMet := Ascan(aHeadMet,{|x| AllTrim(x[2]) == "ZAL_MTRINF" })

		If !Atail(oGetMet:aCols[oGetMet:oBrowse:nAT])
			oGetAno:aCols[oGetAno:oBrowse:nAT][_nPsVlrAno] -= oGetMet:aCols[oGetMet:oBrowse:nAT][_nPsVlrMet]
		Else
			oGetAno:aCols[oGetAno:oBrowse:nAT][_nPsVlrAno] += oGetMet:aCols[oGetMet:oBrowse:nAT][_nPsVlrMet]
		EndIf

		_nPsVlrAno := Ascan(aHeadAno,{|x| AllTrim(x[2]) == "ZAL_MTRREA" })
		_nPsVlrMet := Ascan(aHeadMet,{|x| AllTrim(x[2]) == "ZAL_MTRREA" })

		If !Atail(oGetMet:aCols[oGetMet:oBrowse:nAT])
			oGetAno:aCols[oGetAno:oBrowse:nAT][_nPsVlrAno] -=	 oGetMet:aCols[oGetMet:oBrowse:nAT][_nPsVlrMet]
		Else
			oGetAno:aCols[oGetAno:oBrowse:nAT][_nPsVlrAno] +=	 oGetMet:aCols[oGetMet:oBrowse:nAT][_nPsVlrMet]
		EndIf

		oGetAno:Refresh()

	ElseIf nGD == 2 .AND. (nRec > 0)
		nRetDel	:= .F.
	ElseIf nGD == 3 .AND. (nRec > 0)
		If !((!(AllTrim(GDFieldGet("ZAW_CODMOT")) $ "03|04|99") .And. Empty(GDFieldGet("ZAW_DATGER"))) .Or.;
				((AllTrim(GDFieldGet("ZAW_CODMOT")) $ "03|04|99") .And. GDFieldGet("ZAW_INCLUS") == _dDtAtual))
			nRetDel	:= .F.
		EndIf
	ElseIf nGD == 4
		aadd(aLog,{STR0058,STR0059," "," ",_Versao,_Picpad,"",oGetInd:aCols[oGetInd:nAT][nZAXPCDU],oGetInd:aCols[oGetInd:nAT][nZAXPCDU],"",oGetInd:aCols[oGetInd:nAT][nZAXMETR],oGetAno:aCols[oGetAno:oBrowse:nAT,1],""})	// "EXCLUSÃO DE INDICE" ### "Índice Excluído"
	EndIf

Return nRetDel

/*/{Protheus.doc} U_GV007A07
Função para montar o aheader e o acols com as metricas anuais.

<AUTHOR> Ribeiro
@Since 14/04/2014
@Since 11.5

@obs Função foi alterada de Static Function para function e refeita para atender o projeto do corporativo. Porem seu conceito inicial não foi alterado.

@Param cAnoRef, caracter, contem o ano.
@Param lMantemAno, logico, indica se mantem posicionado no ano passado por parametro.
/*/

User Function GV007A07(cAnoRef,lMantemAno,_cContrato,_cRevisa,_lVA011)

	Local nCount := 0
	Local nPosicao := 0

	Local cCampo := ""
	Local cQuery := ""
	Local cZALAlias := ""

	Local aCampos	:= {"ZAL_ANOREF","ZAL_MTRINF","ZAL_MTRREA"}

	Default cAnoRef := ""
	Default lMantemAno := .F.

	aHeadAno := {}
	aColsAno := {}

	SX3->(DbSetOrder(2))
	For nCount := 1 to Len(aCampos)
		If SX3->(DbSeek(aCampos[nCount]))
			_cPicture := SX3->(FieldGet(FieldPos("X3_PICTURE")))

			If cTpReceita == '2' .And. aCampos[nCount] $ "ZAL_MTRINI/ZAL_MTRREA/ZAL_MTRINF/ZAL_MTRDIP"
				_cPicture := "@E 99,999,999,999,999"
			EndIf

			Aadd(aHeadAno,{TRIM(X3Titulo()),;
				SX3->(FieldGet(FieldPos("X3_CAMPO"))),;
				_cPicture,;
				SX3->(FieldGet(FieldPos("X3_TAMANHO"))),;
				SX3->(FieldGet(FieldPos("X3_DECIMAL"))),;
				SX3->(FieldGet(FieldPos("X3_VALID"))),;
				SX3->(FieldGet(FieldPos("X3_USADO"))),;
				SX3->(FieldGet(FieldPos("X3_TIPO"))),;
				SX3->(FieldGet(FieldPos("X3_ARQUIVO"))),;
				SX3->(FieldGet(FieldPos("X3_CONTEXT")))})
		EndIf
	Next

	cQuery := " SELECT " + CRLF
	cQuery += "	 ZAL_ANOREF " + CRLF
	cQuery += "	,SUM(ZAL_MTRREA) ZAL_MTRREA " + CRLF
	cQuery += "	,CASE WHEN SUM(ZAL_MTRINF) > SUM(ZAL_MTRDIP) THEN " + CRLF
	cQuery += "		SUM(ZAL_MTRINF) " + CRLF
	cQuery += "	 ELSE " + CRLF
	cQuery += "		SUM(ZAL_MTRDIP)  " + CRLF
	cQuery += "	 END ZAL_MTRINF " + CRLF
	cQuery += " FROM " + RetSQLName("ZAL") + " ZAL " + CRLF
	cQuery += " WHERE ZAL.D_E_L_E_T_ = ' ' " + CRLF
	cQuery += "	AND ZAL_CLIENT = '" + _Cliente + "' " + CRLF
	cQuery += "	AND ZAL_LOJA = '" + _Loja + "' " + CRLF
	If _lVA011
		cQuery += "	AND ZAL_CONTRA = '" + _cContrato + "' "
		cQuery += "	AND ZAL_REVISA = '" + _cRevisa + "' "
	Else
		cQuery += "	AND ZAL_CONTRA = '" + _Contrato + "' "
		cQuery += "	AND ZAL_REVISA = '" + _Versao + "' "
	EndIf
	cQuery += "	AND ZAL_STATUS IN ('A','C') " + CRLF
	cQuery += " GROUP BY ZAL_ANOREF " + CRLF
	cQuery += " ORDER BY ZAL_ANOREF " + CRLF

	cZALAlias := GetNextAlias()
	DbUseArea(.T.,__cRdd,TcGenQry(,,cQuery),cZALAlias,.T.,.F.)

	While (cZALAlias)->(!Eof())
		Aadd(aColsAno,Array(Len(aHeadAno)+2))

		For nCount :=1 to Len(aHeadAno)
			cCampo := Alltrim(aHeadAno[nCount,2])
			If cCampo == 'ZAL_ANOREF'
				aColsAno[Len(aColsAno)][nCount] := ( cZALAlias )->(ZAL_ANOREF)
			ElseIf cCampo == 'ZAL_MTRINF'
				aColsAno[Len(aColsAno)][nCount] := ( cZALAlias )->(ZAL_MTRINF)
			ElseIf cCampo == 'ZAL_MTRREA'
				aColsAno[Len(aColsAno)][nCount] := ( cZALAlias )->(ZAL_MTRREA)
			EndIf
		Next
		aColsAno[Len(aColsAno)][Len(aHeadAno)+2] := .F.

		(cZALAlias)->(DbSkip())
	EndDo

	If Len(aColsAno) == 0
		Aadd(aColsAno,Array(Len(aHeadAno)+2))
		aColsAno[1][1] := StrZero(Year(dDataBase),4)
		aColsAno[1][2] := 0.00
		aColsAno[1][3] := 0.00
		aColsAno[Len(aColsAno)][Len(aHeadAno)+2] := .F.
	EndIf

	If lMantemAno .And. !Empty(cAnoRef)
		nPosicao := Ascan(aColsAno,{|x| AllTrim(x[1]) == cAnoRef })
	EndIf

	If nPosicao < 1
		nPosicao := 1
		cAnoRef := aColsAno[nPosicao][1]
	EndIf

	aMetricas := { Array(Len(aColsExc)) , Array(Len(aColsAno),3) }

	For nCount := 1 to Len(aColsAno)
	
		// Carrega os lancamentos dos indices
		If _lVA011
			U_GV007A08(aColsAno[nCount,1],,_cContrato,_cRevisa,.T.)
		Else	
			U_GV007A08(aColsAno[nCount,1])
		EndIf

		// Carrega os lancamentos das metricas
		If _lVA011
			U_GV007A09(aColsAno[nCount,1],,_cContrato,_cRevisa,.T.)
		Else	
			U_GV007A09(aColsAno[nCount,1])
		EndIf
		
		aMetricas[2][nCount][1] := aClone(aColsAno[nCount])
		aMetricas[2][nCount][2] := aClone(aColsInd)
		aMetricas[2][nCount][3] := aClone(aColsMet)
	Next

	aColsInd := aClone(aMetricas[2][nPosicao][2])
	aColsMet := aClone(aMetricas[2][nPosicao][3])

	(cZALAlias)->(DbCloseArea())

Return

/*/{Protheus.doc} U_GV007A08
Função para montar o aheader e acols com os indices.

<AUTHOR> Ribeiro
@Since 14/04/2014
@Since 11.5

@obs Função foi alterada de Static Function para function e refeita para atender o projeto do corporativo. Porem seu conceito inicial não foi alterado.

@Param cAnoRef, caracter, contem o ano.
@Param lEmpty, logico, se não existe indice.
/*/

User Function GV007A08(cAnoRef,lEmpty,_cContrato,_cRevisa,_lVA011)

	Local aNoFields := {"ZAX_CLIENT","ZAX_LOJA","ZAX_PROPOS","ZAX_ITPR","ZAX_VERSAO","ZAX_DTASS","ZAX_ANOREF","ZAX_FTMULT"}

	Local cQuery := ""
	Local cAliasQry := ""

	Local nOpc := 0
	Local nOrder := 1
	Local nCount := 0

	Default cAnoRef := StrZero(Year(dDataBase),4)
	Default lEmpty := .F.

	aHeadInd := {}
	aColsInd := {}

	If lEmpty
		nOpc := 3
	Else
		nOpc := 4

		cQuery := " SELECT ZAX.* " + CRLF
		cQuery += " FROM " + RetSqlName("ZAX") + " ZAX " + CRLF
		cQuery += CRLF
		cQuery += " WHERE " + CRLF
		cQuery += "	ZAX.D_E_L_E_T_ = ' ' " + CRLF
		cQuery += "	AND ZAX.ZAX_FILIAL = '" + xFilial("ZAX") + "' " + CRLF
		
		If _lVA011
			cQuery += "	AND ZAX.ZAX_CONTRA = '" + _cContrato + "' " + CRLF
			cQuery += "	AND ZAX.ZAX_REVISA = '" + _cRevisa + "' " + CRLF
		Else
			cQuery += "	AND ZAX.ZAX_CONTRA = '" + _Contrato + "' " + CRLF
			cQuery += "	AND ZAX.ZAX_REVISA = '" + _Versao + "' " + CRLF
		EndIf
		
		cQuery += "	AND ZAX.ZAX_CLIENT = '" + _Cliente + "' " + CRLF
		cQuery += "	AND ZAX.ZAX_LOJA = '"+_Loja+"' " + CRLF
		cQuery += "	AND ZAX.ZAX_ANOREF = '" + cAnoRef + "' " + CRLF
		cQuery += "	AND ZAX.ZAX_STATUS <> 'I' " + CRLF
		cQuery += CRLF
		cQuery += " ORDER BY " + CRLF
		cQuery += " 	 ZAX.ZAX_FILIAL " + CRLF
		cQuery += "	,ZAX.ZAX_CLIENT " + CRLF
		cQuery += " 	,ZAX.ZAX_STATUS " + CRLF
		cQuery += "	,ZAX.ZAX_ITEM " + CRLF

		cAliasQry := GetNextAlias()
	EndIf

	FillGetDados(nOpc,"ZAX",nOrder,,,,aNoFields,,,cQuery,,lEmpty,aHeadInd,aColsInd,,,,cAliasQry)

	If cTpReceita == '2'
		For nCount := 1 to Len(aHeadInd)
			If aHeadInd[nCount][2] $ "ZAX_REFMT1/ZAX_REFMT2"
				aHeadInd[nCount][3] := "@E 99,999,999,999,999"
			EndIf
		Next
	EndIf

Return

/*/{Protheus.doc} U_GV007A09
Função para montar o aheader e acols com as métricas.

<AUTHOR> Ribeiro
@Since 14/04/2014
@Since 11.5

@obs Função foi alterada de Static Function para function e refeita para atender o projeto do corporativo. Porem seu conceito inicial não foi alterado.

@Param cAnoRef, caracter, contem o ano.
@Param lEmpty, logico, se não existe métrica.
/*/

User Function GV007A09(cAnoRef,lEmpty,_cContrato,_cRevisa,_lVA011)

	Local nOpc := 0
	Local nOrder := 1
	Local nCount := 0

	Local aNoFields := {}

	Local cQuery := ""
	Local cAliasQry := ""
	Local aAuxHd	:= {}
	Local nPosMECF	:= 0

	Default cAnoRef := StrZero(Year(dDataBase),4)
	Default lEmpty	 := .F.

	aHeadMet := {}
	aColsMet := {}

	aNoFields := {"ZAL_CLIENT","ZAL_LOJA","ZAL_CONTRA","ZAL_REVISA","ZAL_PROPOS","ZAL_ITPR","ZAL_VERSAO","ZAL_DTASS";
		,"ZAL_CGCPRI","ZAL_DTCONT","ZAL_SEGMEN","ZAL_CONDIC","ZAL_CEI","ZAL_CNIS","ZAL_CAFIR";
		,"ZAL_CODIGO","ZAL_PRCDU","ZAL_PRET","ZAL_PRAR","ZAL_REAJ","ZAL_TPREAJ","ZAL_FTMULT";
		,"ZAL_OBSERV"}

	If lEmpty
		nOpc := 3
	Else
		nOpc := 4
		cQuery := " SELECT ZAL.* " + CRLF
		
		If ZAL->( FieldPos( 'ZAL_METECF' ) ) > 0  
			cQuery += " , (CASE "
			cQuery += " 	WHEN (ZAL.ZAL_METECF = '1') THEN 'BR_VERDE' "
			cQuery += " 	WHEN (ZAL.ZAL_METECF = '2') THEN 'BR_VERMELHO' "
			cQuery += " 	ELSE 'BR_BRANCO' END ) AS LEGENDAECF "
		EndIf
		
		cQuery += " FROM " + RetSQLName("ZAL") + " ZAL " + CRLF
		cQuery += " WHERE ZAL.D_E_L_E_T_ = ' ' " + CRLF
		cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
		
		If _lVA011
			cQuery += "	AND ZAL.ZAL_CONTRA = '" + _cContrato + "' " + CRLF
			cQuery += "	AND ZAL.ZAL_REVISA = '" + _cRevisa + "' " + CRLF
		Else
			cQuery += "	AND ZAL.ZAL_CONTRA = '" + _Contrato + "' " + CRLF
			cQuery += "	AND ZAL.ZAL_REVISA = '" + _Versao + "' " + CRLF
		EndIf
		
		cQuery += "	AND ZAL.ZAL_CLIENT = '" + _Cliente + "' " + CRLF
		cQuery += "	AND ZAL.ZAL_LOJA = '" + _Loja + "' " + CRLF
		cQuery += "	AND ZAL.ZAL_ANOREF = '" + cAnoRef + "' " + CRLF
		cQuery += "	AND ZAL.ZAL_STATUS IN ('A','C','I')"
		cQuery += " ORDER BY ZAL_CGCAGR " + CRLF

		cAliasQry := GetNextAlias()
	EndIf

	FillGetDados(nOpc,"ZAL",nOrder,,,,aNoFields,,,cQuery,,lEmpty,aHeadMet,aColsMet,,,,cAliasQry)

	If ZAL->( FieldPos( 'ZAL_METECF' ) ) > 0
	
		aAuxHd := {			;
		""					;	// X3_TITULO
		,"LEGENDAECF"		;	// X3_CAMPO
		,"@BMP"				;	// X3_PICTURE
		,0010				;	// X3_TAMANHO
		,0000				;	// X3_DECIMAL
		,""					;	// X3_VALID
		,""					;	// X3_USADO
		,"C"				;	// X3_TIPO
		,""					;	// X3_F3
		,"V"				;	// X3_CONTEXT
		,""					;	// X3_CBOX
		,""					;	// X3_RELACAO
		,""					;	// X3_WHEN
		,"V"				;	// X3_VISUAL
		,""					;	// X3_VLDUSER
		,""					;	// X3_PICTVAR
		,""					;	// X3_OBRIGAT
		}
		
		// Adiciona Legenda no Grid de Metricas
		ASize(aHeadMet, Len(aHeadMet) + 1 )
		AINS( aHeadMet, 1 )
		aHeadMet[1] := aAuxHd
		
	EndIf

	If cTpReceita == '2'
		For nCount := 1 To Len(aHeadMet)
			If aHeadMet[nCount][2] $ "ZAL_MTRINI/ZAL_MTRREA/ZAL_MTRINF/ZAL_MTRDIP/ZAL_MTINFR/ZAL_MTDIFR"
				aHeadMet[nCount][3] := "@E 99,999,999,999,999"
			EndIf
		Next
	EndIf

	nPosCGC 	:= aScan(aHeadMet,{|x| Alltrim(x[2]) == "ZAL_CGCAGR" })
	nPosTp 		:= aScan(aHeadMet,{|x| Alltrim(x[2]) == "ZAL_TPCNPJ" })
	nPosMECF 	:= aScan(aHeadMet,{|x| Alltrim(x[2]) == "ZAL_METECF" })
	
	For nCount := 1 To Len(aColsMet)
	
		If ZAL->( FieldPos( 'ZAL_METECF' ) ) > 0
		
			// Adiciona Legenda no Grid de Metricas
			ASize(aColsMet[nCount], Len(aColsMet[nCount]) + 1 )
			AINS( aColsMet[nCount], 1 )
			
			If aColsMet[nCount][nPosMECF] == "1"
				aColsMet[nCount][1] := "BR_VERDE"
			ElseIf aColsMet[nCount][nPosMECF] == "2"
				aColsMet[nCount][1] := "BR_VERMELHO"
			ElseIf Empty(aColsMet[nCount][nPosMECF] )
				aColsMet[nCount][1] := "BR_BRANCO"
			EndIf
			
		EndIf
	
		aColsMet[nCount][nPosCGC] := STRTRAN(STRTRAN(STRTRAN(aColsMet[nCount][nPosCGC],'.',''),'/',''),'-','')
		If aColsMet[nCount][nPosTP] == "1"
			aColsMet[nCount][nPosCGC] := Transform(aColsMet[nCount][nPosCGC],IIF(cPaisLoc="BRA","@R 99.999.999/9999-99",IIF(cPaisloc="ARG","@R 99-99999999-9","@!")))//Transform(aColsMet[nCount][nPosCGC],"@R 99.999.999/9999-99")
		ElseIf aColsMet[nCount][nPosTP] == "2"
			aColsMet[nCount][nPosCGC] := Transform(aColsMet[nCount][nPosCGC],"@R 999.999.999-99")
		Else
			aColsMet[nCount][nPosCGC] := Transform(aColsMet[nCount][nPosCGC],"@!")
		EndIf
	Next

Return

/*/{Protheus.doc} U_GV007A10
Função para montar o aheader e acols com das exceções.

<AUTHOR> Ribeiro
@Since 14/04/2014
@Since 11.5

@obs Função foi alterada de Static Function para function e refeita para atender o projeto do corporativo. Porem seu conceito inicial não foi alterado.
/*/

User Function GV007A10(_cContrato,_cRevisa,_lVA011)

	Local nCount := 0

	Local lEmpty := .F.

	Local cSeekKey := ""

	Local aNoFields := {}

	Local bSeekWhile := {|| }
	Local bAfterCols := {|| }

	aColsExc := {}
	aHeadExc := {}
	aRecExcec := {}

	cSeekKey := Iif(_lVA011, xFilial("ZAW")+_cContrato+_cRevisa, xFilial("ZAW")+_Contrato+_Versao)
	aNoFields := {"ZAW_CLIENT","ZAW_LOJA","ZAW_PROPOS","ZAW_ITPR","ZAW_VERSAO","ZAW_CONTRA","ZAW_REVISA","ZAW_MEMO"}
	bSeekWhile := {|| ZAW->(ZAW_FILIAL+ZAW_CONTRA+ZAW_REVISA) }
	If _lVA011
		bAfterCols := {||If(xFilial("ZAW")+_cContrato+_cRevisa==ZAW->(ZAW_FILIAL+ZAW_CONTRA+ZAW_REVISA),Aadd(aRecExcec,ZAW->(Recno())),),.T. }
	Else
		bAfterCols := {||If(xFilial("ZAW")+_Contrato+_Versao==ZAW->(ZAW_FILIAL+ZAW_CONTRA+ZAW_REVISA),Aadd(aRecExcec,ZAW->(Recno())),),.T. }
	EndIf
	
	ZAW->(DbSetOrder(4))
	lEmpty := Iif(_lVA011,!ZAW->(DbSeek(xFilial("ZAW")+_cContrato+_cRevisa)),!ZAW->(DbSeek(xFilial("ZAW")+_Contrato+_Versao)))

	FillGetDados(If(lEmpty,3,4),"ZAW",4,cSeekKey,bSeekWhile,,aNoFields,,,,,lEmpty,aHeadExc,aColsExc,bAfterCols,,)

Return

/*/{Protheus.doc} U_GV007A11
Função para preencher os acols com as informações referente ao ano selecionado.

<AUTHOR> Ribeiro
@Since 14/04/2014
@Since 11.5

@obs Função foi alterada de Static Function para function e refeita para atender o projeto do corporativo. Porem seu conceito inicial não foi alterado.

@Param cAnoRef, caracter, contem o ano.
/*/

User Function GV007A11(cAnoRef,_lSoIndice)

	Local lVoltaAcols := .F.

	Local cAnoAnt := cAnoRef
	Local cAnoCorp := GETMV("TDI_ANOCOR",,"2010")
	Local cAnoCorren := YEAR(DDATABASE)

	Local nX := 0
	Local nL := 0
	Local _cIncX := 0
	Local _cIncL := 0
	Local nZAXPCDU	 := AScan(aHeadInd,{|x|  Alltrim( x[2]) == "ZAX_PRCDU" })
	Local nZAXMETR	 := AScan(aHeadInd,{|x|  Alltrim( x[2]) == "ZAX_METRIC" })
	Local nZALNOMA	 := AScan(aHeadMet,{|x|  Alltrim( x[2]) == "ZAL_NOMAGR" })
	Local nZALCNPJP := AScan(aHeadMet,{|x|  Alltrim( x[2]) == "ZAL_TPCNPJ" })
	Local nZALCNPJA := AScan(aHeadMet,{|x|  Alltrim( x[2]) == "ZAL_CGCAGR" })

	Default _lSoIndice := .F.

	cAnoRef := oGetAno:aCols[oGetAno:oBrowse:nAT][1]

	
	If cAnoRef == cAnoInicial
		oGetInd:lUpdate	:= .T.
		oGetInd:LINSERT := .T.
		oGetInd:LDELETE := .T.
		oGetInd:ForceRefresh()
	Else
		oGetInd:lUpdate	:= .F.
		oGetInd:LDELETE := .F.
		oGetInd:ForceRefresh()
	EndIf

	If Len(aMetricas[2]) < oGetAno:oBrowse:nAT
	
		If oGetAno:oBrowse:nAT == 1
			cAnoRef := Soma1(oGetAno:aCols[oGetAno:oBrowse:nAT,1])
		Else
			cAnoRef := Soma1(oGetAno:aCols[oGetAno:oBrowse:nAT-1,1])
		EndIf
		
		If !(cAnoRef > Alltrim(Str(cAnoCorren)))
			If Val(cAnoRef) < (Val(cAnoCorp) + 1)
				Aadd(aColsAno,{cAnoRef,0.00,0.00,NIL,.F.})

				oGetAno:aCols := aClone(aColsAno)
				oGetAno:Refresh()

				// Carrega os lancamentos dos indices
				U_GV007A08(cAnoRef,.T.)

				// Carrega os lancamentos das metricas
				U_GV007A09(cAnoRef,.T.)

				Aadd(aMetricas[2],{aColsAno[Len(aColsAno)],aColsInd,aColsMet})
				Aadd(aLog,{STR0060,STR0061," "," ",_Versao,_Picpad,"",oGetMet:aCols[oGetMet:nAT][nZALCNPJA],oGetMet:aCols[oGetMet:nAT][nZALNOMA],cAnoRef,"",oGetAno:aCols[oGetAno:oBrowse:nAT,1],""})	// "INCLUSÃO DE ANO REFERÊNCIA" ### "Ano de Referência Incluido"
			Else
				Help(,,"Help",,STR0062,1,0)	// "O Ano de referencia inválido"
				oGetAno:aCols[oGetAno:oBrowse:nAT,Len(oGetAno:aCols[oGetAno:oBrowse:nAT])] := .T.
				oGetAno:oBrowse:nAT := oGetAno:oBrowse:nAT - 1
				oGetAno:Refresh()
			EndIf
		Else
			Help(,,"Help",,STR0063,1,0)	// "O Ano de referencia não podera ser maior que o ano corrente"
			oGetAno:aCols[oGetAno:oBrowse:nAT,Len(oGetAno:aCols[oGetAno:oBrowse:nAT])] := .T.
			oGetAno:oBrowse:nAT := oGetAno:oBrowse:nAT - 1
			oGetAno:Refresh()
		EndIf

	ElseIf Len(aMetricas[2]) > Len(oGetAno:aCols)

		lVoltaAcols := .T.
		aDel(aMetricas[2],Len(aMetricas[2]))
		aSize(aMetricas[2],Len(aMetricas[2])-1)

		aDel(aColsAno,Len(aColsAno))
		aSize(aColsAno,Len(aColsAno)-1)

	EndIf

	If nPosAno <> oGetAno:oBrowse:nAT
		If lVoltaAcols
			If Len(aMetricas[2][nPosAno][2]) <> Len(oGetInd:aCols)
				If Len(oGetInd:aCols) > Len(aMetricas[2][nPosAno][2])
					_cIncX := Len(oGetInd:aCols) - Len(aMetricas[2][nPosAno][2])
					For nX := 1 to _cIncX
						Aadd(aLog,{STR0015,STR0064," "," ",_Versao,_Picpad,"",oGetInd:aCols[Len(aMetricas[2][nPosAno][2])+nX,nZAXPCDU],oGetInd:aCols[Len(aMetricas[2][nPosAno][2])+nX,nZAXPCDU],"",oGetInd:aCols[Len(aMetricas[2][nPosAno][2])+nX,nZAXMETR],oGetAno:aCols[nPosAno,1],""})	// "INCLUSAO DE ITEM" ### "Inclusão de Índices"
					Next
				EndIf
			EndIf

			If Len(aMetricas [ 2 ][ nPosAno][ 3 ]) <> Len(oGetMet:aCols)
				If Len(oGetMet:aCols) > Len(aMetricas [ 2 ][ nPosAno ][ 3 ])
					_cIncL := Len(oGetMet:aCols) - Len(aMetricas [ 2 ][ nPosAno ][ 3 ])
					For nL := 1 to _cIncL
						Aadd(aLog,{STR0015,STR0065," "," ",_Versao,_Picpad,"",oGetMet:aCols[Len(aMetricas[2][nPosAno][3])+nL,nZALCNPJP],oGetMet:aCols[Len(aMetricas[2][nPosAno][3])+nL,nZALCNPJA],"",oGetMet:aCols[Len(aMetricas[2][nPosAno][3])+nL,nZALNOMA],oGetAno:aCols[nPosAno,1],""})	// "INCLUSAO DE ITEM" ### "Inclusão de CNPJ"
					Next
				EndIf
			EndIf

			If !_lSoIndice
				oGetMet:aCols := aClone(aMetricas[2][nPosAno][3])

				For nX := 1 to Len(oGetMet:aCols)
					If !("." $ oGetMet:aCols[nX][nZALCNPJA])
						If oGetMet:aCols[nX][nZALCNPJP] == "1"
							oGetMet:aCols[nX][nZALCNPJA] := Transform(oGetMet:aCols[nX][nZALCNPJA],IIF(cPaisLoc="BRA","@R 99.999.999/9999-99",IIF(cPaisloc="ARG","@R 99-99999999-9","@!")))//Transform(oGetMet:aCols[nX][nZALCNPJA],"@R 99.999.999/9999-99")
						ElseIf oGetMet:aCols[nX][nZALCNPJP] == "2"
							oGetMet:aCols[nX][nZALCNPJA] := Transform(oGetMet:aCols[nX][nZALCNPJA],"@R 999.999.999-99")
						Else
							oGetMet:aCols[nX][nZALCNPJA] := Transform(oGetMet:aCols[nX][nZALCNPJA],"@!")
						EndIf
					EndIf
				Next
			EndIf

			oGetInd:aCols := aClone(aMetricas[2][nPosAno][2])

			aMetricas[2][nPosAno][1] := aClone(oGetAno:aCols[nPosAno])
			aMetricas[2][nPosAno][2] := aClone(oGetInd:aCols)
			If !_lSoIndice
				aMetricas[2][nPosAno][3] := aClone(oGetMet:aCols)
			EndIf
		EndIf

		If U_GV007A05(cAnoAnt)
			aMetricas[2][nPosAno][2] := oGetInd:aCols 
			aMetricas[2][nPosAno][3] := oGetMet:aCols
			nPosAno := oGetAno:oBrowse:nAT

			aColsInd := aClone(aMetricas[2][nPosAno][2])
			oGetInd:aCols := aClone(aMetricas[2][nPosAno][2])
			oGetInd:oBrowse:nAT := 1
			oGetInd:Refresh()

			If !_lSoIndice
				aColsMet := aClone(aMetricas[2][nPosAno][3])
				oGetMet:aCols := aClone(aMetricas[2][nPosAno][3])
				oGetMet:oBrowse:nAT := 1
				oGetMet:Refresh()
			EndIf
		Else
			oGetAno:oBrowse:nAT := nPosAno
			oGetAno:Refresh()
		EndIf
	EndIf

Return

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A13 ³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Funcao de validacao da gravacao das metricas e indices     ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007.PRW                                              ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A13(lConfirm,lRecalc,nTipo,cAnoRef)

	Local nZAWMEMO		:= AScan(aHeadExc,{|x|  Alltrim( x[2]) == "ZAW_MEMOD" })
	Local nZAWINCL		:= AScan(aHeadExc,{|x|  Alltrim( x[2]) == "ZAW_INCLUS" })
	Local nZAWCODM		:= AScan(aHeadExc,{|x|  Alltrim( x[2]) == "ZAW_CODMOT" })
	Local nZAWMOTI		:= AScan(aHeadExc,{|x|  Alltrim( x[2]) == "ZAW_MOTIVO" })
	Local nZAXPCDU		:= AScan(aHeadInd,{|x|  Alltrim( x[2]) == "ZAX_PRCDU" })
	Local nZAXMETR		:= AScan(aHeadInd,{|x|  Alltrim( x[2]) == "ZAX_METRIC" })
	Local nZALCNPJA	:= AScan(aHeadMet,{|x|  Alltrim( x[2]) == "ZAL_CGCAGR" })
	Local nZALNOMA		:= AScan(aHeadMet,{|x|  Alltrim( x[2]) == "ZAL_NOMAGR" })

	Local _cDescri		:= ""

	Local _cIncW		:= 0
	Local _cIncX		:= 0
	Local _cIncL		:= 0
	Local nX			:= 0
	Local nL			:= 0
	Local nW			:= 0

	Default nTipo		:= 1 // 1 - Visualizacao || 2 - Manutencao

	If nTipo <> 6
		If U_GV007A05()

			If Len(aMetricas [ 2 ]) < oGetAno:oBrowse:nAT
				aAdd(aMetricas [ 2 ],{ oGetAno:aCols, {}, {}})
				U_GV007A08(cAnoRef, .T. )	// Carrega os lancamentos dos indices
				U_GV007A09(cAnoRef,.T.)	// Carrega os lancamentos das metricas
			Endif
			If Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 2 ]) <> Len(oGetInd:aCols)
				If Len(oGetInd:aCols) > Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 2 ])
					_cIncX := Len(oGetInd:aCols) - Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 2 ])
					For nX := 1 to _cIncX
						aadd(aLog,{STR0015,STR0064," "," ",_Versao,_Picpad,"",oGetInd:aCols[Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 2 ])+nX,nZAXPCDU],oGetInd:aCols[Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 2 ])+nX,nZAXPCDU],"",oGetInd:aCols[Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 2 ])+nX,nZAXMETR],oGetAno:aCols[oGetAno:oBrowse:nAT,1],""})	// "INCLUSAO DE ITEM" ### "Inclusão de Índices"
					Next
				EndIf
			EndIf

			If Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT][ 3 ]) <> Len(oGetMet:aCols)
				If Len(oGetMet:aCols) > Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 3 ])
					_cIncL := Len(oGetMet:aCols) - Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 3 ])
					For nL := 1 to _cIncL
						aadd(aLog,{STR0015,STR0065," "," ",_Versao,_Picpad,"",oGetMet:aCols[Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 3 ])+nL,nZALCNPJA],oGetMet:aCols[Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 3 ])+nL,nZALCNPJA],"",oGetMet:aCols[Len(aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 3 ])+nL,nZALNOMA],oGetAno:aCols[oGetAno:oBrowse:nAT,1],""})	// "INCLUSAO DE ITEM" ### "Inclusão de CNPJ"
					Next
				EndIf
			EndIf

			For nW:= 1 To Len(oGetExc:aCols)
				If Empty(oGetExc:aCols[nW,nZAWMEMO] )
					If !Empty(oGetExc:aCols[nW,nZAWINCL])
						_cIncW := Len(oGetExc:aCols) - Len(aMetricas [ 1 ])
						_cDescri := oGetExc:aCols[Len(aMetricas [ 1 ])+_cIncW,nZAWCODM] + " - " + oGetExc:aCols[Len(aMetricas [ 1 ])+_cIncW,nZAWMOTI]
						aadd(aLog,{STR0015,STR0020," "," ",_Versao,_Picpad,"",,"","","",_cDescri,"",""})	// "INCLUSAO DE ITEM" ### "Inclusão de Exceções"
					EndIf
				EndIf
			Next

			aMetricas [ 1 ] := aClone(oGetExc:aCols)
			aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 1 ] := aClone(oGetAno:aCols[oGetAno:oBrowse:nAT])
			aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 2 ] := aClone(oGetInd:aCols)
			aMetricas [ 2 ][ oGetAno:oBrowse:nAT ][ 3 ] := aClone(oGetMet:aCols)


			If !(lRecalc)
				If !lConfirm
					If MsgYesNo(STR0068)	// "Deseja Recalcular Reajustes?"
						MsgRun( STR0037, STR0038 ,;	// "Recalculo Indices de Reajustes e Métricas" ### "Processando ... "
						( cAnoRef := oGetAno:aCols [ 1, 1 ], { || U_GV007A14() } )) //U_GV007A14()
					EndIf
				Else
					MsgRun( STR0037, STR0038 ,;	// "Recalculo Indices de Reajustes e Métricas" ### "Processando ... "
					( cAnoRef := oGetAno:aCols [ 1, 1 ], { || U_GV007A14() } )) //U_GV007A14()
				EndIf
			EndIf

			lConfirm := .T.

		EndIf
	EndIf

Return lConfirm

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³ U_GV007A14  ³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Funcao para carregar e reajustar os indices e metricas     ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ Workarea Contratos e Portal Cliente                        ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A14()
	Local cAnoCorp	:= GETMV("TDI_ANOCOR",,"2010")
	Local cMaiorMT	:= GETMV("MV_CPMAIOR",,.T.)
	Local cAnoInicial:= cAnoCorp
	Local lConfirm	:= .F.
	Local nA		:= 1
	Local n1		:= 1
	Local n2		:= 1
	Local aAnosRef	:= {}
	Local nPosAnoIni:= 0

	Local nPos1		:= AScan(aHeadMet,{ |x| x[2] == "ZAL_INDICE" })
	Local nPos2		:= AScan(aHeadMet,{ |x| x[2] == "ZAL_MTRINF" })
	Local nPos3		:= AScan(aHeadMet,{ |x| x[2] == "ZAL_MTRREA" })
	Local nPos4		:= AScan(aHeadMet,{ |x| x[2] == "ZAL_ANOREA" })
	Local nPosE		:= AScan(aHeadMet,{ |x| x[2] == "ZAL_MTRDIP" })
	Local nPosF		:= AScan(aHeadMet,{ |x| x[2] == "ZAL_STATUS" })

	Local nPos5		:= AScan(aHeadInd,{ |x| x[2] == "ZAX_VLMCDU" })
	Local nPos6		:= AScan(aHeadInd,{ |x| x[2] == "ZAX_VLMET " })
	Local nPos7		:= AScan(aHeadInd,{ |x| x[2] == "ZAX_VLMAR " })
	Local nPos8		:= AScan(aHeadInd,{ |x| x[2] == "ZAX_REFMT1" })
	Local nPos9		:= AScan(aHeadInd,{ |x| x[2] == "ZAX_REFMT2" })
	Local nPosA		:= AScan(aHeadInd,{ |x| x[2] == "ZAX_ORIGEM" })
	Local nPosB		:= AScan(aHeadInd,{ |x| x[2] == "ZAX_INDCDU" })
	Local nPosC		:= AScan(aHeadInd,{ |x| x[2] == "ZAX_INDET " })
	Local nPosD		:= AScan(aHeadInd,{ |x| x[2] == "ZAX_INDAR " })

	Local aIndices	:= {}
	Local cIndice	:= "1"
	Local nValorRea := 0
	Local nVlrAntMT1:= 0.00
	Local cProposta	:= ""
	Local nTotADIP  := 0
	Local nTotAINF  := 0

	Local cAnoRef := StrZero(Year(dDataBase),4)

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³Verifica se o cliente e do tipo corporativo                    ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
//Retorno da funcao U_GV001X06()
//1 - Numero do Contrato
//2 - Item do Contrato
//3 - Codigo do produto
//4 - Linha de receita  (onde 1 = CDU ; 2 = ET ; 3 = AR ; 4 = SMS ; 5 = CDU Adicional ; 6 = ET Adicional ; 7 = AR Adicional ; 8 = SMS Adicional)
//5 - Status do Contrato
//6 - Data de Cancelamento do Contrato
//7 - Versão do Contrato
//8 - Indice de Reajuste
//9 - Proposta

	If Len(aProdCorp) == 0
		Aviso( STR0002 , STR0032  , { "OK" } )	// "Modalidade Corporativo" ### "Cliente não possui itens de contratos ativos !!"
		Return .F.
	Endif

// Seleciona o indice utilizado no item do contrato
	cIndice   := aProdCorp [ 1 ][ 8 ]

// Seleciona o numero da proposta utilizada no item do contrato
	cProposta := aProdCorp [ 1 ][ 9 ]
	If Empty(cProposta)
		cProposta := U_GV001X11 (_Cliente,_Loja)
	Endif


//³Atualiza todas as alteracoes efetuadas no array aMetricas    ³


	If !U_GV007A13(@lConfirm,.T.,,cAnoRef)
		MsgStop(STR0069)	// "Reajuste não foi realizado, corrija as pendências!"
		Return .F.
	EndIf
// Posiciona no 1o.ano de referencia das metricas
	oGetAno:oBrowse:nAT := 1
	oGetAno:oBrowse:Refresh(.T.)
	oGetAno:ForceRefresh()


//³Carrega os numeros dos anos para busca dos indices			³

	cAnoSeq := oGetAno:aCols [ 1 , 1 ]
	While cAnoSeq <= cAnoCorp
		aAdd(aAnosRef,cAnoSeq )
		cAnoSeq := Soma1(cAnoSeq)
	Enddo

	aIndices := U_GV001X03(,cIndice,aAnosRef)	[1]

	If Len(aIndices) == Len(aAnosRef) .And. Len(aIndices) > 0

		If Len(aAnosRef) > 0

		
		//³Atualiza os conteudos da newgetdados
		
			For n1 := 1 to Len( aMetricas [ 2 ] )

			
			//³Atualiza os conteudos da newgetdados para Metricas			³
			

				If  aMetricas [ 2 ][ n1 ][ 1 ][ 1 ] < cAnoCorp

             	//Muda de Ano zero os totalizadores
					nTotADIP := 0
					nTotAINF := 0

					For n2 := 1 to Len( aMetricas [ 2 ][ n1 ][ 3 ] )

						If aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPosF ] == "A"
							nTotADIP  += aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPosE ]
							nTotAINF  += aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos2 ]
						EndIf


					Next

					nValorRea := 0
					For n2 := 1 to Len( aMetricas [ 2 ][ n1 ][ 3 ] )

						If aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPosF ] == "A"
							aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos1 ] := cIndice	 	// ZAL_INDICE
							If cMaiorMT
								If nTotADIP > nTotAINF
//							If aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPosE ] > aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos2 ]
									aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos3 ] := U_GV001X01( aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPosE ], aMetricas[ 2 ][ n1 ][ 1 ][ 1 ], cAnoCorp, aIndices)	// ZAL_MTRREA
								Else
									aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos3 ] := U_GV001X01( aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos2 ], aMetricas[ 2 ][ n1 ][ 1 ][ 1 ], cAnoCorp, aIndices)	// ZAL_MTRREA
								Endif
							Else
								If aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPosE ] > 0
									aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos3 ] := U_GV001X01( aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPosE ], aMetricas[ 2 ][ n1 ][ 1 ][ 1 ], cAnoCorp, aIndices)	// ZAL_MTRREA
								Else
									aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos3 ] := U_GV001X01( aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos2 ], aMetricas[ 2 ][ n1 ][ 1 ][ 1 ], cAnoCorp, aIndices)	// ZAL_MTRREA
								Endif
							EndIf
							aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos4 ] := cAnoCorp		// ZAL_ANOREA
							nValorRea += aMetricas[ 2 ][ n1 ][ 3 ][ n2 ][ nPos3 ]
						Endif

					Next

					aMetricas[ 2 ][ n1 ][ 1 ][ 3 ] := nValorRea

				Endif


			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³Atualiza os conteudos da newgetdados para Indices de Reajustes ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

				nVlrAntMT1	:= 0.00

			// Inicia a partir do 2a.ano de metrica os reajustes dos indices conforme regra
				If n1 > 1 .And. cAnoInicial < aMetricas [ 2 ][ n1 ][ 1 ][ 1 ]

					If 	(nPosAnoIni := Ascan(aMetricas[2],{|x| AllTrim(x[ 1 ][ 1 ]) == cAnoInicial })) > 0

						For n2 := 1 to Len( aMetricas [ 2 ][ n1 ][ 2 ] )

							aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPos6 ] := U_GV001X02( aMetricas[2][nPosAnoIni][2][n2][nPos6],aMetricas[2][n1][1][1],cAnoInicial,aIndices)	// ZAX_VLMET
							aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPos5 ] := U_GV001X02( aMetricas[2][nPosAnoIni][2][n2][nPos5],aMetricas[2][n1][1][1],cAnoInicial,aIndices)	// ZAX_VLMCDU
							aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPos7 ] := U_GV001X02( aMetricas[2][nPosAnoIni][2][n2][nPos7],aMetricas[2][n1][1][1],cAnoInicial,aIndices)	// ZAX_VLMAR

							If cTpReceita == "1"	// Receita Bruta

								aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPosB ] := aMetricas[2][nPosAnoIni][2][n2][nPosB] 	// ZAX_INDCDU
								aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPosC ] := aMetricas[2][nPosAnoIni][2][n2][nPosC]		// ZAX_INDET
								aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPosD ] := aMetricas[2][nPosAnoIni][2][n2][nPosD]		// ZAX_INDAR

								If n2 < Len( aMetricas [ 2 ][ n1 ][ 2 ] )
									aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPos9 ] := U_GV001X02( aMetricas[2][nPosAnoIni][2][n2][nPos9],aMetricas[2][n1][1][1],cAnoInicial,aIndices,,,, .T.)	// ZAX_REFMT2
								Else
									aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPos9 ] := aMetricas[2][nPosAnoIni][2][n2][nPos9]
								Endif

							// Reajusta o valor "De"
								If n2 > 1 .And. nVlrAntMT1 > 0
									aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPos8 ] := nVlrAntMT1 + 0.01
								Endif
								nVlrAntMT1		:= aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPos9 ]


	//						aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPos8 ] := U_GV001X02( aMetricas[2][1][2][n2][nPos8],aMetricas[2][n1][1][1],aMetricas[2][1][1][1],aIndices)	// ZAX_REFMT1

							Else	// Receita Especifica
								aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPosB ] := U_GV001X02( aMetricas[2][nPosAnoIni][2][n2][nPosB],aMetricas[2][n1][1][1],cAnoInicial,aIndices)	// ZAX_INDCDU
								aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPosC ] := U_GV001X02( aMetricas[2][nPosAnoIni][2][n2][nPosC],aMetricas[2][n1][1][1],cAnoInicial,aIndices)	// ZAX_INDET
								aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPosD ] := U_GV001X02( aMetricas[2][nPosAnoIni][2][n2][nPosD],aMetricas[2][n1][1][1],cAnoInicial,aIndices)	// ZAX_INDAR
							Endif

							aMetricas[ 2 ][ n1 ][ 2 ][ n2 ][ nPosA ] := "R"			// ZAX_AUTMAN

						Next

					Endif

				Endif

			Next

		//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
		//³Atualiza os objetos das NewGetdados dos Anos de Referencia, Metricas e Indices ³
		//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
			For nA := 1 to Len(aMetricas [ 2 ])
				oGetAno:aCols[ nA ] := aClone( aMetricas [ 2 ][ nA ][ 1 ])
			Next

			oGetAno:oBrowse:nAT := 1
			oGetAno:Refresh()
			nPosAno := 1

			aColsInd := aClone(aMetricas [ 2 ][ 1 ][ 2 ])
			oGetInd:oBrowse:nAT := 1
			oGetInd:aCols := aClone(aMetricas [ 2 ][ 1 ][ 2 ])
			oGetInd:Refresh()

			aColsMet := aClone(aMetricas [ 2 ][ 1 ][ 3 ])
			oGetMet:aCols := aClone(aMetricas [ 2 ][ 1 ][ 3 ])
			oGetMet:oBrowse:nAT := 1
			oGetMet:Refresh()

		Endif
	Else
		Aviso( STR0002 , STR0070 , { "OK" } )	// "Modalidade Corporativo" ### "Existem indices de reajustes não cadastrados, verifique a tabela de indices !!"
		Return .F.
	Endif

Return

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A15 ³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Validação campo ZAL_CGCAGR                                 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A15(cCGCAgr,nI,cAnoVld)                              ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A15(cCGCAgr,nI,cAnoVld,lVld)

	Local nInd			:= 0
	Local nTotReg		:= 0
	Local cQuery		:= ''
	Local aArea			:= GetArea()
	Local cZALAlias		:= GetNextAlias()
	Local cCNBAlias		:= GetNextAlias()
	Local nPosAnoRef	:= aScan(aHeadAno,{|x| Alltrim(x[2])=="ZAL_ANOREF"})
	Local nPosCGC 	 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_CGCAGR"})
	Local nPosTPCNPJ 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_TPCNPJ"})
	Local nPosStat	 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_STATUS"})
	Local lRet 			:= .T.
	Local nCGCDPL		:= 0
	Local ny 			:= 0
	Default nI			:= oGetMet:nAT
	Default cAnoVld		:= oGetAno:aCols[oGetAno:nAT,nPosAnoRef]

//Define se o programa está sendo chamado do valid do campo
	Default lVld               := .F.

	If "ZAL_CGCAGR" $ READVAR()
		If oGetMet:aCols[nI,nPosStat] == 'A'
			cCGCAgr 	:= &(READVAR())
		Else
			Help(,,"Help",,STR0071,1,0)	// "CNPJ Agregado Inativo, o CNPJ não poderá ser alterado!"
			Return .F.
		EndIf
	EndIf

	Default cCGCAgr 	:= oGetMet:aCols[nI,nPosCGC]
	cCGCAgr		:= STRTRAN(STRTRAN(STRTRAN(cCGCAgr,'.',''),'/',''),'-','')

	If oGetMet:aCols[nI,nPosTPCNPJ] == '1'
		If !(Len(AllTrim(cCGCAgr))==14) .And. cPaisLoc = "BRA"
			Help(" ",1,"CGC")
			lRet := .F.
		Else
			// Alterado por denise em 01/07/16
			If cPaisLoc = "BRA"
				lRet := CGC(cCGCAgr)
			//Elseif cPaisloc = "ARG"
			//  	lRet := Cuit(cCGCAgr)
			Else
				lRet	:= .T.
			Endif
		EndIf
	ElseIf oGetMet:aCols[nI,nPosTPCNPJ] == '2'
		If !(Len(AllTrim(cCGCAgr))==11) .And. cPaisLoc = "BRA"
			Help(" ",1,"CPFINVALID")
			lRet := .F.
		EndIf
	EndIf

	If lRet
	//Avalia se o Contrato já existe contrato com produtos ativos

		If lVld
			cQuery := " SELECT " + CRLF
			cQuery += "	 COUNT(1) QTDCGC " + CRLF
			cQuery += "	,SA1.A1_FILIAL " + CRLF
			cQuery += "	,SA1.A1_COD " + CRLF
			cQuery += "	,SA1.A1_LOJA " + CRLF
			cQuery += "	,CN9.CN9_NUMERO " + CRLF
			cQuery += " FROM " + RetSqlName("SA1") + " SA1 " + CRLF
			cQuery += CRLF 
			cQuery += " INNER JOIN " + RetSqlName("CNC") + " CNC ON " + CRLF
			cQuery += "	CNC.D_E_L_E_T_ = ' ' " + CRLF
			cQuery += "	AND CNC.CNC_FILIAL = '" + xFilial("CNC") + "' " + CRLF
			cQuery += "	AND CNC.CNC_CLIENT = SA1.A1_COD " + CRLF
			cQuery += "	AND CNC.CNC_LOJACL = SA1.A1_LOJA " + CRLF
			cQuery += "	AND CNC.CNC_TIPCLI = '01' " + CRLF
			cQuery += CRLF 
			cQuery += " INNER JOIN " + RetSqlName("CN9") + " CN9 ON " + CRLF
			cQuery += " 	CN9.D_E_L_E_T_ = ' ' " + CRLF
			cQuery += "	AND CN9.CN9_FILIAL = '" + xFilial("CN9") + "' " + CRLF
			cQuery += "	AND CN9.CN9_NUMERO = CNC.CNC_NUMERO " + CRLF
			cQuery += "	AND CN9.CN9_REVISA = CNC.CNC_REVISA " + CRLF
			cQuery += "	AND CN9.CN9_SITUAC = '05' " + CRLF
			cQuery += "	AND CN9.CN9_ESPCTR = '2' " + CRLF
			cQuery += "	AND CN9.CN9_TPCTO = '013' " + CRLF
			cQuery += CRLF
			cQuery += " INNER JOIN " + RetSqlName("CNB") + " CNB ON " + CRLF
			cQuery += "	CNB.D_E_L_E_T_ = ' ' " + CRLF
			cQuery += "	AND CNB.CNB_FILIAL = '" + xFilial("CNB") + "' " + CRLF
			cQuery += "	AND CNB.CNB_CONTRA = CN9.CN9_NUMERO " + CRLF
			cQuery += "	AND CNB.CNB_REVISA = CN9.CN9_REVISA " + CRLF
			cQuery += "	AND CNB.CNB_SITUAC IN ('A','G') " + CRLF
			cQuery += CRLF		
			cQuery += " WHERE " + CRLF
			cQuery += " 	SA1.D_E_L_E_T_ = ' ' " + CRLF
			cQuery += "	AND SA1.A1_FILIAL = '" + xFilial("SA1") + "' " + CRLF
			cQuery += "	AND SA1.A1_CGC = '" + cCGCAgr + "' " + CRLF
			cQuery += "	AND CONCAT(SA1.A1_COD,SA1.A1_LOJA) NOT IN ('"+_Cliente+_Loja+"') " + CRLF 
			cQuery += CRLF
			cQuery += " GROUP BY " + CRLF
			cQuery += "	 SA1.A1_FILIAL " + CRLF
			cQuery += "	,SA1.A1_COD " + CRLF
			cQuery += "	,SA1.A1_LOJA " + CRLF
			cQuery += "	,CN9.CN9_NUMERO " + CRLF
			
			dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cCNBAlias, .T., .F. )
			dbSelectArea( cCNBAlias )

			If ( cCNBAlias )->( QTDCGC ) > 0
				lRet := MsgYesNo("O CNPJ "+ cCGCAgr + " consta cadastrado em nossa base de clientes no código "+( cCNBAlias )->A1_COD+( cCNBAlias )->A1_LOJA +", por favor, avaliar!"+CRLF+"Deseja Continuar?",STR0075)// "Inválido"
			EndIf
			( cCNBAlias )->(DbCloseArea())
			
			If lRet
			//Avalia se o Contrato tem CNPJs agregados nos clientes corporativos, já cadastrados
				cQuery := "SELECT COUNT(1) QTDCGC, ZAL.ZAL_FILIAL, ZAL.ZAL_CLIENT, ZAL.ZAL_LOJA, ZAL.ZAL_CONTRA, ZAL.ZAL_ANOREF" + CRLF
				cQuery += " FROM "+RetSqlName("ZAL")+" ZAL" + CRLF
				cQuery += " WHERE" + CRLF
				cQuery += " ZAL.ZAL_FILIAL = '"+xFilial("ZAL")+"'" + CRLF
				cQuery += " AND ZAL.ZAL_CGCAGR = '" + cCGCAgr + "'" + CRLF
				cQuery += " AND ZAL.ZAL_ANOREF = '" + cAnoVld + "'" + CRLF
				cQuery += " AND ZAL.ZAL_STATUS IN ('A','C')" + CRLF
				cQuery += " AND CONCAT(ZAL.ZAL_CONTRA,ZAL.ZAL_REVISA) <> '"+_Contrato+_Versao+"' " + CRLF
				cQuery += " AND CONCAT(ZAL.ZAL_CLIENT,ZAL.ZAL_LOJA) <> '"+_Cliente+_Loja+"' " + CRLF
				cQuery += " AND ZAL.D_E_L_E_T_ = ' ' " + CRLF
				cQuery += " GROUP BY ZAL.ZAL_FILIAL, ZAL.ZAL_CLIENT, ZAL.ZAL_LOJA, ZAL.ZAL_CONTRA, ZAL.ZAL_ANOREF "
	
				dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cZALAlias, .T., .F. )
				dbSelectArea( cZALAlias )
				AEval( ZAL->( dbStruct() ), { | x | IIf( x[ 2 ] != 'C' , TcSetField( ( cZALAlias ) ,AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil  ) } )
	
				If ( cZALAlias )->( QTDCGC ) > 0
					lRet := MsgYesNo("O CNPJ "+ cCGCAgr + " consta cadastrado como agregado em nossa base de cliente no código "+( cZALAlias )->ZAL_CLIENTE+( cZALAlias )->ZAL_LOJA +", por favor, avaliar!"+CRLF+" Deseja Continuar?",STR0075)// "CNPJ Agregado já existente no cadastro para Filial" ### "Cliente" ### "Contrato" ### "em" ### "Inválido"
				EndIf
			EndIf
		EndIf

		For nY := 1 to Len(oGetMet:aCols)
			If cCGCAgr == STRTRAN(STRTRAN(STRTRAN(oGetMet:aCols[nY,nPosCGC],'.',''),'/',''),'-','') .And. oGetMet:aCols[nY,nPosStat] == 'A'
				nCGCDPL := nCGCDPL + 1
			EndIf
		Next
		If "ZAL_CGCAGR" $ READVAR()
			If nCGCDPL > 0
				Help(,,"Help",,STR0076,1,0)	// "CNPJ Agregado Duplicado!"
				lRet := .F.
			EndIf
		Else
			If nCGCDPL > 1
				Help(,,"Help",,STR0076,1,0)	// "CNPJ Agregado Duplicado!"
				lRet := .F.
			EndIf
		EndIf

	EndIf

	IIf( Select( cZALAlias ) > 0, ( cZALAlias )->( dbCloseArea() ), Nil )

	If !("." $ cCGCAgr)
		If oGetMet:aCols[ nI ][ nPosTPCNPJ ] == "1"
			oGetMet:aCols[ nI ][nPosCGC] := Transform(cCGCAgr,IIF(cPaisLoc="BRA","@R 99.999.999/9999-99",IIF(cPaisloc="ARG","@R 99-99999999-9","@!"))) //Transform(cCGCAgr,"@R 99.999.999/9999-99")
		ElseIf oGetMet:aCols[ nI ][ nPosTPCNPJ ] == "2"
			oGetMet:aCols[ nI ][nPosCGC] := Transform(cCGCAgr,"@R 999.999.999-99")
		Else
			oGetMet:aCols[ nI ][nPosCGC] := Transform(cCGCAgr,"@!")
		EndIf
	EndIf
	oGetMet:Refresh()

	RestArea( aArea )

Return lRet

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A16 ³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Inicializador padrao do ZAL_Indice                         ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A16()                                                 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A16()

	Local _cRet		:= " "

	If Len(aProdCorp) > 0
		_cRet := aProdCorp[1,8]
	EndIf


Return _cRet

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    U_GV007A17 ³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Botão para visualização do calculo na tela de métricas     ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A17(_Cliente,_Loja)                                ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A17(_Cliente,_Loja)

	Local oListBox := Nil
	Local _oDlg := Nil

	Local aArea := GetArea()
	Local aListBox := {}

	Local cQuery := ""
	Local cValG := ""
	Local cAnoRef := StrZero(Year(dDataBase),4)

	Local _lContinua := .F.
	
	lConfirm := .F.
	lRecalc := .F.

	//If MsgYesNo(STR0078)	// "Para geração do cálculo os dados deverão ser gravados. Deseja gravar os dados agora?","Gravação Cálculo"
	If AVISO( 'Gravação do Calculo', 'Para geração do cálculo os dados deverão ser gravados. Deseja gravar os dados agora?',{'Não','Sim'}, 2 ) == 2 //	
		If U_GV007A13(lConfirm,lRecalc,,cAnoRef)
			MsgRun(STR0079,STR0080,{|| U_GV007A02(),_lContinua := U_TGCVA034(_Cliente,_Loja) })	// "Recalculo Incremento" ### "Aguarde ..."
		Endif

		If _lContinua
			While PH1->(!Eof()) .And. PH0->(PH0_FILIAL+PH0_CODIGO) == PH1->(PH1_FILIAL+PH1_CODIGO)
				If PH1->PH1_TIPPRD == "1"
					cValG := IIf(PH1->PH1_VLRCAL==0.01,"","1")
					Exit
				EndIf
				PH1->(DbSkip())
			EndDo
		EndIf
		
		If _lContinua
			cQuery := " SELECT DISTINCT " + CRLF
			cQuery += " 	 PH1.PH1_ITEM " + CRLF
			cQuery += "	,PH0.PH0_CONTRA " + CRLF
			cQuery += "	,PH0.PH0_VERATU " + CRLF
			cQuery += "	,PH1.PH1_CODPRD " + CRLF
			cQuery += "	,SB1.B1_DESC " + CRLF
			cQuery += "	,PH1.PH1_VLRCAL " + CRLF
			cQuery += " FROM " + RetSqlName("PH0") + " PH0 " + CRLF
			cQuery += CRLF 
			cQuery += " INNER JOIN " + RetSqlName("PH1") + " PH1 ON " + CRLF
			cQuery += "	PH1.D_E_L_E_T_= ' ' " + CRLF
			cQuery += "	AND PH1.PH1_FILIAL = '" + xFilial("PH1") + "' " + CRLF
			cQuery += "	AND PH1.PH1_CODIGO = PH0.PH0_CODIGO " + CRLF 
			cQuery += CRLF	
			cQuery += " LEFT JOIN " + RetSqlName("SB1") + " SB1 ON " + CRLF
			cQuery += "	SB1.D_E_L_E_T_ = ' ' " + CRLF
			cQuery += "	AND SB1.B1_FILIAL = '" + xFilial("SB1") + "' " + CRLF
			cQuery += "	AND SB1.B1_COD = PH1_CODPRD " + CRLF
			cQuery += CRLF 
			cQuery += " WHERE PH0.D_E_L_E_T_ = ' ' " + CRLF
			cQuery += "	AND PH0.PH0_FILIAL  = '" + xFilial("PH0") + "' " + CRLF
			cQuery += "	AND PH0.PH0_CODIGO = '" + PH0->PH0_CODIGO + "' " + CRLF
			cQuery += "	AND PH0.PH0_CLIENT = '" + _Cliente + "' " + CRLF
			cQuery += CRLF
			cQuery += " ORDER BY " + CRLF
			cQuery += "	PH1.PH1_ITEM " + CRLF
			
			aListBox := U_GV001X23(cQuery,,,.T.)
		
			If !Empty(aListBox)
				// Monta a tela para usuario visualizar consulta
				DEFINE MSDIALOG _oDlg TITLE STR0040 FROM 0,0 TO 490,1000 PIXEL  // "Cálculos"
				
				//"Seq." ### "Contrato" ### "Versão" ### "Produto" ### "Descrição" ### "Valor"
				@ 10,10 LISTBOX oListBox FIELDS HEADER STR0083,STR0005,STR0084,STR0085,STR0086,STR0087 SIZE 480,205 OF _oDlg PIXEL
			
				oListBox:SetArray( aListBox )
				oListBox:bLine := {|| {aListBox[oListBox:nAt,1],;
					aListBox[oListBox:nAt,2],;
					aListBox[oListBox:nAt,3],;
					aListBox[oListBox:nAt,4],;
					aListBox[oListBox:nAt,5],;
					aListBox[oListBox:nAt,6]}}
			
				DEFINE SBUTTON FROM 217,453 TYPE 1 ACTION _oDlg:End() ENABLE OF _oDlg
				
				ACTIVATE MSDIALOG _oDlg CENTER
		
				If MsgYesNo("Cálculo Efetuado. Deseja Enviar WorkFlow para o Executivo?")
					U_GV001X17(_Cliente,_Loja,PH0->PH0_CALC,cValG,PH0->PH0_EXCECA)
				EndIf
			Else
				Aviso(STR0040,STR0082,{"Ok"})	// "Cálculos" ### "Nao existe dados calculados a consultar"
			EndIf
		EndIf
	EndIf

	RestArea(aArea)
	
Return

/*
ÉÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»
ºPrograma³tdiSaveMotivo ºAutor  ³TOTVS - Microsiga   º Data ³    /  /     º
ÌÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹
ºDesc.   ³Grava o Motivo na tabela de LOG.                             	  º
ÈÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼
*/

Static Function tdiSaveMotivo( cMotivo, cCausa, cCodCha, cObs, cContrato, cVersao, cOrig )

	Local lRet 			:= .F.

	Default cOrig		:= ""
	default cContrato	:= CN9->CN9_NUMERO
	default cVersao		:= CN9->CN9_REVISA
	default cMotivo		:= "006"
	Default cCausa		:= "014"


//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³Gera Log do Motivo para nova Versao do Contrato.³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	DBSELECTAREA("PBI")
	If PBI->(RECLOCK("PBI",.T.))

		PBI->PBI_FILIAL := xFilial("PBI")
		PBI->PBI_CONTRA	:= cContrato
		PBI->PBI_VERSAO := cVersao
		PBI->PBI_USUARI := cUserName
		PBI->PBI_DTOPER := dDataBase
		PBI->PBI_STATUS := ""
		PBI->PBI_MOTIVO := Upper(cMotivo) + "/" + Upper(cCausa)
		PBI->PBI_ORIGEM := cOrig
		PBI->PBI_OBS 	:= Upper(cObs)
		PBI->PBI_CODCHA	:= cCodCha
		PBI->PBI_ENVIO	:= '2'
		PBI->PBI_COMPET := Substr( DTOS(dDataBase),5,2) + "/" + SUBSTR(DTOS(dDataBase),1,4 )

		PBI->( MsUnlock() )

		lRet := .T.
	EndIf

Return(lRet)

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A18 ³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Validação ZAX_PRCDU no GetInd                              ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A18()                                                 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A18()

	Local nPosPCDU 	:= aScan(aHeader,{|x| Alltrim(x[2])=="ZAX_PRCDU"})
	Local cAnoCorp	:= GETMV("TDI_ANOCOR",,"2010")
	Local cQuery    := ''
	Local aArea		:= GetArea()
	Local cAlias	:= GetNextAlias()
	Local lRet		:= .T.


	cQuery	+= "SELECT COUNT(1) QTDPRD, SB1.B1_COD, SB1.B1_GRUPO "+CRLF
	cQuery	+= " FROM " + RetSqlName("SB1")+" SB1 "+CRLF
	cQuery	+=         "INNER JOIN " + RetFullName("ZQJ","00") + " ZQJ ON ZQJ.ZQJ_FILIAL = '"+ xFilial("ZQJ")+"' AND ZQJ_CODESP = '000001' AND ZQJ.ZQJ_MSBLQL <> '1' AND ZQJ.ZQJ_TIPREC = '1' AND ZQJ.D_E_L_E_T_ = ' ' "+CRLF
	cQuery	+=         "INNER JOIN " + RetFullName("ZQK","00") + " ZQK ON ZQK.ZQK_FILIAL = '"+ xFilial("ZQK")+"' AND ZQK.ZQK_CODZQJ = ZQJ.ZQJ_CODIGO AND ZQK.ZQK_GRUPO = SB1.B1_GRUPO  AND ZQK.D_E_L_E_T_ = ' ' "+CRLF
	cQuery	+=  "WHERE SB1.D_E_L_E_T_ = ' ' "+CRLF
	cQuery	+=    "AND SB1.B1_COD = '"+ &(READVAR())+"' "+CRLF
	cQuery	+= "GROUP BY SB1.B1_COD, SB1.B1_GRUPO"+CRLF

	dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cAlias, .T., .F. )
	dbSelectArea( cAlias )
	AEval( SB1->( dbStruct() ), { | x | IIf( x[ 2 ] != 'C' , TcSetField( ( cAlias ) ,AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil  ) } )
//	( cZALAlias )->( dbEval( { || nTotReg ++ },,{ || !Eof() } ) )

	If ( cAlias )->( QTDPRD ) = 0
		Help(,,"Help",,STR0093,1,0)	// "Produto não faz parte do grupo CDU!","Inválido"
		lRet := .F.
	EndIf

Return lRet
/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A19 ³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Validação ZAX_REFMT1 no GetInd                             ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A19()                                                ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A19()

	Local nPosRMT1 	:= aScan(aHeader,{|x| Alltrim(x[2])=="ZAX_REFMT1"})
	Local lRet		:= .T.

	If oGetInd:nAT > 1
		If !(&(READVAR()) > oGetInd:aCols[oGetInd:nAT-1,nPosRMT1])
			Help(,,"Help",,STR0094,1,0)	// "A Metrica Inicial deve ser maior que a metrica anterior!"
			lRet := .F.
		EndIf
	EndIf

Return lRet
/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A20 ³ Autor ³ TDI-Desenvolvimento	³ Data ³ 10.11.10 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Validação ZAX_REFMT2 no GetInd                             ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A20()                                                ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function GV007A20()

	Local nPosRMT2 	:= aScan(aHeader,{|x| Alltrim(x[2])=="ZAX_REFMT2"})
	Local lRet		:= .T.

	If oGetInd:nAT > 1
		If !(&(READVAR()) > oGetInd:aCols[oGetInd:nAT-1,nPosRMT2])
			Help(,,"Help",,STR0095,1,0)	// "A Metrica Final deve ser maior que a metrica anterior!"
			lRet := .F.
		EndIf
	EndIf

Return lRet

/*/{Protheus.doc} U_GV007A21

<AUTHOR>
@Since ??/??/????
@Version ??
/*/

User Function GV007A21(_Cliente, _Loja)

	Local cAnoCorp	:= GETMV("TDI_ANOCOR",,"2010")
	Local lAjustRot := GetMv("TI_RV2EMP",,.T.) 
	
	If lAjustRot .And. empty(_Versao)
		_Versao := U_TiGetContr(_Contrato)
	EndIf


	DbSelectArea("ZAL")
	DbSetOrder(1)
	If !DbSeek(xFilial("ZAL")+_Cliente+_Loja)
		RecLock("ZAL",.T.)
			ZAL->ZAL_FILIAL := xFilial("ZAL")
			ZAL->ZAL_CLIENT := _Cliente
			ZAL->ZAL_LOJA := _Loja
			ZAL->ZAL_CONTRA := _Contrato
			ZAL->ZAL_REVISA := _Versao
			ZAL->ZAL_ANOREF := cAnoCorp
			ZAL->ZAL_STATUS := "A"
		ZAL->(MsUnLock())
	EndIf

Return

/*/{Protheus.doc} U_GV007A22

<AUTHOR>
@Since ??/??/????
@Version ??
/*/

User Function GV007A22()

	Local cRet 		:= &(READVAR())
	Local nPosMTIA 	:= aScan(aHeadAno,{|x| Alltrim(x[2])=="ZAL_MTRINF"})
	Local nPosMTRA 	:= aScan(aHeadAno,{|x| Alltrim(x[2])=="ZAL_MTRREA"})
	Local nPosMTIM 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRINF"})
	Local nPosMTRM 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRREA"})
	Local nPosMTDM 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_MTRDIP"})
	Local nPosStat 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_STATUS"})
	Local nPosISta	:=	aScan(aHeadInd,{|x| Alltrim(x[2])=="ZAX_STATUS"}) 
	Local cMaiorMT	:= GETMV("MV_CPMAIOR",,.T.)
	Local nY 			:= 0
	Local nMTReaAtu	:= 0
	Local nMTInfAtu	:= 0
	Local nTotInat	:= 0

	For nY := 1 to Len(oGetMet:aCols)
		If oGetMet:aCols[nY][nPosStat] == 'A'
			If cMaiorMT
				If oGetMet:aCols[nY][nPosMTDM] > oGetMet:aCols[nY][nPosMTIM]
					nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
					nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTDM]
				Else
					nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
					nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTIM]
				EndIf
			Else
				If oGetMet:aCols[nY][nPosMTDM] > 0
					nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
					nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTDM]
				Else
					nMTReaAtu := nMTReaAtu + oGetMet:aCols[nY][nPosMTRM]
					nMTInfAtu := nMTInfAtu + oGetMet:aCols[nY][nPosMTIM]
				EndIf
			EndIf
		ElseIf oGetMet:aCols[nY][nPosStat] == 'I'
			nTotInat++
		EndIf
	Next
	
	If nTotInat == Len(oGetMet:aCols)
		For nY :=1 to Len(OGETIND:ACOLS)
			oGetInd:ACOLS[nY][nPosISta]	:= 	"I"	
		Next
		_lChangZAX := .T.
		oGetInd:Refresh()	
	EndIf
	oGetAno:aCols[oGetAno:nAT][nPosMTIA] := nMTInfAtu
	oGetAno:aCols[oGetAno:nAT][nPosMTRA] := nMTReaAtu

	oGetMet:Refresh()
	oGetAno:Refresh()

Return cRet

/*/{Protheus.doc} U_GV007A24
Função para atualizar os dados dos objetos da Tela.

<AUTHOR> Stenio
@Since 13/11/12
@Since 11.5

@obs 16/11/12 - Vitor Ribeiro - Função foi alterada de Static Function para function e refeita para atender o projeto do corporativo. Porem seu conceito inicial não foi alterado.

@Param cAnoRef, caracter, contem o ano.
/*/

User Function GV007A24(cAnoRef)

	Local cAnoCorp := GETMV("TDI_ANOCOR",,"2010")

	Local nCount := 0
	Local nPosCorp := 0
	Local nPosCGC := aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_CGCAGR"})
	Local nPosTp := aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_TPCNPJ"})

	// Carrega os lancamentos de Excecoes
	U_GV007A10()

	// Carrega os lancamentos totalizadores dos anos
	U_GV007A07(@cAnoRef)

	If Len(aMetricas[2]) > 0

		nPosCorp:= Ascan(oGetAno:aCols,{|x| x[1] == cAnoCorp})

		If nPosCorp > 0
			// Atualiza os objetos das NewGetdados dos Anos de Referencia, Metricas e Indices
			For nCount := 1 to Len(aMetricas [ 2 ])
				oGetAno:aCols[ nCount ] := aClone( aMetricas [ 2 ][ nCount ][ 1 ])
			Next

			oGetAno:oBrowse:nAT := nPosCorp
			oGetAno:Refresh()

			aColsInd := aClone(aMetricas[2][nPosCorp][2])

			oGetInd:aCols := aClone(aMetricas[2][nPosCorp][2])
			oGetInd:oBrowse:nAT := 1
			oGetInd:Refresh()

			aColsMet := aClone(aMetricas[2][nPosCorp][3])
			oGetMet:aCols := aClone(aMetricas[2][nPosCorp][3])

			For nCount := 1 To Len(oGetMet:aCols)
				oGetMet:aCols[nCount][nPosCGC] := StrTran(StrTran(StrTran(oGetMet:aCols[nCount][nPosCGC],'.',''),'/',''),'-','')
				If oGetMet:aCols[nCount][nPosTP] == "1"
					oGetMet:aCols[nCount][nPosCGC] := Transform(oGetMet:aCols[nCount][nPosCGC],IIF(cPaisLoc="BRA","@R 99.999.999/9999-99",IIF(cPaisloc="ARG","@R 99-99999999-9","@!")))
				ElseIf oGetMet:aCols[nCount][nPosTP] == "2"
					oGetMet:aCols[nCount][nPosCGC] := Transform(oGetMet:aCols[nCount][nPosCGC],"@R 999.999.999-99")
				Else
					oGetMet:aCols[nCount][nPosCGC] := Transform(oGetMet:aCols[nCount][nPosCGC],"@!")
				EndIf
			Next

			oGetMet:oBrowse:nAT := 1
			oGetMet:Refresh()
		EndIf
	EndIf

Return

/*/{Protheus.doc} _fVerifica
Funcão para verificar se as exceções foram cadastradas corretamente na hora da confirmação da rotina.

<AUTHOR> Ribeiro
@since 03/09/2013
@version 11.5
/*/

Static Function _fVerifica()

	Local _lContinua := .T.

	_lExcecOk := U_GV007A26(.T.)

	If !_lExcecOk
		Help(,,"Help",,"Existe problemas com as exceções! Verifique se não existe exceção com o mesmo motivo estando dentro da mesma vigência.",1,0)
		_lContinua := .F.
	EndIf

	If _lContinua
		VerifEmail()
	EndIf

Return _lContinua

/*/{Protheus.doc} AtualizPbf
Funcão para atualizar as tabela PBF quando a exceção for alterada.
Obs.: Para esta funcao, tem que estar posicionado na excecao referente a alteracao(ZAW).

<AUTHOR> Ribeiro
@since 04/09/2013
@version 11.5

@param _dNovaVige, Data, Contém a nova vigência.
/*/

Static Function AtualizPbf(_dNovaVige)

	Local _cContrato := ""

	If AllTrim(ZAW->ZAW_CODMOT) == "02"

		_cContrato := Posicione("CN9",1,xFilial("CN9")+ZAW->(ZAW_CLIENT+ZAW_LOJA),"CN9_NUMERO")

		DbSelectArea("PH4")
		PH4->(DbSetOrder(2))	// PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_CMPDE+PH4_CMPATE // consultar/confirmar indice

		If PH4->(DbSeek(xFilial("PH4")+_cContrato))
			While PH4->(!Eof()) .And. xFilial("PH4")+_cContrato == PH4->PH4_FILIAL+PH4_CONTRA

				If PH4->(PH4_CMPINI+PH4_CMPFIM) == Substr(DTOS(ZAW->ZAW_VIGEDE),1,4)+Substr(DTOS(ZAW->ZAW_VIGENC),5,2) .And.;
						((ZAW->ZAW_TIPO == "1" .And. "CDU" $ PH4->PH4_LINHAR) .Or. ((ZAW->ZAW_TIPO == "2" .And. "SMS" $ PH4->PH4_LINHAR)))
					RecLock("PH4",.F.)
					PH4->PH4_CMPFIM := Substr(Dtos(_dNovaVige),1,6)
					PH4->(MsUnLock())
					Exit
				EndIf

				PH4->(DbSkip())
			EndDo
		EndIf

	EndIf

Return

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡„o    ³U_GV007A25  ³ Autor ³ Leo Kume		       ³ Data ³ 13.11.12 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡„o ³ Altera o cnpj para a picture                               ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Sintaxe e ³ U_GV007A25()                                                 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³Uso       ³ TGCVA007                                                  ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÁÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/

User function GV007A25()

	Local nPosCGC 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_CGCAGR"})
	Local nPosTp 	:= aScan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_TPCNPJ"})
	Local cCGCAgr := oGetMet:aCols[ oGetMet:nAt ][nPosCGC]
	Local cTpCpnj := oGetMet:aCols[ oGetMet:nAt ][ nPosTp ]

	cCGCAgr		:= STRTRAN(STRTRAN(STRTRAN(cCGCAgr,'.',''),'/',''),'-','')

	If cTpCpnj == "1"
		cCGCAgr := Transform(cCGCAgr,IIF(cPaisLoc="BRA","@R 99.999.999/9999-99",IIF(cPaisloc="ARG","@R 99-99999999-9","@!")))
	ElseIf cTpCpnj == "2"
		cCGCAgr := Transform(cCGCAgr,"@R 999.999.999-99")
	Else
		cCGCAgr := Transform(cCGCAgr,"@!")
	EndIf

Return cCGCAgr

/*/{Protheus.doc} U_GV007A12
Função para validação do campo(sx3).

<AUTHOR> Ribeiro
@Since 11/04/2014
@Version 11.5
/*/

User Function GV007A12()

	Local _nCount := 0
	Local _nQtdCnpj := 0
	Local _nCnpjs := aScan(aHeadMet,{|x| Alltrim(x[2]) == "ZAL_CGCAGR" })

	Local _cRaiz := ""
	Local _cConteudo := &(ReadVar())

	Local _lU_TGCVA011 := FunName() = "TGCVA011"

	If _lU_TGCVA011
		_nCnpjs := aScan(aHeadER,{|x| Alltrim(x[2]) == "ZAL_CGCAGR" })
		_cRaiz := SubStr(StrTran(StrTran(StrTran(aCols[n][_nCnpjs],'.',''),'/',''),'-',''),1,8)
	
		For _nCount := 1 To Len(aCols)
			If _cRaiz == SubStr(StrTran(StrTran(StrTran(aCols[_nCount][_nCnpjs],'.',''),'/',''),'-',''),1,8)
				_nQtdCnpj++
				If _nQtdCnpj > 1
					Exit
				EndIf
			EndIf
		Next
	
	Else
		_cRaiz := SubStr(StrTran(StrTran(StrTran(oGetMet:aCols[oGetMet:nAt][_nCnpjs],'.',''),'/',''),'-',''),1,8)
		
		For _nCount := 1 To Len(oGetMet:aCols)
		If _cRaiz == SubStr(StrTran(StrTran(StrTran(oGetMet:aCols[_nCount][_nCnpjs],'.',''),'/',''),'-',''),1,8)
			_nQtdCnpj++
			If _nQtdCnpj > 1
				Exit
			EndIf
		EndIf
	Next
	
	EndIf

	If _lDocEntCp .And. _nQtdCnpj > 1
		_lDocEntCp := MsgYesNo("Deseja replicar a opção para todos os CNPJs do CNPJ raiz?")
		If _lDocEntCp
			U_GV007A27()
			_fDocEntra(_cConteudo,_cRaiz,_lU_TGCVA011)
			If _lU_TGCVA011
				U_GV011A02(_cConteudo,_cRaiz)
			EndIf
		EndIf
	EndIf
	_lDocEntCp := .T.
Return .T.

/*/{Protheus.doc} _fDocEntra
Função para preencher todos os campos da mesma raiz do cnpj.

<AUTHOR> Ribeiro
@Since 11/04/2014
@Version 11.5

@Param _cConteudo, caracter, conteudo que será incluso no campo.
@Param _cRaiz, caracter, raiz do cnpj.
/*/

Static Function _fDocEntra(_cConteudo,_cRaiz,_lU_TGCVA011)

	Local _nCount := 0
	Local _nDocEnt := aScan(aHeadMet,{|x| Alltrim(x[2]) == "ZAL_DOCENT" })
	Local _nCnpjs := aScan(aHeadMet,{|x| Alltrim(x[2]) == "ZAL_CGCAGR" })

	For _nCount := 1 To Len(oGetMet:aCols)
		If SubStr(StrTran(StrTran(StrTran(oGetMet:aCols[_nCount][_nCnpjs],'.',''),'/',''),'-',''),1,8) == _cRaiz
			oGetMet:aCols[_nCount][_nDocEnt] := _cConteudo

			If !_lU_TGCVA011 .And. ExistTrigger("ZAL_DOCENT")
				RunTrigger(2,_nCount,,,"ZAL_DOCENT")
			EndIf
		EndIf
	Next

Return

/*/{Protheus.doc} _fDocEntra
Função para verificar se envia e-mail para o executivo da conta com as informações de alteração do cliente.

<AUTHOR> Ribeiro
@Since 11/04/2014
@Version 11.5
/*/

Static Function VerifEmail()

	Local _nCount := 0
	Local _nPosCGC := Ascan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_CGCAGR"})
	Local _nPosNom := Ascan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_NOMAGR"})
	Local _nPosAno := Ascan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_ANOREF"})
	Local _nPosSta := Ascan(aHeadMet,{|x| Alltrim(x[2])=="ZAL_STATUS"})

	Local _aCgcAlt := {}

	Local _cCGCAgr := ""
	Local _cStsAnt := ""
	Local _cStsDep := ""

	ZAL->(DbSetOrder(3))

	For _nCount := 1 To Len(oGetMet:aCols)
		_cCGCAgr := PadR(StrTran(StrTran(StrTran(oGetMet:aCols[_nCount][_nPosCGC],'.',''),'/',''),'-',''),TamSx3("ZAL_CGCAGR")[1]," ")

		If !ZAL->(DbSeek(xFilial("ZAL")+_cCGCAgr+CNC->CNC_CLIENT+oGetMet:aCols[_nCount][_nPosAno]))
			Aadd(_aCgcAlt,{oGetMet:aCols[_nCount][_nPosCGC],AllTrim(oGetMet:aCols[_nCount][_nPosNom]),"","Inclusão"})
		ElseIf ZAL->ZAL_STATUS <> oGetMet:aCols[_nCount][_nPosSta]
			_cStsAnt := If(ZAL->ZAL_STATUS=="A","Ativo",If(ZAL->ZAL_STATUS=="C","Consulta","Inativo"))
			_cStsDep := If(oGetMet:aCols[_nCount][_nPosSta]=="A","Ativo",If(oGetMet:aCols[_nCount][_nPosSta]=="C","Consulta","Inativo"))

			Aadd(_aCgcAlt,{oGetMet:aCols[_nCount][_nPosCGC],AllTrim(oGetMet:aCols[_nCount][_nPosNom]),_cStsAnt,_cStsDep})
		EndIf
	Next

	If !Empty(_aCgcAlt)
		EnviaEMail(_aCgcAlt)
	EndIf

Return

/*/{Protheus.doc} EnviaEMail
Função para enviar e-mail.

<AUTHOR> Ribeiro
@Since 11/04/2014
@Version 11.5

@Param _aCgcAlt, array, contem o cnpjs alterados no contrato.
/*/

Static Function EnviaEMail(_aCgcAlt)

	Local _cOrigem := GETMV("MV_RELACNT",,'<EMAIL>')
	Local _cDestino := ""
	Local _cCopy := ""
	Local _cAssunto := "Alteração de Cnpj's no contrato do cliente - " + AllTrim(CN9->CN9_NUMERO)
	Local _cTexto := ""

	SA1->(DbSetOrder(1))
	SA1->(DbSeek(xFilial("SA1")+CNC->(CNC_CLIENT+CNC_LOJACL)))

	_cDestino := BuscaDest()
	_cCopy := GetMv("MV_#CORPM2",,"")
	
	If !Empty(_cDestino) .Or. !Empty(_cCopy)

		// Se não houver destino, só envia para a cópia.
		If Empty(_cDestino)
			_cDestino := _cCopy
			_cCopy := ""
		EndIf
		
		_cTexto := MakeBody(_aCgcAlt)
	
		U_GV001X27(_cDestino,_cAssunto,_cTexto,,,_cCopy,_cOrigem,,.T.)
	EndIf
	
Return

/*/{Protheus.doc} BuscaDest
Função para buscar os destinos do e-mail.

<AUTHOR> Ribeiro
@Since 11/04/2014
@Version 11.5
/*/

Static Function BuscaDest()

	Local _cCodGar := ""

	Local _cDestino := ""

	AZS->(DbSetOrder(4))
	If AZS->(DbSeek(xFilial("AZS")+SA1->A1_VEND))
		SA3->(DbSetOrder(1))
		If SA3->(DbSeek(xFilial("SA3")+SA1->A1_VEND))
			_cDestino := SA3->A3_EMAIL
			_cCodGar := U_TCRMXSUP(AZS->AZS_CODUSR,"000002",2)
			_cCodGar := Iif(_cCodGar=="MAINGR",Space(6),_cCodGar)
	
		If !Empty(_cCodGar)
			If SA3->(DbSeek(xFilial("SA3")+_cCodGar))
				If !Empty(_cDestino)
					_cDestino += ";"
				EndIf
				_cDestino += SA3->A3_EMAIL
			EndIf
		EndIf
		EndIf
	EndIf

Return _cDestino

/*/{Protheus.doc} MakeBody
Função para montar o corpo do e-mail.

<AUTHOR> Ribeiro
@Since 11/04/2014
@Version 11.5

@Param _aCgcAlt, array, contem o cnpjs alterados no contrato.
/*/

Static Function MakeBody(_aCgcAlt)

	Local _nCount := 0

	Local _cBody    := ""

	_cBody := "<!DOCTYPE HTML PUBLIC '-//W3C//DTD HTML 4.01 Transitional//EN'>"
	_cBody += "<html>"
	_cBody += "	<head>"
	_cBody += "		<title>Documento sem t&iacute;tulo</title>"
	_cBody += "		<meta http-equiv='Content-Type' content='text/html; charset=iso-8859-1'>"
	_cBody += "	</head>"
	_cBody += "	<body topmargin='0' leftmargin='0' rightmargin='0'>"
	_cBody += "		<br>"
	_cBody += "		<table border='0' cellspacing='0' cellpadding='0' width='879'>"
	_cBody += "			<tr>"
	_cBody += "				<td><font face='Arial'><img border='0' src='http://gps.totvs.com/workflow/titulo_wf_totvs.png' width='879' height='62'></font></td>"
	_cBody += "			</tr>"
	_cBody += "			<tr>"
	_cBody += "				<td>"
	_cBody += "					<p>"
	_cBody += "						<font color='#333333' size='2' face='Verdana, Arial, Helvetica, sans-serif'>"
	_cBody += "							Prezado Executivo(a),"
	_cBody += "							<br><br>"
	_cBody += "							Informamos que foi alterado os Cnpj's no contrato corporativo do cliente " + CNC->CNC_CLIENT+"/"+CNC->CNC_LOJACL+" " + AllTrim(SA1->A1_NOME) + " na data do dia " + DtoC(dDataBase) + "."
	_cBody += "							<br><br>"
	_cBody += "							Abaixo os CNPJs agregados que foram alterados:"
	_cBody += "						</font>"
	_cBody += "					</p>"
	_cBody += "				</td>"
	_cBody += "			</tr>"
	_cBody += "			<tr>"
	_cBody += "				<td>"
	_cBody += "					<p>"
	_cBody += "						<table border=1 cellspacing=0 width=879>"
	_cBody += "							<tr>"
	_cBody += "								<td><b>Cnpj</b></td>"
	_cBody += "								<td><b>Nome</b></td>"
	_cBody += "								<td><b>Alterado de</b></td>"
	_cBody += "								<td><b>Para</b></td>"
	_cBody += "							</tr>" + CRLF
	For _nCount := 1 To Len(_aCgcAlt)
		_cBody += "							<tr>"
		_cBody += "								<td>" + _aCgcAlt[_nCount][1] + "</td>"
		_cBody += "								<td>" + _aCgcAlt[_nCount][2] + "</td>"
		_cBody += "								<td>" + _aCgcAlt[_nCount][3] + "</td>"
		_cBody += "								<td>" + _aCgcAlt[_nCount][4] + "</td>"
		_cBody += "							</tr>"
	Next nX
	_cBody += "						</table>"
	_cBody += "					</p>"
	_cBody += "				</td>"
	_cBody += "			</tr>"
	_cBody += "		</table>"
	_cBody += "	</body>"
	_cBody += "</html>"

Return _cBody

/*/{Protheus.doc} VericaExce
Função para verificar se existe um exceção vigente.

<AUTHOR> Ribeiro
@Since 11/04/2014
@Version 11.5

@Param _cCodCli, caracter, contem o codigo do cliente.
@Param _cLoja, caracter, contem a loja do cliente.
@Param _cCodMot, caracter, contem o codigo do motivo da exceção.
/*/

Static Function VericaExce(_cCodCli,_cLoja,_cCodMot)

	Default _cCodCli := ""
	Default _cLoja := ""
	Default _cCodMot := ""

	If !Empty(_cCodCli) .And. !Empty(_cLoja) .And. !Empty(_cCodMot)
		ZAW->(DbSetOrder(2))
		If ZAW->(DbSeek(xFilial("ZAW")+_cCodCli+_cLoja+_cCodMot))
			While ZAW->(!Eof()) .And. _cCodCli+_cLoja+_cCodMot == AllTrim(ZAW->(ZAW_CLIENT+ZAW_LOJA+ZAW_CODMOT))
				If ZAW->ZAW_VIGEDE < dDataBase .And. dDataBase < ZAW->ZAW_VIGENC
					If SX5->(DbSeek(xfilial('SX5')+PadR("HH",TamSx3("X5_TABELA")[1])+_cCodMot))
						Help(,,"Help",,"O Cliente de codigo " + _cCodCli + " possuí uma exceção '" +  SX5->(FieldGet(FieldPos("X5_DESCRI"))) + "' vigente! Verifique",1,0)
						Exit
					EndIf
				EndIf
				ZAW->(DbSkip())
			EndDo
		EndIf
	EndIf

Return

/*/{Protheus.doc} U_GV007A26
Função para validar se a exceção inclusa está ok.

<AUTHOR> Ribeiro
@since 29/08/2013
@version 11.5

@param _lTudoOk, Lógico, Define se vai verificar todo o aCols ou somente o posicionado.
/*/

User Function GV007A26(_lTudoOk)

	Local _nCount    := 0
	Local _nPosicao  := 0
	Local _nPosVigDe := AScan(oGetExc:aHeader,{|x| AllTrim(x[2]) == "ZAW_VIGEDE"})
	Local _nPosVigAt := AScan(oGetExc:aHeader,{|x| AllTrim(x[2]) == "ZAW_VIGENC"})
	Local _nPosCodMo := AScan(oGetExc:aHeader,{|x| AllTrim(x[2]) == "ZAW_CODMOT"})
	Local _nPosTipo  := AScan(oGetExc:aHeader,{|x| AllTrim(x[2]) == "ZAW_TIPO"})

	Local _lContinua := .T.
	Local _lLoop      := .T.

	Default _lTudoOk := .F.

	If Type("n") == "U"
		n := 1
	EndIf

	While _lLoop
		If !oGetExc:aCols[n][Len(oGetExc:aHeader)+1]
			M->ZAW_VIGEDE := GDFieldGet("ZAW_VIGEDE",,,oGetExc:aHeader,oGetExc:aCols)
			M->ZAW_VIGENC := GDFieldGet("ZAW_VIGENC",,,oGetExc:aHeader,oGetExc:aCols)
			M->ZAW_CODMOT := GDFieldGet("ZAW_CODMOT",,,oGetExc:aHeader,oGetExc:aCols)
			M->ZAW_TIPO := GDFieldGet("ZAW_TIPO",,,oGetExc:aHeader,oGetExc:aCols)
			M->ZAW_VALOR := GDFieldGet("ZAW_VALOR",,,oGetExc:aHeader,oGetExc:aCols)
			M->ZAW_REAJUS := GDFieldGet("ZAW_REAJUS",,,oGetExc:aHeader,oGetExc:aCols)

			If Len(oGetExc:aCols) <> 1 .Or. (M->(!Empty(ZAW_VIGEDE) .Or. !Empty(ZAW_VIGENC) .Or. !Empty(ZAW_CODMOT) .Or. !Empty(ZAW_TIPO) .Or. !Empty(ZAW_VALOR)))

				If Empty(M->ZAW_VIGEDE)
					Help(,,"Help",,"O campo 'Vigencia de' é obrigatório! Verifique.",1,0)
					_lContinua := .F.
				ElseIf Empty(M->ZAW_VIGENC)
					Help(,,"Help",,"O campo 'Vigencia ate' é obrigatório! Verifique.",1,0)
					_lContinua := .F.
				ElseIf Empty(M->ZAW_CODMOT)
					Help(,,"Help",,"O campo 'Cod. Motivo' é obrigatório! Verifique.",1,0)
					_lContinua := .F.
				ElseIf Empty(M->ZAW_TIPO) .And. (AllTrim(M->ZAW_CODMOT) $ "01|02")
					Help(,,"Help",,"Para o motivo selecionado o campo 'Tipo' é obrigatório! Verifique.",1,0)
					_lContinua := .F.
				ElseIf Empty(M->ZAW_REAJUS) .And. (AllTrim(M->ZAW_CODMOT) $ "03") .And. (M->ZAW_VIGENC > dDataBase)
					Help(,,"Help",,"Para o motivo selecionado o campo 'Reajusta?' é obrigatório! Verifique.",1,0)
					_lContinua := .F.
				Else

					If AllTrim(M->ZAW_CODMOT) == "01" .And. Empty(M->ZAW_VALOR)
						Help(,,"Help",,"Para o motivo selecionado o campo 'Percentual' é obrigatório! Verifique.",1,0)
						_lContinua := .F.
					Else
						_nPosicao  := aScan(oGetExc:aCols,{|x| x[_nPosCodMo] == M->ZAW_CODMOT})

						If _nPosicao > 0 .And. _nPosicao <> n
							While n > 1 .And. _nPosicao <= Len(oGetExc:aCols) .And. _nPosicao <> n

								If !oGetExc:aCols[_nPosicao][Len(oGetExc:aHeader)+1]
									If oGetExc:aCols[_nPosicao][_nPosCodMo] == M->ZAW_CODMOT
										If AllTrim(oGetExc:aCols[_nPosicao][_nPosTipo])+AllTrim(oGetExc:aCols[_nPosicao][_nPosCodMo]) ==;
												M->(AllTrim(ZAW_TIPO)+AllTrim(ZAW_CODMOT)) .And.;
												((oGetExc:aCols[_nPosicao][_nPosVigDe] <= M->ZAW_VIGEDE .And. oGetExc:aCols[_nPosicao][_nPosVigAt] >= M->ZAW_VIGEDE) .Or.;
												(oGetExc:aCols[_nPosicao][_nPosVigDe] <= M->ZAW_VIGENC .And. oGetExc:aCols[_nPosicao][_nPosVigAt] >= M->ZAW_VIGENC))
											Help(,,"Help",,"Já existe um exceção que é igual a esta, e que esta dentro do mesmo período! Verifique",1,0)
											_lContinua := .F.
											Exit
										EndIf
									EndIf
								EndIf

								_nPosicao++
							EndDo
						EndIf
					EndIf
				EndIf
			EndIf
		EndIf

		If _lContinua .And. _lTudoOk .And. n < Len(oGetExc:aCols)
			n++
		Else
			_lLoop := .F.
		EndIf
	EndDo

Return _lContinua

/*/{Protheus.doc} U_GV007A27
Função para validar se a metrica foi alterada ou não.

<AUTHOR> Ribeiro
@since 29/08/2013
@version 11.5

@obs As variaveis _lMetriAlt e _lChangZAL são private e lógica Se a metrica foi alterada ou nao.
/*/

User Function GV007A27()

	Local _cCampo := ""

	Local _lContinua := .F.

	Local _nAnoCorp := Val(GetMv("TDI_ANOCOR",,""))	// Ano Corporativo

	Local _nPosicao := 0
	Local _nPosMtIni := 0
	Local _nPosMtInf := 0
	Local _nPosMtDip := 0
	Local _nPosAnoRe := AScan(oGetMet:aHeader,{|x| AllTrim(Upper(x[2])) == "ZAL_ANOREF" })
	Local _nPosCampo := AScan(oGetMet:aHeader,{|x| AllTrim(x[2]) == StrTran(Upper(ReadVar()),"M->","")})	// Posição do campo posicionado

	_cCampo := AllTrim(StrTran(ReadVar(),"M->",""))

	_nPosicao := Ascan(oGetMet:aHeader,{|x| AllTrim(Upper(x[2])) == _cCampo })

	If _nPosicao > 0 .And. !_lChangZAL
		_lChangZAL := &(ReadVar()) <> oGetMet:aCols[n][_nPosicao]
	EndIf

	If _lChangZAL
		If Val(oGetMet:aCols[n][_nPosAnoRe]) <= _nAnoCorp
			If "ZAL_MTRINI" $ ReadVar() .Or. "ZAL_MTRINF" $ ReadVar() .Or. "ZAL_MTRDIP" $ ReadVar()
				_lContinua := .T.
			ElseIf "ZAL_STATUS" $ ReadVar()
				_nPosMtIni := AScan(oGetMet:aHeader,{|x| AllTrim(Upper(x[2])) == "ZAL_MTRINI" })
				_nPosMtInf := AScan(oGetMet:aHeader,{|x| AllTrim(Upper(x[2])) == "ZAL_MTRINF" })
				_nPosMtDip := AScan(oGetMet:aHeader,{|x| AllTrim(Upper(x[2])) == "ZAL_MTRDIP" })

				If oGetMet:aCols[n][_nPosMtIni] > 0 .Or. oGetMet:aCols[n][_nPosMtInf] > 0 .Or. oGetMet:aCols[n][_nPosMtDip] > 0
					_lContinua := .T.
				EndIf
			EndIf

			If _lContinua .And. &(ReadVar()) <> oGetMet:aCols[n][_nPosCampo]
				_lMetriAlt := .T.
			EndIf
		EndIf
	EndIf

Return .T.

/*/{Protheus.doc} U_GV007A28
Função para verificar se a ZAL foi alterada.

<AUTHOR> Ribeiro
@since 29/08/2013
@version 11.5

@obs A variavel_lChangZAL é private e lógica. Se a metrica foi alterada ou nao.
/*/

User Function GV007A28()

	Local _lContinua := .T.

	If _lChangZAL
		_lContinua := MsgNoYes("A métrica foi alterada porem não foi salva. Ao mudar o ano as alterações feitas serão perdidas! Deseja continuar?","Atenção")
		
		If _lContinua
			_lChangZAL := .F.
		EndIf
	EndIf

Return _lContinua

/*/{Protheus.doc} U_GV007A29
Função para validar se o indice foi alterado.

<AUTHOR> Ribeiro
@since 29/08/2013
@version 11.5

@obs A variavel _lChangZAX é private e lógica, se o indice foi alterada ou nao.
/*/

User Function GV007A29()

	Local _cCampo := ""

	Local _nPosicao := 0

	_cCampo := AllTrim(StrTran(ReadVar(),"M->",""))

	_nPosicao := Ascan(oGetInd:aHeader,{|x| AllTrim(Upper(x[2])) == _cCampo })

	If _nPosicao > 0 .And. !_lChangZAX
		_lChangZAX := &(ReadVar()) <> oGetInd:aCols[n][_nPosicao]
	EndIf

Return .T.

/*/{Protheus.doc} U_GV007A30
Função para validar se a exceção foi alterado.

<AUTHOR> Ribeiro
@since 29/08/2013
@version 11.5

@obs A variavel _lChangZAW é private e lógica, se a exceção foi alterada ou nao.
/*/

User Function GV007A30()

	Local _cCampo := ""

	Local _nPosicao := 0

	_cCampo := AllTrim(StrTran(ReadVar(),"M->",""))

	_nPosicao := Ascan(oGetExc:aHeader,{|x| AllTrim(Upper(x[2])) == _cCampo })

	If _nPosicao > 0 .And. !_lChangZAW
		_lChangZAW := &(ReadVar()) <> oGetExc:aCols[n][_nPosicao]
	EndIf

Return .T.

/*/{Protheus.doc} GV007A31
Função exporta métricas.

<AUTHOR> Ribeiro
@since 29/08/2013
/*/

User Function GV007A31()

	Local _cQuery := ""
	Local _cCampo := ""
	
	Local _aCabeca := {}
	Local _aLinhas := {}
	Local _aCombo := {}
	Local _aOpcao := {}
	Local _aAreaSX3 := SX3->(GetArea())
	
	Local _nCount := 0
	
	SX3->(DbSetOrder(1))
	SX3->(DbSeek("ZAL"))
	
	If SX3->(Eof())
		_cCampo += "	ZAL.* " + CRLF
	Else
		While SX3->(!Eof()) .And. "ZAL" $ SX3->(FieldGet(FieldPos("X3_ARQUIVO")))
			If Empty(SX3->(FieldGet(FieldPos("X3_CONTEXT")))) .Or. "R" $ SX3->(FieldGet(FieldPos("X3_CONTEXT")))
				If Empty(_cCampo)
					_cCampo := "	 "
				Else
					_cCampo += "	,"
				EndIf
				
				If Empty(SX3->(FieldGet(FieldPos("X3_CBOX"))))
					_cCampo += "ZAL." + AllTrim(SX3->(FieldGet(FieldPos("X3_CAMPO")))) + " " + CRLF
				Else
					_aCombo := Separa(SX3->(FieldGet(FieldPos("X3_CBOX"))),";")
					
					_cCampo += "CASE " + CRLF
					
					For _nCount := 1 To Len(_aCombo)
						If !Empty(_aCombo[_nCount])
							_aOpcao := Separa(_aCombo[_nCount],"=")
							
							_cCampo += "		WHEN ZAL." + AllTrim(SX3->(FieldGet(FieldPos("X3_CAMPO")))) + " = '" + _aOpcao[1] + "' THEN '" + _aOpcao[2] + "' " + CRLF
						EndIf
					Next
					_cCampo += "		ELSE " + AllTrim(SX3->(FieldGet(FieldPos("X3_CAMPO")))) + CRLF
					_cCampo += "	 END " + AllTrim(SX3->(FieldGet(FieldPos("X3_CAMPO")))) + CRLF
				EndIf
				
				Aadd(_aCabeca,AllTrim(Upper(SX3->(FieldGet(FieldPos("X3_TITULO"))))))
			EndIf
			SX3->(DbSkip())
		EndDo
	EndIf

	_cQuery := " SELECT " + CRLF
	_cQuery += _cCampo
	_cQuery += " FROM " + RetSQLName("ZAL") + " ZAL " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE ZAL.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CONTRA = '" + _Contrato + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_REVISA = '" + _Versao + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _Cliente + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_LOJA = '" + _Loja + "' " + CRLF
	_cQuery += CRLF
	_cQuery += " ORDER BY " + CRLF
	_cQuery += "	 ZAL.ZAL_FILIAL " + CRLF
	_cQuery += "	,ZAL.ZAL_CONTRA " + CRLF
	_cQuery += "	,ZAL.ZAL_REVISA " + CRLF
	_cQuery += "	,ZAL.ZAL_ANOREF " + CRLF
	_cQuery += "	,ZAL.ZAL_CGCAGR " + CRLF
	
	_aLinhas := U_GV001X23(_cQuery,,,.T.)
	
	Processa({|| DlgToExcel({{"ARRAY","Metricas",_aCabeca,_aLinhas}}) },"TGCVA007","Aguarde... Exportando métricas...",.T.)
	
	RestArea(_aAreaSX3)
	
Return


//------------------------------------------------------
/*/{Protheus.doc} MySetField
Tratamento para o campo de ECF, ao alterar o campo,
atualiza a Legenda do Grid

<AUTHOR> Ferreira
@since 07/05/2016
@version 1.0
/*/
//------------------------------------------------------
Static Function MySetField(x,y,oGrid)

	Local aCor		:= {}
	Local lRet 		:= .T.
	Local nPosMECF	:= aScan(oGrid:aHeader,{|x| AllTrim(x[2]) == "ZAL_METECF"})
	Local nPosLeg	:= aScan(oGrid:aHeader,{|x| AllTrim(x[2]) == "LEGENDAECF"})
	
	oGrid:EDITCELL()
	
	If nPosMECF > 0 .And. nPosLeg > 0
		
		If oGrid:OBROWSE:COLPOS == nPosMECF
		
		    If oGrid:aCols[oGrid:nAt][nPosMECF] == "1"
		    	
		    	oGrid:aCols[oGrid:nAt][nPosLeg] := 'BR_VERDE'
		    	
		    ElseIf oGrid:aCols[oGrid:nAt][nPosMECF] == "2"
		    
		    	oGrid:aCols[oGrid:nAt][nPosLeg] := 'BR_VERMELHO'
		    	
		    ElseIf Empty(oGrid:aCols[oGrid:nAt][nPosMECF] )
		    
		    	oGrid:aCols[oGrid:nAt][nPosLeg] := 'BR_BRANCO'
		    
			EndIf
				
	    	oGrid:Refresh()
			
		ElseIf oGrid:OBROWSE:COLPOS == nPosLeg

			aAdd(aCor,{"BR_VERDE"	,"Concorda"   		})
			aAdd(aCor,{"BR_VERMELHO","Não Concorda"		})
			aAdd(aCor,{"BR_BRANCO"	,"Não Informado"	})
		
			BrwLegenda("Metrica ECF","Metrica ECF",aCor)		
				
		EndIf
	
	Endif
		
Return lRet

Static Function GC007INAT(oGMet, oGInd)

Local nli := 1
Local nPsIndS := aScan(aHeadInd,{|x| Alltrim( x[2] ) == "ZAX_STATUS" } )
Local nPsMetS := aScan(aHeadMet,{|x| Alltrim( x[2] ) == "ZAL_STATUS"} )	
	
//If MSGYESNO("Deseja inativar contrato corporativo!","ATENCAO!")
If AVISO( 'Inativando Corporativo', 'Deseja inativar todo o contrato corporativo?',{'Não','Sim'}, 2 ) == 2
	If AVISO( 'ATENCAO! Inativando Corporativo', 'Inativação não pode ser desfeita. CONFIRMA?',{'Não','Sim'}, 2 ) == 2
	//If MSGYESNO("Inativação não tem como ser desfeito. CONFIRMA?","ATENCAO!")
		
		For nli := 1 to Len(oGInd:aCols)
			oGInd:aCols[nli,nPsIndS]	 := "I"	
		Next 
		
		For nli := 1 to Len(oGMet:aCols)
			oGMet:aCols[nli,nPsMetS]		:= "I"		
		Next 
	
		oGMet:Refresh()
		oGInd:Refresh()
		DbSelectArea("ZAL")
		DbSetOrder(10) //ZAL_FILIAL+ZAL_STATUS+ZAL_CONTRA+ZAL_REVISA+ZAL_CGCPRI+ZAL_ANOREF
	
	
		Do While ZAL->(DbSeek(xFilial('ZAL') + "A" + _Contrato)) .oR. ZAL->(DbSeek(xFilial('ZAL') + "C" + _Contrato))
			If RecLock("ZAL",.F.)
				ZAL->ZAL_STATUS := "I"
				ZAL->(MsUnLock())
			EndIf
			ZAL->(DbSkip())
		End Do 
		
		DbSelectArea("ZAX")
		DbSetOrder(3)	//ZAX_FILIAL+ZAX_CONTRA+ZAX_REVISA+ZAX_PRCDU+ZAX_ANOREF+ZAX_ITEM
	
		If DbSeek(xFilial('ZAX') +  _Contrato)
			Do While !ZAX->(EOF()) .And. ZAX->ZAX_CONTRA == _Contrato 
				If ZAX->ZAX_STATUS == "A" .oR. ZAX->ZAX_STATUS == "C"
					If RecLock("ZAX",.F.)
						ZAX->ZAX_STATUS := "I"
						ZAX->(MsUnLock())
					EndIf
				EndIf
				ZAX->(DbSkip())
			End Do 
		EndIf	
	EndIf	
EndIf
Return
