#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TGCVA001.CH"
#INCLUDE "GCTXDEF.ch"
#INCLUDE "TGVX001DEF.CH"
#INCLUDE "TbiConn.ch"
#INCLUDE "TOPCONN.CH"

// Defines para arrays de filtro
#DEFINE _FLTCODCLI	1
#DEFINE _FLTLOJCLI	2
#DEFINE _FLTTPCLI	3

Static _nRecnoCN9 	:=	0
Static aLicMistD	:= {}
//-------------------------------------------------------------------
/*{Protheus.doc} TGCVA001
Cockpit de Contratos

<AUTHOR>
@since 25/06/2015
@version P12
*/
//-------------------------------------------------------------------
User Function TGCVA001( cCliente )

	Local aCoors   := FWGetDialogSize(oMainWnd) //MsAdvSize(.F.)
	Local nLinhaS  := 0
	Local nColunaS := 0
	Local nLinhaG  := 0
	Local nColunaG := 0
	Local aArea	   := GetArea()

	Local oFWLayer

	Local oPanFilter
	Local oPanButton
	Local oPanelValidity
	Local oPanelProposals
	Local nTamGrids 	:= 20// GetMV("TI_GV2ALTG")
	Local aStrut		:= {}
	Local lFilVig		:= .F.

	Local aCampos		:= {}
	Local aCposPH7		:= {}
	Local aIndTmp		:= {}

	Local nX			:= 0
	Local oItens
	Local oTmpAli		:= Nil
	Local oTmpPH7		:= Nil

	// Bloco de codigo para funcao de validacao dos filtros
	Local bValidaFil := { | _nItem | ValidaFil(aGetFilt,_nItem,aObjFilt,aFilAnt) }

	Local aObjFilt	:= Array(3)
	Local aFilAnt	:= Array(2)

	Local nPrWButton	:= 0
	Local nPrWFilter	:= 0
	Local nPrHButton	:= 0
	Local nPrHBrowse	:= 0

	Local aTpCli		:= Separa( GetSx3Cache("CNC_TIPCLI","X3_CBOX") , ";" )
	Private cTpCli		:= "XX"

	Private cAliasTrb		:= GetNextAlias()
	Private cAliasPH7		:= GetNextAlias()

	Private oBrowseProposals
	Private oBrowseValidity

	Private aVldUsr 		:= u_Cn3VldUsr()	//Utilizada pela Rotina de Manutencao de Contratos
	Private cCadastro		:= STR0001			//"Cockpit de Contratos"
	Private oDlgPrinc

	Private aGetFilt	:= {Space(TamSx3("A1_COD")[1]),;
		Space(TamSx3("A1_LOJA")[1]) }

	Private cDescrCli  := SPACE(TamSX3("A1_NOME")[1])
	Private oNomCliente
	Private oVinculo
	Private lVinculo   	:= .F.
	Private cCptSel		:= U_TGetCmpAtu(1)

	IF IsInCallStack( 'U_GctXRun' )
		PRIVATE aRotina		:= {}
	ENDIF

	Default cCliente	:= ''

	AADD(aTpCli,"XX=Todos")

	//-- Atalho para config. dos parametros
	//-- mv_par01 - Mostra Lancamentos: S/N
	//-- mv_par02 - Aglut Lancamentos:  S/N
	//-- mv_par03 - Lancamentos Online: S/N

	SetKey(VK_F6  , {|| U_GC202PH4()     }  )
	SetKey(VK_F7  , {|| U_TILogCtr(,, .t.)}  )
	SetKey(VK_F12 , {|| Pergunte("CNT100", .T.)})

	Pergunte("CNT100", .F.)

	lFilVig	:= !( Empty( cCliente ) )
	aStrut	:= GV01MtStrt()

	DEFINE FONT oFontArial NAME "Arial" BOLD SIZE 15,0
	oFontCal := TFont():New("Calibri",Nil,16,Nil,.T.,,,,.F.,.F.)

	//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
	//³ESTRUTURA DO ARQUIVO DE TRABALHO CN9
	//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	AAdd(aCampos,{"CN9_SITUAC"   	, "C" , TamSx3("CN9_SITUAC")[1], TamSx3("CN9_SITUAC")[2],RetTitle("CN9_SITUAC"),PesqPict("CN9","CN9_SITUAC")})
	AAdd(aCampos,{"CN9_RECNO"    	, "N" , 12, 0,"Recno",'999999999999'})
	AAdd(aCampos,{"CN9_APROV"		, "C" , TamSX3('CN9_APROV')[1],TamSX3('CN9_APROV')[2],RetTitle("CN9_APROV"),PesqPict("CN9","CN9_APROV")})
	AAdd(aCampos,{"CN9_NUMERO"    	, "C" , TamSx3("CN9_NUMERO")[1], TamSx3("CN9_NUMERO")[2],RetTitle("CN9_NUMERO"),PesqPict("CN9","CN9_NUMERO")})
	AAdd(aCampos,{"CN9_REVISA"   	, "C" , TamSx3("CN9_REVISA")[1], TamSx3("CN9_REVISA")[2],RetTitle('CN9_REVISA'),PesqPict("CN9","CN9_REVISA")})
	AAdd(aCampos,{"CN9_DTINIC"   	, "D" , TamSx3("CN9_DTINIC")[1], TamSx3("CN9_DTINIC")[2],RetTitle("CN9_DTINIC"),PesqPict("CN9","CN9_DTINIC")})
	AAdd(aCampos,{"CN9_DTASSI"   	, "D" , TamSx3("CN9_DTASSI")[1], TamSx3("CN9_DTASSI")[2],RetTitle("CN9_DTASSI"),PesqPict("CN9","CN9_DTASSI")})
	AAdd(aCampos,{"CN9_UNVIGE"   	, "C" , 20, 0,RetTitle("CN9_UNVIGE"),"@!" })
	AAdd(aCampos,{"CN9_VIGE"   		, "N" , TamSx3("CN9_VIGE")[1], TamSx3("CN9_VIGE")[2],RetTitle('CN9_VIGE'),PesqPict("CN9","CN9_VIGE")})
	AAdd(aCampos,{"CN9_DTFIM"   	, "D" , TamSx3("CN9_DTFIM")[1], TamSx3("CN9_DTFIM")[2],RetTitle("CN9_DTFIM"),PesqPict("CN9","CN9_DTFIM")})
	AAdd(aCampos,{"CN9_MOEDA"    	, "N" , TamSx3("CN9_MOEDA")[1], TamSx3("CN9_MOEDA")[2],RetTitle('CN9_MOEDA'),PesqPict("CN9","CN9_MOEDA")})
	AAdd(aCampos,{"CN9_CONDPG"    	, "C" , TamSx3("CN9_CONDPG")[1], TamSx3("CN9_CONDPG")[2],RetTitle('CN9_CONDPG'),PesqPict("CN9","CN9_CONDPG")})
	AAdd(aCampos,{"CN9_DESCPG"    	, "C" , TamSx3("CN9_DESCPG")[1], TamSx3("CN9_DESCPG")[2],RetTitle('CN9_DESCPG'),PesqPict("CN9","CN9_DESCPG")})
	AAdd(aCampos,{"CNC_TIPCLI"   	, "C" , 20, 0,RetTitle("CNC_TIPCLI"),"@!" })
	aIndTmp := {}
	AADD(aIndTmp,{"CNC_TIPCLI","CN9_NUMERO"})

	oTmpAli := creatTmpTab(aCampos, cAliasTRB, aIndTmp) // creatTmpTab((aFields, cAlias, aIndice)



	//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
	//³ESTRUTURA DO ARQUIVO DE TRABALHO PH7
	//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	AAdd(aCposPH7,{"PH7_RECNO"    	, "N" , 12, 0,"Recno",'999999999999'})
	AAdd(aCposPH7,{"PH7_STATUS"		, "C" , TamSx3("PH7_STATUS")[1], TamSx3("PH7_STATUS")[2],RetTitle("PH7_STATUS"),PesqPict("PH7","PH7_STATUS")})
	AAdd(aCposPH7,{"PH7_NUMERO"   	, "C" , TamSx3("PH7_NUMERO")[1], TamSx3("PH7_NUMERO")[2],"Planilha",PesqPict("PH7","PH7_NUMERO")})
	AAdd(aCposPH7,{"CNL_CODIGO"		, "C" , TamSX3('CNL_CODIGO')[1],TamSX3('CNL_CODIGO')[2],"Tipo",PesqPict("CNL","CNL_CODIGO")})
	AAdd(aCposPH7,{"CNL_DESCRI"    	, "C" , TamSx3("CNL_DESCRI")[1], TamSx3("CNL_DESCRI")[2],"Descrição",PesqPict("CNL","CNL_DESCRI")})
	AAdd(aCposPH7,{"PH7_COMPET"   	, "C" , TamSx3("PH7_COMPET")[1], TamSx3("PH7_COMPET")[2],"Competência",PesqPict("PH7","PH7_COMPET")})
	AAdd(aCposPH7,{"PH7_MOEDA"   	, "C" , TamSx3("PH7_MOEDA")[1], TamSx3("PH7_MOEDA")[2],"Moeda",PesqPict("PH7","PH7_MOEDA")})
	AAdd(aCposPH7,{"PH7_VLRFAT"   	, "N" , TamSx3("PH7_VLRFAT")[1], TamSx3("PH7_VLRFAT")[2],RetTitle("PH7_VLRFAT"),PesqPict("PH7","PH7_VLRFAT")})

	aIndTmp := {}
	AADD(aIndTmp,{"PH7_NUMERO"})

	oTmpPH7 := creatTmpTab(aCposPH7, cAliasPH7, aIndTmp) // creatTmpTab((aFields, cAlias, aIndice)


	//DEFINE MSDIALOG oDlgPrinc Title STR0001 From aCoors[1], aCoors[2] To aCoors[3], aCoors[4] PIXEL		//"Cockpit de Contratos"
	oDlgPrinc := TDialog():New(aCoors[1], aCoors[2],aCoors[3], aCoors[4],STR0001,,,,nOr(WS_VISIBLE,WS_POPUP),CLR_BLACK,CLR_WHITE,,oMainWnd,.T.,,,,,,.F.)

	// Cria o container onde serao colocados os browses
	oFWLayer := FWLayer():New()
	oFWLayer:Init(oDlgPrinc, .F., .T.)

	nPrWButton := round((387/aCoors[4])*100,2)
	nPrWFilter := 100 - nPrWButton

	nPrHButton := round((137/(aCoors[3]-aCoors[2]))*100,2)
	nPrHBrowse := 100 - nPrHButton

	// Define Painel Superior
	oFWLayer:AddLine("UP", 20, .F.)
	//oFWLayer:AddCollumn("ALL" , 100, .T., "UP")
	oFWLayer:AddCollumn( 'ColFilter', nPrWFilter, .T.,'UP')
	oFWLayer:AddCollumn( 'ColButton', nPrWButton, .T.,'UP')
	oFWLayer:AddWindow('ColFilter','WinFilter','Filtros',100, .F., .F., , 'UP', Nil )
	oFWLayer:AddWindow('ColButton','WinButton','Ações'  ,100, .F., .F., , 'UP', Nil )

	oPanFilter := oFWLayer:GetWinPanel('ColFilter','WinFilter','UP')
	oPanButton := oFWLayer:GetWinPanel('ColButton','WinButton','UP')

	nLinhaS := 02
	nColunaS:= 05

	nLinhaG := 25
	nColunaG:= 05

	@ 10 , 150  SAY oNomCliente VAR cDescrCli PICTURE "@!" /*COLOR CLR_RED*/ FONT oFontArial of oPanFilter PIXEL


	If !Empty(cCliente)

		aGetFilt[_FLTCODCLI] := SA1->A1_COD
		aGetFilt[_FLTLOJCLI] := SA1->A1_LOJA

	Endif

	cCliente := Space(6)
	cLoja := Space(2)
	// @ nLinhaS,nColunaS GET oCli VAR cCliente SIZE 200,20 OF oPanFilter PIXEL
	aObjFilt[_FLTCODCLI] := TGet():New( C(nLinhaS),C(nColunaS), {|u| IIf(PCount() == 0, aGetFilt[_FLTCODCLI], aGetFilt[_FLTCODCLI] := u) }, oPanFilter, 40, 10, "@!", {||Eval(bValidaFil,_FLTCODCLI)}, Nil, Nil, Nil, .T., Nil, .T., Nil, Nil, {||Empty(cCliente)}, Nil, Nil, {||ClearAll(oBrowseProposals, oBrowseValidity)}, .F., .F., "SA1", "A1_COD", Nil, Nil, Nil, .T., Nil, Nil, "Cliente:",  1, Nil, Nil, "" )

	nColunaS+= 30
	nColunaG+= 40

	// @ nLinhaS,nColunaS GET oLoja VAR cLoja SIZE 200,20 OF oPanFilter PIXEL
	aObjFilt[_FLTLOJCLI] := TGet():New( C(nLinhaS),C(nColunaS), {|u| IIf(PCount() == 0, aGetFilt[_FLTLOJCLI], aGetFilt[_FLTLOJCLI] := u) }, oPanFilter, 25, 10, Nil, {||Eval(bValidaFil,_FLTLOJCLI)}, Nil, Nil, Nil, .T., Nil, .T., Nil, Nil, {||Empty(cCliente)}, Nil, Nil, {||ClearAll(oBrowseProposals, oBrowseValidity)}, .F., .F., Nil, "A1_LOJA", Nil, Nil, Nil, .T., Nil, Nil, "Loja:",  1, Nil, Nil, "" )

	nColunaS+= 20
	nColunaG+= 40

	aObjFilt[_FLTTPCLI] := TComboBox():New( C(nLinhaS),C(nColunaS)	,{|u| If(PCount()>0,Eval({|x| cTpCli :=x },u),cTpCli)},aTpCli,75,13,oPanFilter,,,,,,.T.,,,,,,,,,"cTpCli","Tipo Cliente",1)

	nColunaS+= 30
	nColunaG+= 30

	@ nLinhaS + 23,05 	CHECKBOX oVinculo VAR lVinculo PROMPT '' OF oPanFilter;
		FONT oFontCal WHEN .F. SIZE 20,007 PIXEL
	@ nLinhaS + 23, 015 SAY 'Faz parte de um vínculo no licenciamento' OF oPanFilter;
		FONT oFontCal SIZE 150,007 PIXEL

	@ 10,05 BUTTON "Seleciona Cliente"   SIZE 50, 20 OF oPanButton PIXEL ACTION (	Processa({ || Carga(cAliasTrb),;
		oBrowseValidity:GoTop(.T.),;
		UpdateFldr(oBrowseValidity,cAliasPH7),;
		oBrowseProposals:GoTop(.T.)},;
		"Aguarde","Carregando Contratos.."))

	// Cria Menu
	oMenu := TMenu():New(0,0,0,0,.T.)

	// Adiciona itens no Menu
	oTMItem1 := TMenuItem():New(oPanButton,"Visualizar Cliente"          ,,,, { || U_GV01CLFBT(1,1) },,,,,,,,,.T.)
	oTMItem2 := TMenuItem():New(oPanButton,"Posição do Cliente"          ,,,, { || U_GV01CLFBT(1,2) },,,,,,,,,.T.)
	oTMItem3 := TMenuItem():New(oPanButton,"Títulos Baixados"            ,,,, { || U_GV01CLFBT(1,3) },,,,,,,,,.T.)
	oTMItem4 := TMenuItem():New(oPanButton,"Liberação de Help Desk (ZQE)",,,, { || U_GV01CLFBT(1,4) },,,,,,,,,.T.)
	oTMItem5 := TMenuItem():New(oPanButton,"Site(s) de Faturamento"      ,,,, { || U_GV01CLFBT(1,5) },,,,,,,,,.T.)

	oMenu:Add(oTMItem1)
	oMenu:Add(oTMItem2)
	oMenu:Add(oTMItem3)
	oMenu:Add(oTMItem4)
	oMenu:Add(oTMItem5)
//		oMenu:Add(oTMItem6)


	// Cria botão que sera usado no Menu
	oTButton1 := TButton():New( 10, 60, "Cliente",oPanButton,{||.T.},;
		35,20,,,.F.,.T.,.F.,,.F.,,,.F. )

	// Define botão no Menu
	oTButton1:SetPopupMenu(oMenu)


	// Cria Menu - Relatórios
	oMenuRel := TMenu():New(0,0,0,0,.T.)

	// Adiciona itens no Menu
	oTMRel2 := TMenuItem():New(oPanButton,STR0078 ,,,, { || U_GV01CLFBT(2,14) },,,,,,,,,.T.)  //"Relatório Bonif/Caren"
	oTMRel3 := TMenuItem():New(oPanButton,STR0079 ,,,, { || U_GV01CLFBT(2,15) },,,,,,,,,.T.)  //"Relatorio de Versionamento"
	oTMRel4 := TMenuItem():New(oPanButton,STR0080 ,,,, { || U_GV01CLFBT(2,22) },,,,,,,,,.T.)//"Relatório Revisões Pendentes"
	oTMRel5 := TMenuItem():New(oPanButton,"Relatório Cronograma Financeiro"     	,,,, { || U_GV01CLFBT(2,17) },,,,,,,,,.T.)//"Relatório Cronograma Financeiro"
	oTMRel6 := TMenuItem():New(oPanButton,"Relatório Analítico Contratos"      		,,,, { || U_GV01CLFBT(2,18) },,,,,,,,,.T.)//"Relatório Analítico Contratos"
	oTMRelA := TMenuItem():New(oPanButton,"Relatório Apuração Royalties Misterchef" ,,,, { || U_GV01CLFBT(2,39) },,,,,,,,,.T.)//"Relatório Apuração Royalties Misterchef"
	oTMRelB := TMenuItem():New(oPanButton,"Relatório Troca" 						,,,, { || U_GV01CLFBT(2,45) },,,,,,,,,.T.)//"Relatório Troca de Modalidade"
	oTMRlBC := TMenuItem():New(oPanButton,"Relatório de Cancelamentos" 				,,,, { || U_GV01CLFBT(2,46) },,,,,,,,,.T.)//"Relatório de Cancelamentos"
	oTMRlBV := TMenuItem():New(oPanButton,"Relatório de Vínculo no licenciamento" 	,,,, { || U_TGCV15R(.T.) },,,,,,,,,.T.)//"Relatório de Vinculo no Licenciamento"

	oTMRel7 := TMenuItem():New(oPanButton,"Jobs do Contrato"     					,,,, { || U_GV01CLFBT(2,12) },,,,,,,,,.T.)//"Jobs do Contrato"
	oTMRel8 := TMenuItem():New(oPanButton,"Proces. Lotes"      						,,,, { || U_GV01CLFBT(2,13) },,,,,,,,,.T.)//"Proces. Lotes"
	oTMRel9 := TMenuItem():New(oPanButton,"Log Lotes"        						,,,, { || U_CFGX001A('U_TGCVJ0Log')  },,,,,,,,,.T.)//"log. Lotes"	  //TIADMVIN-3138
	oTMRel10 := TMenuItem():New(oPanButton,"Complemento Cliente"     				,,,, { || U_GV01CLFBT(2,25) },,,,,,,,,.T.)//" Complemento do Clientes"
	oTMRlBT  := TMenuItem():New(oPanButton,"Relatório de Oscilação Intera" 			,,,, { || U_GV01CLFBT(2,48) },,,,,,,,,.T.)//"Relatório de Cancelamentos"
	oTMRlBT2 := TMenuItem():New(oPanButton,"Relatório Rateio Vigente" 			    ,,,, { || U_GV01CLFBT(2,50) },,,,,,,,,.T.)//"Relatório de Rateio Vigente"
	oTMRlBT3 := TMenuItem():New(oPanButton,"Relatório Fotografia Intera"		    ,,,, { || U_GV01CLFBT(2,51) },,,,,,,,,.T.)//"Relatório Fotografia Intera"

	oMenuRel:Add(oTMRel2)
	oMenuRel:Add(oTMRel3)
	oMenuRel:Add(oTMRel4)
	oMenuRel:Add(oTMRel5)
	oMenuRel:Add(oTMRel6)
	oMenuRel:Add(oTMRelA)
	oMenuRel:Add(oTMRelB)
	oMenuRel:Add(oTMRlBC)
	oMenuRel:Add(oTMRlBV)
	oMenuRel:Add(oTMRel7)
	oMenuRel:Add(oTMRel8)
	oMenuRel:Add(oTMRel9)
	oMenuRel:Add(oTMRel10)
	oMenuRel:Add(oTMRlBT)
	oMenuRel:Add(oTMRlBT2)
	oMenuRel:Add(oTMRlBT3)

	// Cria botão que sera usado no Menu
	oTButton2 := TButton():New( 10, 100, "Outras Ações",oPanButton,{||.T.},;
		42,20,,,.F.,.T.,.F.,,.F.,,,.F. )

	// Define botão no Menu
	oTButton2:SetPopupMenu(oMenuRel)



	@ 10,147 BUTTON "Sair" SIZE 35, 20 OF oPanButton PIXEL ACTION ( oDlgPrinc:End()  ) //""Sair""

	// Define o Painel Inferior
	oFWLayer:AddLine("CENTER", 42, .F.)
	oFWLayer:AddCollumn("ALL" , 100, .T., "CENTER")
	oPanelValidity := oFWLayer:GetColPanel("ALL", "CENTER")

	oPnlVldBrw := TPanel():New(00,00,"",oPanelValidity,Nil,.F.,,Nil,Nil,00,00)
	oPnlVldLeg := TPanel():New(00,00,"",oPanelValidity,Nil,.F.,,Nil,Nil,00,15)
	oPnlVldLeg:align:= CONTROL_ALIGN_BOTTOM
	oPnlVldBrw:align:= CONTROL_ALIGN_ALLCLIENT



	// FWmBrowse Vigência
	oBrowseValidity	:= FWMBrowse():New()
	oBrowseValidity:SetOwner(oPnlVldBrw)
	oBrowseValidity:SetDescription(STR0008)		//"Vigência"
	oBrowseValidity:SetMenuDef("TGCVA001A")
	oBrowseValidity:SETMAINPROC("TGCVA001A")
	oBrowseValidity:DisableDetails()
	oBrowseValidity:SetAlias(cAliasTrb)
	//oBrowseValidity:Alias("CN9")
	//oBrowseValidity:SetOnlyFields(aStrut[3])
	oBrowseValidity:SetProfileID("3")
	//oBrowseValidity:ForceQuitButton()
	oBrowseValidity:DisableReport()
	//oBrowseValidity:SetFilterDefault(U_TGC001FT(SA1->A1_COD, SA1->A1_LOJA))
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '01'"										, "RED"	, STR0009)			// "Cancelado"
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '02'"										, "YELLOW"	, STR0010) 		// "Em Elaboracao"
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '03'"										, "BLUE"	, STR0011) 		// "Emitido"
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '04'"										, "ORANGE"	, STR0012) 		// "Em Aprovacao"
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '05'"										, "GREEN"	, STR0013) 		// "Vigente"
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '06'"										, "GRAY"	, STR0014) 		// "Paralisado"
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '07'"										, "BROWN"	, STR0015) 		// "Sol. Finalizacao"
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '08'"										, "BLACK"	, STR0016) 		// "Finalizado"
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '09' .And. Empty((cAliasTrb)->CN9_APROV) "		, "PINK"	, STR0017) 		// "Revisao"
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '09' .And. !Empty((cAliasTrb)->CN9_APROV)"		, "VIOLET"	, STR0018) 		// "Revisão por alçadas
	oBrowseValidity:AddLegend("Alltrim((cAliasTrb)->CN9_SITUAC) == '10'"										, "WHITE"	, STR0019) 		// "Revisado"

	//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
	//³Adiciona Colunas ao Browse Central 				³
	//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
	For nx := 4 to Len(aCampos)//Pula situac para legenda, aprovador e recno
		cTemp := "{ || " + aCampos[nx][1] + " }"

		ADD COLUMN oItens DATA &(cTemp) TITLE aCampos[nx][5] SIZE  aCampos[nx][3] PICTURE aCampos[nx][6] ALIGN 0 OF oBrowseValidity

	Next nx

	oBrowseValidity:SetChange({||UpdateFldr(oBrowseValidity, cAliasPH7),oBrowseProposals:GoTop(.T.)})

	// FWmBrowse Cronograma Represado
	// Define Painel Central
	oFWLayer:AddLine("DOWN", 38, .F.)
	oFWLayer:AddCollumn("ALL" , 100, .T., "DOWN")
	oPanelProposals := oFWLayer:GetColPanel("ALL", "DOWN")


	oPnlPrpBrw := TPanel():New(00,00,"",oPanelProposals,Nil,.F.,,Nil,Nil,00,00)
	oPnlPrpLeg := TPanel():New(00,00,"",oPanelProposals,Nil,.F.,,Nil,Nil,00,15)//30)
	oPnlPrpLeg:align:= CONTROL_ALIGN_BOTTOM
	oPnlPrpBrw:align:= CONTROL_ALIGN_ALLCLIENT


	//oBrowseProposals:= FWMBrowse():New()
	oBrowseProposals := FWBrowse():New()
	oBrowseProposals:SetOwner(oPnlPrpBrw)
	oBrowseProposals:SetDescription("Cronograma Represado")
	oBrowseProposals:SetDataTable(.T.)
	oBrowseProposals:SetAlias(cAliasPH7)

	oBrowseProposals:AddLegend(".T.", "BR_VERMELHO"	, "Pendente")

	For nx := 3 to Len(aCposPH7)//Pula situac para legenda, aprovador e recno
		cTemp := "{ || " + aCposPH7[nx][1] + " }"

		ADD COLUMN oItens DATA &(cTemp) TITLE aCposPH7[nx][5] SIZE  aCposPH7[nx][3] PICTURE aCposPH7[nx][6] ALIGN 0 OF oBrowseProposals

	Next nx


	oBmpVig := TBitmap():New ( 005, 005, 010, 010, "BR_VERDE.PNG", Nil, .T., oPnlVldLeg, Nil, Nil, .F., .F., Nil, Nil, Nil, Nil, .T., Nil, Nil, Nil, Nil )
	oSayVig := TSay():New( 005, 015, { || "Vigente" }, oPnlVldLeg, Nil, Nil, Nil, Nil, Nil, .T., Nil, Nil, 030, 010, Nil, Nil, Nil, Nil, Nil, .T./*[lHTML]*/ )
	oBmpRev := TBitmap():New ( 005, 060, 010, 010, "BR_PINK.PNG", Nil, .T., oPnlVldLeg, Nil, Nil, .F., .F., Nil, Nil, Nil, Nil, .T., Nil, Nil, Nil, Nil )
	oSayRev := TSay():New( 005, 070, { || "Revisão" }, oPnlVldLeg, Nil, Nil, Nil, Nil, Nil, .T., Nil, Nil, 020, 010, Nil, Nil, Nil, Nil, Nil, .T./*[lHTML]*/ )
	oBmpEla := TBitmap():New ( 005, 110, 010, 010, "BR_AMARELO.PNG", Nil, .T., oPnlVldLeg, Nil, Nil, .F., .F., Nil, Nil, Nil, Nil, .T., Nil, Nil, Nil, Nil )
	oSayEla := TSay():New( 005, 120, { || "Em Elaboração" }, oPnlVldLeg, Nil, Nil, Nil, Nil, Nil, .T., Nil, Nil, 040, 010, Nil, Nil, Nil, Nil, Nil, .T./*[lHTML]*/ )


	oBmpRpP := TBitmap():New ( 005, 005, 010, 010, "BR_VERMELHO.PNG", Nil, .T., oPnlPrpLeg, Nil, Nil, .F., .F., Nil, Nil, Nil, Nil, .T., Nil, Nil, Nil, Nil )
	oSayRpP := TSay():New( 005, 015, { || "Pendente" }, oPnlPrpLeg, Nil, Nil, Nil, Nil, Nil, .T., Nil, Nil, 090, 010, Nil, Nil, Nil, Nil, Nil, .T. )



	oBrowseProposals:Activate()
	oBrowseValidity:Activate()

	oBrowseValidity:oBrowse:SetRowHeight(nTamGrids)
	oBrowseProposals:oBrowse:SetRowHeight(nTamGrids)

	If !Empty(cCliente)

		Processa({ || 	Carga(cAliasTrb),;
			oBrowseValidity:GoTop(.T.),;
			UpdateFldr(oBrowseValidity,cAliasPH7),;
			oBrowseProposals:GoTop(.T.)},;
			"Aguarde","Carregando Contratos..")

	Endif


//	ACTIVATE MSDIALOG oDlgPrinc CENTER
	oDlgPrinc:Activate(Nil,Nil,Nil,.T.,{||.T.},Nil,{||.T.},Nil,Nil )

	SetKey(VK_F6 , NIL)
	SetKey(VK_F7 , NIL)
	SetKey(VK_F12, NIL)


	oBrowseProposals:Destroy()
	oBrowseValidity:Destroy()

	oBrowseProposals	:= NIL
	oBrowseValidity	:= NIL

	(cAliasTrb)->(DbCloseArea())
	(cAliasPH7)->(DbCloseArea())


	oTmpAli:Delete()
	oTmpPH7:Delete()

	FreeObj(oTmpAli)
	FreeObj(oTmpPH7)


	RestArea(aArea)

Return NIL

//-------------------------------------------------------------------
/*{Protheus.doc} MenuDef
Definicao das Opcoes de Menu

<AUTHOR>
@since 25/06/2015
@version P12
*/
//-------------------------------------------------------------------
Static Function MenuDef()
	Local aRotCli		:= {}
	Local aRotina		:= {}

	ADD OPTION aRotCli TITLE STR0005 ACTION "U_GV01CLFBT(1,1)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_VIS_CLIEN		//"Vis. Cliente"
	ADD OPTION aRotCli TITLE STR0006 ACTION "U_GV01CLFBT(1,2)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_POS_CLIEN		//"Pos. Cliente"
	ADD OPTION aRotCli TITLE STR0021 ACTION "U_GV01CLFBT(1,3)" OPERATION MODEL_OPERATION_UPDATE ACCESS 0 ID DEF_TIT_BAIXA	//"Tit. Baixados"
	ADD OPTION aRotCli TITLE STR0023 ACTION "U_GV01CLFBT(1,4)" OPERATION MODEL_OPERATION_UPDATE ACCESS 0 ID DEF_CHAMADOS	//"Chamados"
	ADD OPTION aRotCli TITLE STR0057 ACTION "U_GV01CLFBT(1,5)" OPERATION MODEL_OPERATION_VIEW ACCESS 0 						//"Site(s) Faturamento"
	ADD OPTION aRotCli TITLE STR0062 ACTION "U_GV01CLFBT(1,6)" OPERATION MODEL_OPERATION_VIEW ACCESS 0  					//"Processamentos Lote"

//	ADD OPTION aRotRel TITLE STR0077 ACTION "U_GV01CLFBT(2,16)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_REL_PROP 	//"Relatório Represados"
//	ADD OPTION aRotRel TITLE STR0078 ACTION "U_GV01CLFBT(2,14)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 					//"Relatório Bonif/Caren"
//	ADD OPTION aRotRel TITLE STR0079 ACTION "U_GV01CLFBT(2,15)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 ID DEF_REL_VERS		//"Relatorio de Versionamento"
//	ADD OPTION aRotRel TITLE STR0080 ACTION "U_GV01CLFBT(2,22)" 	OPERATION MODEL_OPERATION_VIEW ACCESS 0 					//"Relatório Revisões Pendentes"


	ADD OPTION aRotina TITLE STR0002 ACTION aRotCli OPERATION MODEL_OPERATION_UPDATE ACCESS 0	//"Clientes"

//	ADD OPTION aRotina TITLE STR0081 ACTION aRotRel OPERATION MODEL_OPERATION_UPDATE ACCESS 0	//"Relatórios"

Return aRotina

//----------------------------------------------------------------
Static Function ValidaFil(aGetFilt,nPosCpo,aObjFilt,aFilAnt)
	Local lRet      := .T.
	Local cCodCGC   := ""
	Local cAliasAI0 := ""

	If nPosCpo != 0

		If !Empty(aGetFilt[nPosCpo])

			Do Case

			Case nPosCpo == _FLTCODCLI

				SA1->(DbSetOrder(1))
				If !(lRet := SA1->(DbSeek(xFilial('SA1')+aGetFilt[_FLTCODCLI])))

					MsgStop('Cliente não cadastrado.')

				EndIf

			Case nPosCpo == _FLTLOJCLI

				SA1->(DbSetOrder(1))
				If !(lRet := SA1->(DbSeek(xFilial('SA1')+aGetFilt[_FLTCODCLI]+aGetFilt[_FLTLOJCLI])))

					MsgStop('Cliente não cadastrado.')

				EndIf

			End Case

		Endif

	Endif

	If lRet
		AI0->(dbOrderNickName("AI0_XVINCH"))
		lVinculo := .F.
		If AI0->(MsSeek(SA1->(A1_FILIAL + A1_COD)))
			lVinculo := .T.
		Else
			cAliasAI0 := GetNextAlias()
		/*pesquisa o CNPJ do FILHO para localizar CNPJ do PAI*/
			BeginSQL Alias cAliasAI0
			SELECT AI0_XVINCH FROM %table:AI0% AI0 
			 WHERE AI0.AI0_FILIAL = %xFILIAL:AI0%
			   AND AI0.AI0_CODCLI = %exp:SA1->A1_COD%
			   AND AI0.AI0_LOJA = %exp:SA1->A1_LOJA%
			   AND AI0.AI0_XVINCH <> ' '
			   AND AI0.%notdel%
			 Group By AI0_XVINCH
			EndsQL
			If (cAliasAI0)->(!Eof())
				lVinculo := .T.
			EndIf
			(cAliasAI0)->(dbCloseArea())
		EndIf
		AI0->(DbSetOrder(1))

		If (SA1->A1_EST <> 'EX' .or. SA1->A1_PAIS == '105')
			If SA1->A1_PESSOA == 'F'

				cCodCGC := Alltrim(Transform(SA1->A1_CGC,"@R 999.999.999-99"))

			Else

				cCodCGC := Alltrim(Transform(SA1->A1_CGC,"@R 99.999.999/9999-99"))

			EndIf
		Else
			cCodCGC := Alltrim(SA1->A1_CGC)
		EndIf

		cDescrCli := Alltrim(SA1->A1_NOME) + " - " + cCodCGC
		oNomCliente:Refresh()
		oVinculo:Refresh()

		If (nPosCpo != 0) .And. (aGetFilt[nPosCpo] != aFilAnt[nPosCpo])

			//Gatilha a loja no TGet
			If nPosCpo == _FLTCODCLI
				aObjFilt[_FLTLOJCLI]:cText := SA1->A1_LOJA
			Endif

			aEval(aObjFilt,{ | _oObj, _nInd | aObjFilt[_nInd]:Refresh() } )
			aEval(aGetFilt,{ | _cFiltro, _nInd | aFilAnt[_nInd] := _cFiltro } )

		Endif

	Endif

Return lRet


//----------------------------------------------------------------
Static Function Carga(cAliasTrb)

	Local aArea		:= GetArea()
	Local cQuery	:= ''
	Local cAliasQry	:= GetNextAlias()
	Local aParQry   := {}

	(cAliasTrb)->(__dbZap())

	cQuery += "	SELECT												 "
	cQuery += "	CN9_SITUAC,CN9_DTINIC,CN9_NUMERO,CN9_DTASSI			,"
	cQuery += " CN9_UNVIGE,	CN9_VIGE,CN9_DTFIM,CN9_MOEDA    		,"
	cQuery += " CN9_CONDPG,CN9_REVISA, CN9.R_E_C_N_O_ AS CN9_RECNO	,"
	cQuery += " coalesce(E4_DESCRI,' ') AS CN9_DESCPG, CN9_APROV	,"
	cQuery += " CNC_TIPCLI   "

	cQuery += "	FROM " + RetSqlName("CN9") + " CN9					"

	cQuery += "	INNER JOIN " + RetSqlName("CNC") + " CNC			"
	cQuery += "	ON CNC.CNC_FILIAL = ?								"
	AADD(aParQry, FWxFilial('CNC') )
	cQuery += "	AND CNC.CNC_NUMERO = CN9.CN9_NUMERO					"
	cQuery += "	AND CNC.CNC_REVISA = CN9.CN9_REVISA					"
	If cTpCli <> "XX"
		cQuery += "	AND CNC.CNC_TIPCLI = ?				"
		AADD(aParQry, cTpCli )
	EndIf
	cQuery += "	AND CNC.CNC_CLIENT = ?								"
	AADD(aParQry, SA1->A1_COD )
	cQuery += "	AND CNC.CNC_LOJACL = ?								"
	AADD(aParQry, SA1->A1_LOJA  )
	cQuery += "	AND CNC.D_E_L_E_T_ = ' '							"

	cQuery += " LEFT OUTER JOIN " + RetSqlName('SE4') + " SE4		"
	cQuery += " ON E4_FILIAL = '" + FWxFilial('SE4') + "'			"
	cQuery += " AND E4_CODIGO = CN9_CONDPG							"
	cQuery += " AND SE4.D_E_L_E_T_ = ' ' 							"

	cQuery += "	WHERE CN9.CN9_FILIAL = ? 							"
	AADD(aParQry, FwxFilial("CN9")   )
	cQuery += " AND CN9.CN9_SITUAC IN ( ?, ?, ?) 					"
	AADD(aParQry, '02'  )
	AADD(aParQry, '05'  )
	AADD(aParQry, '09'  )
	cQuery += " AND CN9.CN9_ESPCTR = ?		                      	"
	AADD(aParQry, '2'  )
	cQuery += " AND CN9_TPCTO = ?									"
	AADD(aParQry, '013'  )
	cQuery += "	AND CN9.D_E_L_E_T_ =' '								"

	cQuery += "	ORDER BY CNC_TIPCLI, CN9_NUMERO, CN9_REVISA DESC	"

	If Select(cAliasQry) > 0
		(cAliasQry)->(DbCloseArea())
	EndIf

	DBUseArea(.T., "TOPCONN", TCGenQry2(NIL,NIL,cQuery, aParQry), cAliasQry , .F., .T. )

	TcSetField(cAliasQry,'CN9_DTINIC','D',8,0)
	TcSetField(cAliasQry,'CN9_DTASSI','D',8,0)
	TcSetField(cAliasQry,'CN9_DTFIM','D',8,0)
	TcSetField(cAliasQry,'CN9_VIGE','N',TamSx3('CN9_VIGE')[1],TamSx3('CN9_VIGE')[2])
	TcSetField(cAliasQry,'CN9_MOEDA','N',TamSx3('CN9_MOEDA')[1],TamSx3('CN9_MOEDA')[2])
	TcSetField(cAliasQry,'CN9_RECNO','N',12,0)

	While !(cAliasQry)->(Eof())

		(cAliasTrb)->(RecLock(cAliasTrb,.T.))

		(cAliasTrb)->CN9_SITUAC := (cAliasQry)->CN9_SITUAC
		(cAliasTrb)->CN9_DTINIC := (cAliasQry)->CN9_DTINIC
		(cAliasTrb)->CN9_NUMERO := (cAliasQry)->CN9_NUMERO
		(cAliasTrb)->CN9_DTASSI := (cAliasQry)->CN9_DTASSI
		(cAliasTrb)->CN9_UNVIGE := RetX3Opc("CN9_UNVIGE",(cAliasQry)->CN9_UNVIGE)
		(cAliasTrb)->CN9_VIGE   := (cAliasQry)->CN9_VIGE
		(cAliasTrb)->CN9_DTFIM  := (cAliasQry)->CN9_DTFIM
		(cAliasTrb)->CN9_MOEDA  := (cAliasQry)->CN9_MOEDA
		(cAliasTrb)->CN9_CONDPG := (cAliasQry)->CN9_CONDPG
		(cAliasTrb)->CN9_REVISA := (cAliasQry)->CN9_REVISA
		(cAliasTrb)->CN9_RECNO	:= (cAliasQry)->CN9_RECNO
		(cAliasTrb)->CN9_APROV	:= (cAliasQry)->CN9_APROV
		(cAliasTrb)->CN9_DESCPG	:= (cAliasQry)->CN9_DESCPG
		(cAliasTrb)->CNC_TIPCLI := (cAliasQry)->CNC_TIPCLI + " - " + RetX3Opc("CNC_TIPCLI",(cAliasQry)->CNC_TIPCLI)

		(cAliasTrb)->(MsUnlock())

		(cAliasQry)->(DbSkip())

	Enddo

	(cAliasQry)->(DbCloseArea())

	RestArea(aArea)

Return Nil

//-----------------------------------------------------------------------------
Static Function UpdateFldr(oBrowseValidity,cAliasPH7)

	Local aArea		:= GetArea()
	Local cQuery	:= ''
	Local cAliasQry	:= GetNextAlias()
	Local cContra	:= oBrowseValidity:GetColumnData(2)
	Local cRevisa	:= oBrowseValidity:GetColumnData(3)
	Local cCompAtu := Substr( DtoS(MsDate()) ,1,6)
	Local aParQry   := {}

	(cAliasPH7)->(__dbZap())

	cQuery := " SELECT PH7_NUMERO,CNL_CODIGO,CNL_DESCRI, PH7_COMPET,PH7_MOEDA, PH7_VLRFAT, PH7_STATUS, "
	cQuery += " PH7.R_E_C_N_O_ as PH7_RECNO "

	cQuery += " FROM " + RetSqlName("PH7") + " PH7 "

	cQuery += " INNER JOIN " + RetSqlName("CNA") + " CNA	"
	cQuery += " ON CNA_FILIAL = ? "
	aadd(aParQry, FWxFilial("CNA"))
	cQuery += " AND CNA_CONTRA = PH7_CONTRA  "
	cQuery += " AND CNA_REVISA = PH7_REVISA  "
	cQuery += " AND CNA_NUMERO = PH7_NUMERO  "
	cQuery += "	AND CNA.D_E_L_E_T_ = ' '	 "

	cQuery += " INNER JOIN " + RetSqlName("CNL") + " CNL	"
	cQuery += " ON CNL.CNL_FILIAL = ? "
	aadd(aParQry, FWxFilial("CNL"))
	cQuery += " AND CNL_CODIGO = CNA_TIPPLA	"
	cQuery += "	AND CNL.D_E_L_E_T_ = ' '	"

	cQuery += " WHERE PH7.PH7_FILIAL = ? "
	aadd(aParQry, FWxFilial("PH7"))
	cQuery += " AND PH7_CONTRA = ? "
	aadd(aParQry, cContra)
	cQuery += " AND PH7_REVISA = ? "
	aadd(aParQry, cRevisa)

	cQuery += " AND PH7_STATUS NOT IN ( ?, ?) "
	aadd(aParQry, '10')
	aadd(aParQry, '12')

	cQuery += "	AND PH7_ANOMES < ? "
	aadd(aParQry, cCompAtu)
	cQuery += " AND PH7_VLRFAT > ? "
	aadd(aParQry, "0")
	cQuery += "	AND PH7.D_E_L_E_T_ = ' '	"

	cQuery += " ORDER BY  PH7_COMPET,CNL_DESCRI DESC "

	If Select(cAliasQry) > 0
		(cAliasQry)->(DbCloseArea())
	EndIf

	DBUseArea(.T., "TOPCONN", TCGenQry2(NIL,NIL,cQuery, aParQry), cAliasQry , .F., .T. )


	TcSetField(cAliasQry,'PH7_VLRFAT','N',TamSx3('PH7_VLRFAT')[1],TamSx3('PH7_VLRFAT')[2])
	TcSetField(cAliasQry,'PH7_RECNO','N',12,0)

	aReceitas := {}

	While !(cAliasQry)->(Eof())

		(cAliasPH7)->(RecLock(cAliasPH7,.T.))

		(cAliasPH7)->PH7_NUMERO := (cAliasQry)->PH7_NUMERO
		(cAliasPH7)->CNL_CODIGO := (cAliasQry)->CNL_CODIGO
		(cAliasPH7)->CNL_DESCRI := (cAliasQry)->CNL_DESCRI
		(cAliasPH7)->PH7_COMPET := (cAliasQry)->PH7_COMPET
		(cAliasPH7)->PH7_MOEDA  := (cAliasQry)->PH7_MOEDA
		(cAliasPH7)->PH7_VLRFAT := (cAliasQry)->PH7_VLRFAT
		(cAliasPH7)->PH7_RECNO	:= (cAliasQry)->PH7_RECNO
		(cAliasPH7)->PH7_STATUS	:= (cAliasQry)->PH7_STATUS

		(cAliasPH7)->(MsUnlock())

		(cAliasQry)->(DbSkip())

	EndDo

	(cAliasQry)->(DbCloseArea())

	RestArea(aArea)

Return NIL

//-------------------------------------------------------------------
/*{Protheus.doc} TGC001VC
Visualizacao do Cliente

<AUTHOR>
@since 29/06/2015
@version P12
*/
//-------------------------------------------------------------------
User Function TGC001VC()
	Local aArea			:= GetArea()
	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())
	Local aAreaCNC 		:= CNC->(GetArea())

	If Type("cCadastro") == "U" // Quando é chamada da rotina CNTA301 (P.E CNTA300_PE)
		cCadastro := STR0005 //"Vis. Cliente"
	EndIf

	A030Visual("SA1",SA1->(Recno()),2)

	RestArea(aAreaCNC)
	RestArea(aAreaCN9)
	RestArea(aAreaSA1)
	RestArea(aArea)
Return Nil
//-------------------------------------------------------------------
/*{Protheus.doc} TGC001PC
Posicao do Cliente

<AUTHOR>
@since 29/06/2015
@version P12
*/
//-------------------------------------------------------------------
User Function TGC001PC()
	Local aArea			:= GetArea()
	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())
	Local aAreaCNC 		:= CNC->(GetArea())

	Private cFilCorr	:= cFilAnt

	SaveInter() // Salva as variáveis PRIVATE

	If Type("cCadastro") == "U" // Quando é chamada da rotina CNTA301 (P.E CNTA300_PE)
		cCadastro := STR0006 //"Pos. Cliente"
	EndIf

	If Type("aRotina") == "U"
		aRotina	:= {}
		aRotina	:=	{{STR0044 ,"AxPesqui" , 0 , 1},; //"Pesquisar"
		{STR0045, "AxVisual" , 0 , 2}} //"Visualizar"
	EndIf

	pergunte("FIC010",.T.)
	Finc010(2) // Executa a rotina de posição de clientes, opção de consulta (FC010CON)

	Pergunte("CNT100", .F.)

	RestArea(aAreaCNC)
	RestArea(aAreaCN9)
	RestArea(aAreaSA1)
	RestArea(aArea)
	Restinter() // Restaura as variaveis PRIVATE

Return NIL
//-------------------------------------------------------------------
/*{Protheus.doc} TGC001TB
Chamada da Rotina de Consulta dos Titulos Baixados

<AUTHOR>
@since 02/07/2015
@version P12
*/
//-------------------------------------------------------------------
User Function TGC001TB()
	Local aArea			:= GetArea()
	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())
	Local aAreaCNC 		:= CNC->(GetArea())
	Local aAreaSE1		:= SE1->(GetArea())

	//Aplica o Filtro para o Cliente Selecionado
	cFilter := "E1_CLIENTE == '" + SA1->A1_COD + "' .AND. E1_LOJA == '" + SA1->A1_LOJA + "'"

	DbSelectArea('SE1')
	Set Filter to &cFilter

	//Chamada da Rotina de Titulos Baixados
	FINC040()

	//Limpa o Filtro Aplicado
	SE1->(DbClearFilter())

	Pergunte("CNT100", .F.)

	RestArea(aAreaSE1)
	RestArea(aAreaCNC)
	RestArea(aAreaCN9)
	RestArea(aAreaSA1)
	RestArea(aArea)
Return NIL

//-------------------------------------------------------------------
/*{Protheus.doc} TGC001CH
Chamada da Rotina de Chamados

<AUTHOR>
@since 02/07/2015
@version P12
*/
//-------------------------------------------------------------------
User Function TGC001CH()
	Local aArea			:= GetArea()
	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())

	//Chamada da Rotina de Chamados
	If MPUserHasAccess("TGCVA004", /*nOpc*/ , /*[cCodUser]*/, /*[lShowMsg]*/, /*[lAudit]*/  )
		U_TGCVA004()
	EndIf
	Pergunte("CNT100", .F.)

	RestArea(aAreaCN9)
	RestArea(aAreaSA1)
	RestArea(aArea)
Return NIL
//-------------------------------------------------------------------
/*{Protheus.doc} TGC001CV
Visualização de Contratos

<AUTHOR>
@since 03/07/2015
@version P12
*/
//-------------------------------------------------------------------
User Function TGC001CV(nRecCN9)
	Local aArea			:= GetArea()
	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())
	Local aAreaCNC 		:= CNC->(GetArea())
	Default nRecCN9		:= 0

	If U_TGCV201P()
		U_TGC201CV(nRecCN9)
		Return
	EndIf

	//
	If Type("nRecCN9") == "N"

		CN9->(DbGoTo(nRecCN9))

	Endif
	CursorWait()

	If CN9->(Eof())
		Aviso("TGC001CV", STR0027, {STR0026})		//"Selecione um Contrato para Visualização."		//"Fechar"
	Else

		U_A3STpRev("")
		U_GV002SOP(MODEL_OPERATION_VIEW)
		FWExecView(STR0004,"TGCVA301",MODEL_OPERATION_VIEW,,{|| .T.})		//"Vis. Contrato"

	EndIf

	RestArea(aAreaCNC)
	RestArea(aAreaCN9)
	RestArea(aAreaSA1)
	RestArea(aArea)

	U_GV002S0S("")

	CursorArrow()

Return Nil

//-------------------------------------------------------------------
/*{Protheus.doc} TGC001CA
Alteração de Contratos

<AUTHOR>
@since 03/07/2015
@version P12
*/
//-------------------------------------------------------------------
User Function TGC001CA(nRecCN9)

	Local aArea			:= GetArea()
	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())
	Local aAreaCNC 		:= CNC->(GetArea())
	Local cTipRev 		:= ""
	Local nRecSA1		:= 0
	Local nRetOpc		:= 0
	Local lRet			:= .T.
	Local cContrato 	:= ""
	Local cRevisa		:= ""
	Local cChaveLock	:= ""
	Local lAprvAut		:= .F.
	Local cRecNew		:= 0
	Local cContra		:= ""
	Local lVigent		:= .F.
	Local lExcMedAut	:= .F.
	Local cClienPri		:=  ""
	Local cLojaPri		:= ""
	Local lZqeOffLine   := .F.

	If U_TGCV201P()
		U_TGC201CA(nRecCN9)

		Return
	EndIf

	lAprvAut		:= GetMV("TI_AREVAUT") //Aprova revisão automatica
	lExcMedAut	:= GetMV("TI_EXCMEDA",,.F.) // GCV  - Exclui medição automaticamente antes de alterar o contrato
	lZqeOffLine   := Iif( cEmpAnt $ GetMV("TI_ZQEOFFL",,"ND"), .T., .F.)



	CursorWait()

	aLicMistD := {}

	CN9->(DbGoTo(nRecCN9))

	cChaveLock	:= "CN9"+xFilial("CN9")+CN9->CN9_NUMERO

	cRecNew 	:= nRecCN9
	nRecSA1		:= SA1->(Recno())

	//Limpa o Filtro Aplicado
	SA1->(dbGoTo(nRecSA1))

	If CN9->(Eof())

		Aviso("TGC001CA", STR0028, {STR0026})		//"Selecione um Contrato para Alteração."	//"Fechar"

	Else

		If U_GVFUNLOC(1,cChaveLock,"TGCVA001")

			U_GV002S0S("")
			U_GV002GH3S("")
			U_GCVA301GL({})
			U_GV002GTL( .F. )
			U_GV002GAC({})
			U_GV002GP4({})
			U_GV002GP3({})
			U_GV002SVC(.F.)// limpa variável para controle de execeução do cronograma financeiro
			U_A3SRevis(.F.)
			U_GV002SVA(.T.) // Marca que que a View está ativada
			u_CNTSetFun("TGCVA301")

			If AllTrim(CN9->CN9_SITUAC) == "05" .And. Empty(CN9->CN9_REVATU) // Se Revisão Atual, Chama a Opção de Copia ( Nova Revisão )

				If lExcMedAut // Verifica se existe medição em Aberto, e exclui.

					If !U_GV027Exc(CN9->CN9_NUMERO,CN9->CN9_REVISA, , .T.,, lExcMedAut )
						Help(" ",1, 'Help','TGC001NOMED', STR0063, 3, 0 ) //'Nao Foi Possivel Excluir as Mediçoes em Aberto para Esse Contrato, Verifique!'
						lRet := .F.
					EndIf

				EndIf

				If lRet

					CursorArrow()
					CursorWait()
					U_A3STpRev(DEF_REV_RENOV)

					// Tela de manutenção do contrato
					U_GV002SOP(OP_COPIA)

					cContrato 	:= CN9->CN9_NUMERO
					FWMsgRun(, {|| nRetOpc 	:= FWExecView(STR0008,"TGCVA301",OP_COPIA,,{|| .T.}, /*bOk*/, /*nPercReducao*/, /*aEnableButtons*/, {|| ValidSaida() } /*bCancel*/ )} ,"Carregando dados de contrato!!!", "Aguarde..." )

					//FWExecView (cTitulo, cPrograma, nOperation, oDlg, bCloseOnOk, /*bOk*/, /*nPercReducao*/, /*aEnableButtons*/, /*bCancel*/ )

					cRecNew := CN9->(Recno())

					If nRetOpc == 0 .And. lAprvAut

						CursorWait()

						CN9->(dbSetOrder(8))// Posiciona na Revisão Atual
						If CN9->(dbSeek(xFilial("CN9")+ cContrato + Padr('',TamSx3("CN9_REVATU")[1],' ')))

							// Aprova automaticamente a revisão.
							FWMsgRun(,{ || lVigent := U_GCTXAPR( cContrato, CN9->CN9_REVISA ) },STR0073, STR0059 ) //"Efetivando contrato como vigente"

							If lVigent
								cContra := CN9->CN9_NUMERO
								cRevisa := CN9->CN9_REVISA
							EndIf

						EndIf
						CursorArrow()

					ElseIf nRetOpc <> 0

						lRet := .F.

					EndIf

					CursorArrow()

				EndIf

			ElseIf AllTrim(CN9->CN9_SITUAC) == '09'

				cTipRev := U_Cn3RetSt('TIPREV')

				U_A3STpRev(cTipRev)

				If cTipRev == DEF_REV_REAJU
					Help('',1,'CNTA300NOA')
				ElseIf cTipRev == DEF_REV_REALI .And. A300RevMed()
					Help('',1,'CNTA300NOAREA')
				Else
					U_GV002SOP(MODEL_OPERATION_UPDATE)
					FWExecView(STR0008,"TGCVA301",MODEL_OPERATION_UPDATE,,{|| .T.})// Contrato Cliente(s)
				EndIf

			Else

				U_GV002SOP(MODEL_OPERATION_UPDATE)
				FWExecView(STR0008,"TGCVA301",MODEL_OPERATION_UPDATE,,{|| .T.})// Contrato Cliente(s)

			EndIf

			U_GVFUNLOC(2,cChaveLock,"TGCVA001")

		Else
			lRet := .F.
		EndIf

	EndIf

	U_A3SATpRv("")
	U_A3STpRev("")
	U_A3SRevis(.F.)
	U_GV002S0S("")
	U_GV002GH3S("")
	U_GCVA301GL({})
	U_GV002SOP(0)
	U_GV002GTL( .F. )
	U_GV002GAC({})
	U_GV002GP4({})
	U_GV002GP3({})
	U_GV002SVC(.F.)// limpa variável para controle de execeução do cronograma financeiro
	U_GV002SVA(.F.) // Marca que que a View está desativada

	If lRet
		SA1->(dbGoTo(nRecSA1))

		RestArea(aAreaCNC)
		RestArea(aAreaCN9)
		RestArea(aAreaSA1)
		RestArea(aArea)

		CN9->(dbGoTo(cRecNew))
		If lZqeOffLine
			If lVigent
				lExec := .T.
				U_JOBOFFZEQ(cContra, cRevisa)
			EndIf
		Else
			IIf( lVigent, FWMsgRun(,{ || lExec := U_TGCVA018( cContra, cRevisa ) },STR0065, STR0066 ), Nil ) //'01-Atualizando Liberacao de Help Desk'###'Aguarde'
		EndIf

		If U_GVCliPrin( cContra , cRevisa , @cClienPri , @cLojaPri )
			//Verificar Contratos de Setor Publico
			If FindFunction("U_GV050WF")
				FWMsgRun(,{ || U_GV050WF(cContra,cRevisa,cClienPri,cLojaPri)  },STR0070, STR0059 )// WF Setor Publico###"Aguarde..."
			EndIf

		EndIf

		// Verifica se Precisa desativar a licença Misterchef
		GVDstMist()
		aLicMistD := {}

		//Verifica se tem competencias pendentes de Gerar Medições
		U_GV01CmpMed(cContra)

	EndIf

	CursorArrow()

Return lRet

Static Function ValidSaida()
	Local oContato := U_GV002RCTR()

	If oContato <> NIL .and. ! MsgYesNo("As alterações serão perdidas, confirma a saida sem salvar?")
		Return .F.
	EndIf

Return .t.


//-------------------------------------------------------------------
/*/{Protheus.doc} TGC001CE
Opção de exclusão do contrato/revisão
<AUTHOR> Ferreira
@since 15/09/2015
@version 1.0
/*/
//-------------------------------------------------------------------
User Function TGC001CE(nRecCN9)

	Local aArea			:= GetArea()
	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())
	Local aAreaCNC 		:= CNC->(GetArea())
	Local cTipRev 		:= ""
	Local cFiltCN9		:= oBrowseValidity:GetFilterDefault()
	Local nRecSA1		:= 0
	Local cCliente		:= SA1->A1_COD
	Local cLoja			:= SA1->A1_LOJA
	Local cStatDef 	    := AllTrim(GetMV("TI_CN9FCP",,"FATA600|TGCVA301|TGCVA003|TJ27GCT"))

	CN9->(DbGoTo(nRecCN9))

	nRecSA1	:= SA1->(Recno())

	//Limpa o Filtro Aplicado
	SA1->(dbGoTo(nRecSA1))

	If CN9->(Eof())

		Aviso("TGC001CE", STR0037, {STR0026}) //"Selecione um Contrato para excluir."  //"Fechar"

	Else

		CursorWait()

		oBrowseValidity:SetChange({|| .T.})
		oBrowseValidity:CleanFilter()
		CN9->(dbClearfilter())
		CN9->(dbGoTo(nRecCN9))


		If Alltrim(CN9->CN9_XORIGE) $ cStatDef

			U_A3SATpRv("")
			U_GV002SOP(MODEL_OPERATION_DELETE)
			Pergunte("CNT100",.F.)
			FWExecView(STR0008,"TGCVA301",MODEL_OPERATION_DELETE,,{|| .T.})// Contrato Cliente(s)
		Else

			Aviso("TGC001CE2", STR0038, {STR0026})	 //"Opção somente para contratos incluídos pelo Cockpit."

		EndIf

		//oBrowseValidity:SetFilterDefault(U_TGC001FT(SA1->A1_COD, SA1->A1_LOJA))
		oBrowseValidity:GoTo(nRecCN9, .T.)

		CursorArrow()

	EndIf

	SA1->(dbGoTo(nRecSA1))

	RestArea(aAreaCNC)
	RestArea(aAreaCN9)
	RestArea(aAreaSA1)
	RestArea(aArea)

	U_A3SATpRv("")
	U_A3STpRev("")
	U_GV002S0S("")
	U_GV002GTL( .F. )
	U_GCVA301GL({})

Return Nil

//-------------------------------------------------------------------
/*{Protheus.doc} TGC001TR
Chamada da Rotina de Transferencia de Licenças

<AUTHOR>
@since 06/07/2015
@version P12
*/
//-------------------------------------------------------------------
User Function TGC001TR(nRecCN9)

	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())
	Local aAreaCNC 		:= CNC->(GetArea())
	Local aAreaCNA		:= CNA->(GetArea())
	Local cCliente		:= SA1->A1_COD
	Local cLojaCli		:= SA1->A1_LOJA
	Local cContrato		:= ""
	Local cRevisao		:= ""
	Local nRegSA1		:= SA1->(Recno())
	Local cTipRev		:= U_GCTXTREV()		//Tipo de Revisao do Contrato
	Local cMsgErro		:= ""
	Local lRetorno		:= .F.
	Local cSituTrf		:= GetMV('TI_TRF0001')
	Local aButtons	:= {{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.T.,STR0074},{.T.,STR0075},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil}} //"Confirmar"###"Cancelar"
	Local cTipo         := ''

	CN9->(DbGoTo(nRecCN9))

	cContrato	:= CN9->CN9_NUMERO
	cRevisao	:= CN9->CN9_REVISA

	//somente contratos que estejam dentro da situação informada no parametr (atualmente 30/11/2015: 05-Vigente)
	if CN9->CN9_SITUAC $ cSituTrf
		//Verifica se o cliente selecionado é o cliente principal do contrato selecionado
		dbSelectArea("CNC")
		CNC->(dbOrderNickName("GCV01"))//CNC_FILIAL+CNC_NUMERO+CNC_REVISA+CNC_TIPCLI+CNC_CLIENT+CNC_LOJACL
		if CNC->(dbSeek(xFilial('CNC')+cContrato+cRevisao+'01'+cCliente+cLojaCli))

			//Somente se existir uma revisão (CN0) cadastrada como tipo C
			If !Empty(cTipRev)

				CursorWait()

				If !CN9->(Eof())

					DbSelectArea("SA1")
					SA1->(DbSetOrder(1))//A1_FILIAL, A1_COD, A1_LOJA
					If SA1->(DbSeek(xFilial("SA1") + cCliente + cLojaCli))

						DbSelectArea("CN9")
						CN9->(DbSetOrder(1))		//CN9_FILIAL, CN9_NUMERO, CN9_REVISA
						If CN9->(DbSeek(xFilial("CN9") + cContrato + cRevisao))

							DbSelectArea("CNA")
							CNA->(DbSetOrder(1))		//CNA_FILIAL, CNA_CONTRA, CNA_REVISA
							If CNA->(DbSeek(FWxFilial("CNA") + CN9->CN9_NUMERO + CN9->CN9_REVISA))

								//Verifica se existem itens que podem ser transferidos
								if GC001VIT(CN9->CN9_NUMERO,CN9->CN9_REVISA)
									cTipo := U_GV3SelTan()  // Seleciona o tipo de transferencia
									If cTipo == "1"
										lRetorno := FWExecView(STR0029, "TGCVA003", MODEL_OPERATION_UPDATE, , Nil, Nil , ,aButtons ) == 0		//"Transf. Licenças"
									ElseIf cTipo == "2"
										lRetorno := FWExecView(STR0082, "TGCVA003", MODEL_OPERATION_UPDATE, , Nil, Nil , ,aButtons ) == 0		//"Transferência de Periodicidade"
									EndIf
								Else

									Aviso("TGC001TR_08", STR0046+CN9->CN9_NUMERO+CN9->CN9_REVISA+STR0049, {STR0026})//"Para este Contrato/Revisão ("##") não existem itens que possam ser transferidos."##Fechar

								EndIf

							Else

								Aviso("TGC001TR_07", STR0030, {STR0026})		//"Contrato sem Itens na Planilha."	//"Fechar"

							EndIf

						Else

							Aviso("TGC001TR_06", STR0040, {STR0026}) //"Contrato de Origem nao localizado."###"Fechar"

						EndIf

					Else

						Aviso("TGC001TR_05", STR0041, {STR0026}) //"Cliente não localizado."###"Fechar"

					EndIf

				Else

					Aviso("TGC001TR_04", STR0028, {STR0026})		//"Selecione um Contrato para Alteração."	//"Fechar"

				EndIf

			Else

				cMsgErro := STR0039 //"Não existe um Tipo de Revisão do Tipo Renovação cadastrado. Cadastre o Tipo de Revisão antes de Confirmar a Transferência de Licenças."
				Aviso("TGC001TR_03", cMsgErro, {STR0026})		//"Fechar"

			EndIf

		Else

			//"Para este Contrato/Revisão ("###") o cliente/loja ("###")não é o principal, portanto não poderá transferir qualquer licença."""###"Fechar"
			Aviso("TGC001TR_02",STR0046+Alltrim(cContrato)+'/'+Alltrim(cRevisao)+STR0047+Alltrim(cCliente)+'/'+cLojaCli+STR0048, {STR0026})

		EndIf

	Else
		Aviso("TGC001TR_01",STR0050, {STR0026})
	EndIf


	CursorArrow()

	RestArea(aAreaSA1)
	RestArea(aAreaCNC)
	RestArea(aAreaCNA)
	RestArea(aAreaCN9)

Return Nil
//-------------------------------------------------------------------
/*{Protheus.doc} TGC001FT
Retorna o Filtro do Browse de Contratos

<AUTHOR>
@since 30/06/2015
@version P12
*/
//-------------------------------------------------------------------
User Function TGC001FT(cCliente, cLojaCli, nOpc, lFilVig )

	Local cFiltro 	:= ""
	Default nOpc	:= 1
	Default lFilVig	:= .F.

	If nOpc == 1
		cFiltro := "@CN9_FILIAL = '" + xFilial("CN9") + "' "

		If lFilVig
			cFiltro += " AND CN9_SITUAC = '05' "
		EndIf

		cFiltro += " AND CN9_ESPCTR = '2' "
		cFiltro += " AND CN9_TPCTO = '013' "
		cFiltro += " AND EXISTS (SELECT CNC_NUMERO "
		cFiltro += " 			FROM "+RetSqlName("CNC")+" CNC "
		cFiltro += " 			WHERE CNC.CNC_FILIAL = '" + xFilial("CNC") + "' "
		cFiltro += " 			AND CNC.CNC_NUMERO = CN9_NUMERO "
		cFiltro += " 			AND CNC.CNC_REVISA = CN9_REVISA "
		cFiltro += " 			AND CNC.CNC_CLIENT = '"+cCliente+"'"
		cFiltro += " 			AND CNC.CNC_LOJACL = '"+cLojaCli+"'"
		cFiltro += " 			AND CNC.D_E_L_E_T_ = ' '  ) "
	Else
		cFiltro := "@CN9_FILIAL = '" + xFilial("CN9") + "' "

		If lFilVig
			cFiltro += " AND CN9_SITUAC = '05' "
		EndIf

		cFiltro += " AND CN9_ESPCTR = '2' "
		cFiltro += " AND CN9_TPCTO = '013' "
		cFiltro += " AND EXISTS (SELECT CNB_CONTRA "
		cFiltro += " FROM "+RetSqlName("CNB")+" CNB "
		cFiltro += " WHERE CNB_FILIAL = '" + xFilial("CNB") + "' "
		cFiltro += " AND CNB_CONTRA = CN9_NUMERO "
		cFiltro += " AND CNB_REVISA = CN9_REVISA "
		cFiltro += " AND CNB_PROPOS = '"+ADY->ADY_PROPOS+"' "
		cFiltro += " AND CNB_PROREV = '"+ADY->ADY_PREVIS+"' "
		cFiltro += " AND CNB.D_E_L_E_T_ = ' '  ) "
	Endif

Return cFiltro

//-------------------------------------------------------------------
/*/{Protheus.doc} TGC002FT
Retorna o Filtro do Browse de clientes, para exibir somente os clientes
que tem contrato

<AUTHOR> Ferreira
@since 06/10/2015
@version 1.0
@return ${cFil}, ${expressão SQL para o filtro do Browse}
/*/
//-------------------------------------------------------------------
User Function TGC002FT( cCliente )
	Local cFil	:= ""

	cFil := "@A1_FILIAL = '" + xFilial("SA1") + "' "

	If !( Empty( cCliente ) )
		cFil += " AND A1_COD = '"+ cCliente +"' "
	EndIf

	cFil += " AND EXISTS (SELECT DISTINCT CNC_CLIENT "
	cFil += " FROM "+RetSqlName("CNC")+" CNC "
	cFil += " WHERE CNC_FILIAL = '"+xFilial("CNC")+"'"
	cFil += " AND SUBSTR( CNC_NUMERO, 1, 3 ) = 'CON' "
	cFil += " AND CNC_NUMERO > ' '"
	cFil += " AND CNC_REVISA = ( SELECT CN9_REVISA "
	cFil += " 					 FROM "+RetSqlName("CN9")+" CN9 "
	cFil += " 					 WHERE CN9_FILIAL = '"+ FWxFilial("CN9") +"'"
	cFil += " 					 AND CN9_NUMERO = CNC_NUMERO "
	cFil += " 					 AND CN9_REVISA = CNC_REVISA "
	cFil += " 					 AND CN9_ESPCTR = '2'  "

	If !( Empty( cCliente ) )
		cFil += " AND CN9_SITUAC = '05' "
	EndIf

	cFil += " 					 AND CN9.D_E_L_E_T_= ' '"
	cFil += " 					)"
	cFil += " AND CNC_CLIENT = A1_COD "
	cFil += " AND CNC_LOJACL = A1_LOJA "
	cFil += " AND CNC_CLIENT <> ' ' "
	cFil += " AND CNC.D_E_L_E_T_ = ' '  )"

Return cFil

//-------------------------------------------------------------------
/*/{Protheus.doc} TGC003FT
Filtro de proposta, so lista as propostas que tem contrato
<AUTHOR>
@since 14/10/2015
@version 1.0
@return ${cFiltro}, ${expressão sql de filtro}
/*/
//-------------------------------------------------------------------
User Function TGC003FT( cCliente )

	Local cFiltro 	:= ""

	cFiltro := "@ADY_FILIAL = '" + xFilial("ADY") + "' "

	If !( Empty( cCliente ) )
		cFiltro += " AND ADY_CODIGO = '"+ cCliente +"' "
	EndIf

	cFiltro += " AND EXISTS (SELECT DISTINCT CNB_NUMERO "
	cFiltro += " FROM "+RetSqlName("CNB")+" CNB "
	cFiltro += " INNER JOIN "+RetSqlName("CN9")+" CN9 "
	cFiltro += " ON CN9_FILIAL = '" + xFilial("CN9") + "' "
	cFiltro += " AND CN9_NUMERO = CNB_CONTRA "
	cFiltro += " AND CN9_REVISA = CNB_REVISA "
	cFiltro += " AND SUBSTR( CN9_NUMERO, 1, 3 ) = 'CON' "
	cFiltro += " AND CN9_ESPCTR = '2'  "
	cFiltro += " AND CN9.D_E_L_E_T_ = ' ' "
	cFiltro += " WHERE CNB_FILIAL = '"+xFilial("CNB")+"'"
	cFiltro += " AND CNB_PROPOS = ADY_PROPOS "
	cFiltro += " AND CNB_PROREV = ADY_PREVIS "
	cFiltro += " AND CNB.D_E_L_E_T_ = ' '  )"

Return cFiltro


//-------------------------------------------------------------------
/*/{Protheus.doc} TGC001ST
Chama rotina padrão para alterar a situação do contrato
<AUTHOR>
@since 16/09/2015
@version 1.0
/*/
//-------------------------------------------------------------------
User Function TGC001ST(nRecCN9)

	Local cCliente		:= SA1->A1_COD
	Local cLoja			:= SA1->A1_LOJA
	Local nRecSA1		:= SA1->(Recno())
	Local cChaveLock	:= ""
	Local cAtuSituac	:= ""
	Local cNewSituac	:= ""

	CN9->(DbGoTo(nRecCN9))

	cChaveLock	:= "CN9"+xFilial("CN9")+CN9->CN9_NUMERO

	CursorWait()
	If U_GVFUNLOC(1,cChaveLock,"TGCVA001")

		Pergunte("CNT100",.F.)

		//Ajustado para passar os parametros da Rotina do Padrao
		cAtuSituac := CN9->CN9_SITUAC
		CN100Situac("CN9", CN9->(Recno()), 4)
		cNewSituac := CN9->CN9_SITUAC
		If cAtuSituac <> cNewSituac .And. (cNewSituac == DEF_SVIGE .Or. cNewSituac == DEF_SELAB)// Contrato "Vigente" ou "Em Elaboração"
			U_GV001SIT(cNewSituac)
		EndIf
		U_GVFUNLOC(2,cChaveLock,"TGCVA001")
	EndIf


	SA1->(dbGoTo(nRecSA1))

	Carga(cAliasTrb)
	UpdateFldr(oBrowseValidity,cAliasPH7)

	CursorArrow()

Return


//-------------------------------------------------------------------
/*/{Protheus.doc} TGC001AP
Função para aprovação da revisão
<AUTHOR> Ferreira
@since 13/10/2015
@version 1.0
/*/
//-------------------------------------------------------------------
User Function TGC001AP(nRecCN9)
	Local lRet		:= .T.
	Local lShowAprv	:= .T.
	Local cClienPri	:= ""
	Local cLojaPri	:= ""
	Local lZqeOffLine   := Iif( cEmpAnt $ GetMV("TI_ZQEOFFL",,"ND"), .T., .F.)
	CN9->(DbGoTo(nRecCN9))
//	CN9->(DBGOTO(oBrowseValidity:NAT))

	lShowAprv	:= !ExistSCR('RV',CN9->(CN9_NUMERO+CN9_REVISA))


	If lShowAprv

		If Alltrim(CN9->CN9_SITUAC) == DEF_SREVS

			CursorWait()

			CN9->(dbClearfilter())
			CN9->(dbGoTo(nRecCN9))

			U_A3SATpRv(U_Cn3RetSt("TIPREV"))
			U_GV002S0S("")
			U_GV002GAC({})
			U_GV002GP4({})
			U_GV002GP3({})
			Pergunte("CNT100", .F.)
			FWExecView(STR0008,"TGCVA301",MODEL_OPERATION_UPDATE,,{|| .T.})
			U_GV002GAC({})
			U_GV002GP4({})
			U_GV002GP3({})
			U_A3SATpRv("")
			U_A3STpRev("")

			If lZqeOffLine
				lRet := .T.
				U_JOBOFFZEQ(CN9->CN9_NUMERO, CN9->CN9_REVISA)
			Else
				FWMsgRun(,{ || lRet := U_TGCVA018( CN9->CN9_NUMERO, CN9->CN9_REVISA ) },STR0069, STR0059 )//"03-Atualizando Liberacao de Help Desk" ###"Aguarde..."
			EndIf

			If U_GVCliPrin( CN9->CN9_NUMERO, CN9->CN9_REVISA , @cClienPri , @cLojaPri )
				//Verificar Contratos de Setor Publico
				If FindFunction("U_GV050WF")
					FWMsgRun(,{ || U_GV050WF( CN9->CN9_NUMERO, CN9->CN9_REVISA,cClienPri,cLojaPri)  },STR0070, STR0059 )// WF Setor Publico###"Aguarde..."
				EndIf

			EndIf

			CursorArrow()

		Else
			Help('',1,'CNTA300NOREV')
			lRet := .F.
		EndIf
	Else
		Help(" ",1,"EXISTSCR")
		lRet := .F.
	EndIf

	cAprTipRev := ''

Return lRet


//-------------------------------------------------------------------------------------------------------------------
/*/{Protheus.doc} GV001JOB
Chamada da função que apresentará os Jobs do Contrato - PH9   
 
<AUTHOR> Ferreira
@since 17/10/2015
@return Nil
/*/
//-------------------------------------------------------------------------------------------------------------------
User Function GV001JOB(nRecCN9)

//CN9->(DBGOTO(oBrowseValidity:NAT))
	CN9->(DBGOTO(nRecCN9))

	FWExecView(STR0042,"TGCVC001",MODEL_OPERATION_VIEW,,{|| .T.}) //"Jobs do Contrato"

Return Nil

//-------------------------------------------------------------------------------------------------------------------
/*/{Protheus.doc} TGC001CI
Inclusão de contrato
<AUTHOR> Ferreira
@since 23/10/2015
@version 1.0
@return ${return}, ${return_description}
/*/
//-------------------------------------------------------------------------------------------------------------------
User Function TGC001CI()
	Local lRet		:= .T.
	Local nRetOpc 	:= 1
	Local cClienPri	:= ""
	Local cLojaPri	:= ""
	Local lZqeOffLine   := Iif( cEmpAnt $ GetMV("TI_ZQEOFFL",,"ND"), .T., .F.)
	U_GV002SOP(MODEL_OPERATION_INSERT)

	CursorWait()
	U_GV002S0S("")
	U_GV002GH3S("")
	U_GCVA301GL({})
	U_GV002GTL( .F. )
	U_GV002GAC({})
	U_GV002GP4({})
	U_GV002GP3({})
	U_GV002SVA(.T.) // Marca que que a View está ativada

	u_CNTSetFun("TGCVA301")
	nRetOpc := FWExecView(STR0043,"TGCVA301",MODEL_OPERATION_INSERT,,{|| .T.}) //"Inclusão (Vendas)"

	U_A3SATpRv("")
	U_A3STpRev("")
	U_GV002S0S("")
	U_GV002GH3S("")
	U_GCVA301GL({})
	U_GV002SOP(0)
	U_GV002GTL( .F. )
	U_GV002GAC({})
	U_GV002GP4({})
	U_GV002GP3({})
	U_GV002SVC(.F.)
	U_GV002SVA(.F.) // Marca que que a View está desativada

	If ValType(nRetOpc) == 'N' .And. nRetOpc == 0

		If lZqeOffLine
			lRet := .T.
			U_JOBOFFZEQ(CN9->CN9_NUMERO, CN9->CN9_REVISA)
		Else
			FWMsgRun(,{ || lRet := U_TGCVA018( CN9->CN9_NUMERO, CN9->CN9_REVISA ) },STR0069, STR0059 ) //"03-Atualizando Liberacao de Help Desk"###"Aguarde..."
		EndIf

		If U_GVCliPrin( CN9->CN9_NUMERO, CN9->CN9_REVISA , @cClienPri , @cLojaPri )
			//Verificar Contratos de Setor Publico
			If FindFunction("U_GV050WF")
				FWMsgRun(,{ || U_GV050WF( CN9->CN9_NUMERO, CN9->CN9_REVISA,cClienPri,cLojaPri) },STR0070, STR0059 )// WF Setor Publico###"Aguarde..."
			EndIf

		EndIf

	EndIf
	CursorArrow()

Return lRet


//-------------------------------------------------------------------------------------------------------------------
/*/{Protheus.doc} GV001SIT
Efetua o tratamento da PH9 na alteração de situação do contrato    
 
<AUTHOR> Ferreira
@since 24/10/2015
@return Nil
/*/
//-------------------------------------------------------------------------------------------------------------------
User Function GV001SIT(cNewSituac)
	Local oModel 	:= Nil
	Local oModPH9 	:= Nil
	Local lRet 		:= .T.
	Local nPH9		:= 0
	Local oStruCNA	:= FWFormStruct(1,'CNA')
	Local oStruCNB	:= FWFormStruct(1,'CNB')
	Local oStruPH3	:= FWFormStruct(1,'PH3')
	Local oStruPH4	:= FWFormStruct(1,'PH4')
	Local cClienPri	:= ""
	Local cLojaPri	:= ""
	Local lZqeOffLine   := Iif( cEmpAnt $ GetMV("TI_ZQEOFFL",,"ND"), .T., .F.)
	Default cNewSituac	:= ""

//Abre o modelo da consulta de JOBS de contratos
	oModel := FWLoadModel("TGCVC001")

//CNA - Planilhas do Contrato
	oModel:AddGrid('CNADETAIL','CN9MASTER',oStruCNA, /*bLinePre*/,    /* bLinePost*/,  /*bPre*/,  /*bPost*/,/*bLoad*/)
	oModel:SetRelation('CNADETAIL',{{'CNA_FILIAL','xFilial("CNA")'},{'CNA_CONTRA','CN9_NUMERO'},{'CNA_REVISA','CN9_REVISA'}},CNA->(IndexKey(1)))
	oModel:GetModel('CNADETAIL'):SetOptional( .T. )
	oModel:GetModel('CNADETAIL'):SetOnlyQuery(.T.)

//CNB - Itens das Planilhas
	oModel:AddGrid('CNBDETAIL','CNADETAIL',oStruCNB)
	oModel:SetRelation('CNBDETAIL',{{'CNB_FILIAL','xFilial("CNB")'},{'CNB_CONTRA','CN9_NUMERO'},{'CNB_REVISA','CN9_REVISA'},{"CNB_NUMERO","CNA_NUMERO"}},CNB->(IndexKey(1)))
	oModel:GetModel('CNADETAIL'):SetOptional( .T. )
	oModel:GetModel('CNADETAIL'):SetOnlyQuery(.T.)

//PH3 - Cancelamento
	oModel:AddGrid('PH3DETAIL','CNADETAIL',oStruPH3, /*bLinePre*/,    /* bLinePost*/,  /*bPre*/,  /*bPost*/,/*bLoad*/)
	oModel:SetRelation('PH3DETAIL',{{'PH3_FILIAL','xFilial("PH3")'},{'PH3_CONTRA','CN9_NUMERO'},{'PH3_REVISA','CN9_REVISA'},{"PH3_NUMERO","CNA_NUMERO"}  },'PH3_ITSEQ' )
	oModel:GetModel('PH3DETAIL'):SetOptional( .T. )
	oModel:GetModel('PH3DETAIL'):SetOnlyQuery(.T.)

//PH4 - Bonificação e Carencia
	oModel:AddGrid("PH4DETAIL", "CNADETAIL",oStruPH4, /*bLinePre*/,    /* bLinePost*/,  /*bPre*/,  /*bPost*/,/*bLoad*/)
	oModel:SetRelation('PH4DETAIL',{{'PH4_FILIAL','xFilial("PH4")'},{'PH4_CONTRA','CN9_NUMERO'},{'PH4_REVISA','CN9_REVISA'},{"PH4_NUMERO","CNA_NUMERO"}},'PH4_ITSEQ' )
	oModel:GetModel('PH4DETAIL'):SetOptional( .T. )
	oModel:GetModel('PH4DETAIL'):SetOnlyQuery(.T.)

	oModel:SetOperation(MODEL_OPERATION_UPDATE)
	If oModel:Activate()
		If cNewSituac == DEF_SVIGE // Contrato "Vigente"
			U_GV002JOB(oModel, CN9->CN9_NUMERO, CN9->CN9_REVISA ) // Marca o contrato para os Jobs serem executados

			If lZqeOffLine
				lRet := .T.
				U_JOBOFFZEQ(CN9->CN9_NUMERO, CN9->CN9_REVISA)
			Else
				FWMsgRun(,{ || lRet := U_TGCVA018( CN9->CN9_NUMERO, CN9->CN9_REVISA ) },STR0067, STR0066 ) //'02-Atualizando Liberacao de Help Desk'###'Aguarde'
			EndIf

			If U_GVCliPrin( CN9->CN9_NUMERO, CN9->CN9_REVISA , @cClienPri , @cLojaPri )
				//Verificar Contratos de Setor Publico
				If FindFunction("U_GV050WF")
					FWMsgRun(,{ || U_GV050WF( CN9->CN9_NUMERO, CN9->CN9_REVISA,cClienPri,cLojaPri) },STR0070, STR0059 )// WF Setor Publico###"Aguarde..."
				EndIf

			EndIf
//			If !lRet
//				Help(" ",1, 'Help','GV001SIT','Nao Foi Possivel Gerar a Liberacao de Help Desk do Contrato [ '+ AllTrim( CN9->CN9_NUMERO ) +' ]/[ '+ AllTrim( CN9->CN9_REVISA ) +' ]', 3 , 0)
//			EndIf
//
		ElseIf cNewSituac == DEF_SELAB // Contrato "Em Elaboração"
			oModPH9 := oModel:GetModel("PH9DETAIL")
			If !oModPH9:IsEmpty()
				U_CNTA3BlMd(oModPH9,.F.)
				For nPH9 := oModPH9:Length() to  1 Step -1 // Exclui todos as marcações de Jobs
					oModPH9:Goline(nPH9)
					oModPH9:DeleteLine()
				Next nPH9
			EndIf
		EndIf
		If oModel:VldData()
			oModel:CommitData()
		EndIf
	EndIf

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} GV01MtStrt
Estrutura dos campos que devem aparece nos grids da tela do cockpit
<AUTHOR>
@since 17/11/2015
@version 1.0
@return ${array}, ${return_description}
/*/
//-------------------------------------------------------------------
Static Function GV01MtStrt()

	Local aStrutSA1	:= {}
	Local aStrutADY	:= {}
	Local aStrutCN9	:= {}


	aStrutSA1:=	{'A1_COD',;
		'A1_LOJA',;
		'A1_NOME',;
		'A1_PESSOA',;
		'A1_NREDUZ',;
		'A1_EST',;
		'A1_TEL',;
		'A1_CGC',;
		'A1_INSCR'	;
		}

	aStrutADY:=	{;
		'ADY_FILIAL'	, ;
		'ADY_PROPOS'	, ;
		'ADY_PREVIS'	, ;
		'ADY_OPORTU'	, ;
		'ADY_REVISA'	, ;
		'ADY_CODIGO'	, ;
		'ADY_LOJA'		, ;
		'ADY_STATUS'	, ;
		'ADY_DATA'		;
		}

	aStrutCN9:=	{;
		'CN9_FILIAL'	, ;
		'CN9_NUMERO'	, ;
		'CN9_REVISA'	, ;
		'CN9_DTINIC'	, ;
		'CN9_DTASSI'	, ;
		'CN9_UNVIGE'	, ;
		'CN9_VIGE'		, ;
		'CN9_DTFIM'		, ;
		'CN9_MOEDA'		, ;
		'CN9_CONDPG'	, ;
		'CN9_DESCPG'	;
		}

Return {aStrutSA1,aStrutADY,aStrutCN9}
//-------------------------------------------------------------------
/*/{Protheus.doc} GC001VIT
(Verifica se existe itens que possam ser transferidos conforme confi-
guração do grupo de produto).
@type function
<AUTHOR>
@since 24/11/2015
@version 1.0
@param cContraRev, character, (Descrição do parâmetro)
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
//-------------------------------------------------------------------
/*/
Static Function GC001VIT(cContra,cRevisao)
	Local lExItTrn	:= .F.

	lExItTrn	:= U_GC001CIT(cContra,cRevisao,'',lExItTrn)

Return lExItTrn
//-------------------------------------------------------------------
/*/{Protheus.doc} GC001CIT
(Consultas de itens que são licenças passiveis de transferências,
esta definição esta no grupo do produto).
@type function
<AUTHOR>
@since 24/11/2015
@version 1.0
@param cContra, character, (Descrição do parâmetro)
@param cRevisao, character, (Descrição do parâmetro)
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
//-------------------------------------------------------------------
User Function GC001CIT(cContra,cRevisao,cPlanilha,lExItTrn)

	Local cQryCNB	:= ''
	Local cAliasIT	:= GetNextAlias()
	Local cSitTrfB	:= GetMV('TI_TRF0002')//Situação do item que não devem ser considerados para transferir CNB_SITUAC

	cQryCNB	:= " SELECT CNB.CNB_PRODUT "+CRLF
	cQryCNB	+= " FROM "+RetSqlName('CNB')+" CNB "+CRLF
	cQryCNB	+= " WHERE CNB.CNB_CONTRA = '"+cContra+"' "+CRLF
	cQryCNB	+= " AND CNB.CNB_REVISA = '"+cRevisao+"' "+CRLF
	cQryCNB	+= " AND CNB.CNB_SITUAC NOT IN ('"+StrTran(cSitTrfB,',',"','")+"') " +CRLF

	if !Empty(cPlanilha)

		cQryCNB	+= " AND CNB.CNB_NUMERO = '"+cPlanilha+"' "+CRLF

	EndIf

	cQryCNB	+= " AND CNB.D_E_L_E_T_= '' "+CRLF

	cQryCNB	:= ChangeQuery(cQryCNB)
	DbUseArea(.T.,__cRdd,TcGenQry(,,cQryCNB),cAliasIT,.T.,.F.)

	if !(cAliasIT)->(Eof())

		lExItTrn	:= .T.

	Else

		lExItTrn	:= .F.

	EndIf

	if Select(cAliasIT) > 0

		(cAliasIT)->(dbCloseARea())

	EndIf

Return lExItTrn

//-------------------------------------------------------------------
/*/{Protheus.doc} TGC001N9
(posiciona no  primeiro contrato do cliente selecionado
@type function
<AUTHOR>
@since 26/01/2016
@version 1.0
@param cCliAtu, character, (código do cliente)
@param cLojaAtu, character, (loja do cliente)
@return ${Recno}, ${numero do RECNO do contrato posicionado}
@example
(examples)
@see (links_or_references)
/*/
//-------------------------------------------------------------------
User Function TGC001N9(cCliAtu, cLojaAtu)

	Local aAreaCN9		:= CN9->(GetArea())
	Local aAreaCNC		:= {}

	Local cCTRAtu		:= CN9->CN9_NUMERO+CN9->CN9_REVISA

	Local nRecCN9		:= CN9->(Recno())

	//Ao selecionar um cliente e não clicar em um contrato a CN9 pode nao estar posicionada, MESMO se um contrato estiver visivelmente selecionado,
	//além de não carregar algumas variáveis, posteriormente, aparece uma msg de validação CNTA240-"...usuário sem  autorização..." para isto
	//se não estiver posicionada e  o campo CN9_NUMERO estiver vazio posiciono no primeiro contrato do cliente
	if Empty(cCTRAtu)

		dbSelectArea("CN9")
		CN9->(dbSetOrder(1))
		if CN9->(dbSeek(FwxFilial('CN9')+'CON'+cCliAtu))

			nRecCN9		:= CN9->(Recno())

		Else
			//Caso  o cliente não seja o principal, procura o contrato na tabela CNC Amarração Cliente x Contrato
			aAreaCNC	:=  CNC->(GetArea())

			dbSelectARea("CNC")
			CNC->(dbSetOrder(4))
			if CNC->(dbSeek(FWxFilial('CNC')+cCliAtu+cLojaAtu))

				cCTRAtu	:= CNC->CNC_NUMERO+CNC->CNC_REVISA

				dbSelectARea("CN9")
				CN9->(dbSetOrder(1))
				if CN9->(dbSeek(FWxFilial("CN9")+cCTRAtu))

					nRecCN9		:= CN9->(Recno())

				EndIf

			EndIf

			RestArea(aAreaCNC)

		Endif

	EndIf

	oBrowseValidity:GoTo(nRecCN9, .T.)

	RestArea(aAreaCN9)

Return nRecCN9
//-------------------------------------------------------------------
/*/{Protheus.doc} TGC001RE
(Chama a tela de reativação se as validações retornarem verdadeiras
@type User function
<AUTHOR>
@since 28/01/2016
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
//-------------------------------------------------------------------
User Function TGC001RE(nRecCN9)

	Local cCliente	:= SA1->A1_COD
	Local cLojaCli	:= SA1->A1_LOJA
	Local cCtrPosi	:= ''
	Local cRevPosi	:= ''

	Local aAreaCN9	:= CN9->(GetArea())

	Local lExist	:= .F.

//		CN9->(DBGOTO(oBrowseValidity:NAT))
	CN9->(DbGoTo(nRecCN9))

	cCtrPosi	:= CN9->CN9_NUMERO //contrato posicionado
	cRevPosi	:= CN9->CN9_REVISA //revisão posicionada

	If CN9->CN9_SITUAC == '05'
		//Verifica se o contrato posicionado é da revisão atual
		if !Empty(cCtrPosi) .AND. Empty(CN9->CN9_REVATU)
			//chama Tela de reativação
			U_GV019MAN()

		Else

			Help(" ",1, 'Help','TGC001RE_02', STR0052+" ("+cCtrPosi+"). "+STR0053+" ("+cRevPosi+") "+STR0054, 3, 0 )
			//"Selecione a última revisão do contrato"##" A revisão selecionada"##"não corresponde a revisão atual."

		EndIf

	Else

		Help(" ",1, 'Help','TGC001RE_01',STR0068, 3 , 0)//"Somente é possível reativar itens em Contratos Vigentes. //"Somente é possível reativar itens em Contratos Vigentes."

	EndIf
	RestArea(aAreaCN9)

Return


//-------------------------------------------------------------------
/*/{Protheus.doc} TGC001SF
Botão para consultar os Sites de faturamento do Cliente

<AUTHOR> Ferreira
@since 25/04/2016
@version 1.0
/*/
//-------------------------------------------------------------------
User Function TGC001SF()

	Local aArea			:= GetArea()
	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())
	Local nRecCN9		:= 0
	Local cSql			:= ""
	Local clAlias		:= GetNextAlias()
	Local oDlg			:= Nil
	Local aHeader		:= {}
	Local aCols			:= {}
	Local lPosicio		:= .F.
	Local nPosCnt 		:= 0
	Local nPosRev 		:= 0

	cSql := U_GCV01QSF(SA1->A1_COD , SA1->A1_LOJA)

	FWMsgRun(,{|| DbUseArea(.T.,__cRdd,TcGenQry(,,cSql),clAlias,.T.,.F.)},STR0058, STR0059) //"Consultando Contratos"###"Aguarde..."

	(clAlias)->(dbGoTop())

	If (clAlias)->(! Eof())

		GV001HASF(clAlias,@aHeader,@aCols)

		DEFINE MSDIALOG oDlg TITLE STR0060 FROM 009,000 TO 035,120 OF oMainWnd //"Site(s) de Faturamento"

		oGet := MsNewGetDados():New(005,005,100,232,0,,,,,,,,,,oDlg,aHeader,aCols)
		oGet:obrowse:align:= CONTROL_ALIGN_ALLCLIENT
		oGet:nAt := oGet:OBROWSE:NAT := 1 //nPos

		oGet:oBrowse:blDblClick := {|| If(len(aCols)>0,lPosicio:=.T.,lPosicio:=.F.),oDlg:End()}

		EnchoiceBar(oDlg, {|| If(len(aCols)>0,lPosicio:=.T.,lPosicio:=.F.), oDlg:End() },{|| oDlg:End() },,{} )

		oDlg:aControls[Len(oDlg:aControls)]:cCaption := STR0060 //"Site(s) de Faturamento"
		oDlg:aControls[Len(oDlg:aControls)]:cTitle 	:= STR0060 //"Site(s) de Faturamento"

		ACTIVATE MSDIALOG oDlg CENTERED

		If lPosicio

			nPosCnt := aScan(aHeader,{|x| AllTrim(x[2]) == "CNB_CONTRA"})
			nPosRev := aScan(aHeader,{|x| AllTrim(x[2]) == "CNB_REVISA"})

			CN9->(dbSetOrder(8))
			If CN9->(dbSeek(FWxFilial("CN9") + aCols[oGet:nAt,nPosCnt]  ))
				nRecCN9 := CN9->(Recno())
			EndIf

		EndIf

	Else
		Help(" ",1, 'Help','TGC001SF', STR0061, 3, 0 )  //"Não foi localizado Site de Faturamento para os contratos deste cliente."
	EndIf

	(clAlias)->(dbCloseArea())

	Pergunte("CNT100", .F.)

	RestArea(aAreaSA1)
	RestArea(aAreaCN9)
	RestArea(aArea)

	If nRecCN9 > 0
		_nRecnoCN9 := nRecCN9
	EndIf

Return NIL

//-------------------------------------------------------------------
/*/{Protheus.doc} GCV01QSF
Query para listar os Sites de Faturamento dos contratos do cliente
posicionado

<AUTHOR> Ferreira
@since 25/04/2016
@version 1.0
@param cCliente, character, codigo do Cliente
@param cLoja, character, Lojja do Cliente
@return cSql, expressão Sql
/*/
//-------------------------------------------------------------------
User Function GCV01QSF(cCliente, cLoja)

	Local cSql := ""

	cSql := " SELECT DISTINCT "
	cSql += "  CNB_CONTRA "
	cSql += " , CNB_REVISA "
	cSql += " , CNB_GRUPO "
	cSql += " , CNB_UNINEG "
	cSql += " FROM " + RetSqlName("CNC") + " CNC "

	cSql += " INNER JOIN  " + RetSqlName("CN9") + " CN9 "
	cSql += " ON CN9.CN9_NUMERO = CNC.CNC_NUMERO "
	cSql += " AND CN9.CN9_REVATU = ' ' "
	cSql += " AND CN9.CN9_ESPCTR = '2' "
	cSql += " AND CN9.CN9_REVISA = ( SELECT MAX(CN9_REVISA) "
	cSql += " 						 FROM " + RetSqlName("CN9")+ " A "
	cSql += " 						 WHERE A.CN9_FILIAL = CN9.CN9_FILIAL "
	cSql += " 						 AND A.CN9_NUMERO = CN9.CN9_NUMERO "
	cSql += " 						 AND A.CN9_REVATU = ' ' "
	cSql += " 						 AND A.CN9_ESPCTR = '2' "
	cSql += " 						 AND A.D_E_L_E_T_ = ' ' "
	cSql += " 						 ) "
	cSql += " AND CN9.D_E_L_E_T_ = ' ' "

	cSql += " INNER JOIN  " + RetSqlName("CNB") + " CNB "
	cSql += " ON CNB.CNB_FILIAL = '"+ FWxFilial("CNB") +"'"
	cSql += " AND CNB.CNB_CONTRA = CN9.CN9_NUMERO "
	cSql += " AND CNB.CNB_REVISA = CN9.CN9_REVISA "
	cSql += " AND CNB.D_E_L_E_T_= ' '"

	cSql += " WHERE CNC.CNC_FILIAL = '"+ FWxFilial("CNC") +"'"
	cSql += " AND CNC.CNC_CLIENT = '"+ cCliente +"'"
	cSql += " AND CNC.CNC_LOJACL = '"+ cLoja +"'"
	cSql += " AND CNC.CNC_REVISA = CN9.CN9_REVISA "
	cSql += " AND CNC.D_E_L_E_T_= ' '"

	cSql += " ORDER BY CNB_GRUPO , CNB_UNINEG , CNB_CONTRA , CNB_REVISA"

Return cSql

//-------------------------------------------------------------------
/*/{Protheus.doc} GV001HASF
Carrega o aHeader e aCols da tela de consulta dos Sites de Faturamento

<AUTHOR> Ferreira
@since 25/04/2016
@version 1.0
@param clAlias, character, Result da Query
@param aHeader, array, aHeader da tela de consulta
@param aCols, array, aCols da tela de Consulta
/*/
//-------------------------------------------------------------------
Static Function GV001HASF(clAlias,aHeader,aCols)

	Local nY		:= 0
	Local alFields	:= {}
	Local cField    := ""

	aadd(alFields, "CNB_CONTRA" )
	aadd(alFields, "CNB_REVISA" )
	aadd(alFields, "CNB_GRUPO" 	)
	aadd(alFields, "CNB_GRPDES" )
	aadd(alFields, "CNB_UNINEG" )
	aadd(alFields, "CNB_UNIDES" )

	For nY := 1 To Len( alFields )
		cField  := alFields[ nY ]
		Aadd( aHeader,{ TRIM(FWX3Titulo(cField) ), FwGetSx3Cache(cField,"X3_CAMPO"), FwGetSx3Cache(cField,"X3_PICTURE"), FwGetSx3Cache(cField,"X3_TAMANHO"), FwGetSx3Cache(cField,"X3_DECIMAL"), FwGetSx3Cache(cField,"X3_VALID"), FwGetSx3Cache(cField,"X3_USADO"), FwGetSx3Cache(cField,"X3_TIPO"), , FwGetSx3Cache(cField,"X3_CONTEXT")})
	Next nY

	While (clAlias)->( !Eof() )

		Aadd( aCols, {} )


		Aadd( aTail( aCols ), ( clAlias )->CNB_CONTRA )
		Aadd( aTail( aCols ), ( clAlias )->CNB_REVISA )
		Aadd( aTail( aCols ), ( clAlias )->CNB_GRUPO )
		Aadd( aTail( aCols ), Posicione("SM0",1,( clAlias )->CNB_GRUPO,"M0_NOME") )
		Aadd( aTail( aCols ), ( clAlias )->CNB_UNINEG )
		Aadd( aTail( aCols ), Posicione("SM0",1,( clAlias )->(CNB_GRUPO+ CNB_UNINEG) ,"M0_FILIAL") )

		Aadd( aTail( aCols ), .F.)

		( clAlias )->( dbSkip() )
	EndDo

Return

//-------------------------------------------------------------------
/*/{Protheus.doc} BRWPOSCN9
Função para posicionar no contrato, conforme posiciona no browse
de cliente

<AUTHOR>
@since 25/04/2016
@version 1.0
/*/
//-------------------------------------------------------------------
User Function BRWPOSCN9( lFilVig )
	Default lFilVig := .F.

	oBrowseValidity:SetFilterDefault( U_TGC001FT( SA1->A1_COD, SA1->A1_LOJA, 1, lFilVig ) )

	If _nRecnoCN9 > 0
		CN9->(DBGOTO(_nRecnoCN9))
		oBrowseValidity:GoTo(_nRecnoCN9, .T.)
	Else
		CN9->(DBGOTO(oBrowseValidity:NAT))
	EndIf

	_nRecnoCN9 := 0

Return

//-------------------------------------------------------------
/*/{Protheus.doc} GV01CmpMed
Verifica se tem competencias passadas até a atual, sem Medição

<AUTHOR>
@since 29/06/2016
@version 1.0
@param cContra, character, (Descrição do parâmetro)
@return ${return}, ${return_description}
/*/
//-------------------------------------------------------------
User Function GV01CmpMed(cContra)
	Local aAreaCN9	  := CN9->(GetArea())
	Local cRevisa	  := ""
	Local lNewGeraPed := SuperGetMv("TI_NEWPEDI",,.F.)
	Local nTamCN9NUM  := FwGetSx3Cache("CN9_NUMERO", "X3_TAMANHO" )

	Default cContra := ""

	cContra := Padr(cContra, nTamCN9NUM)

	Begin Sequence

		If IsBlind()
			Break
		EndIf

		If Empty(cContra)
			Break
		EndIf

		CN9->(dbSetOrder(7))
		If !CN9->(dbSeek(FWxFilial("CN9") + cContra + "05")) // Vigente
			Break
		EndIf

		cRevisa := CN9->CN9_REVISA

		If !U_GCVA142C(cContra,cRevisa) //Verfica se tem pedido em aberto
			Break
		EndIf

		If lNewGeraPed
			If MsgYesNo( STR0083 ) //"Existe competência(s) sem pedidos, deseja gerar agora?"
				U_GCVA143V()
			EndIf
		Else
			If MsgYesNo( STR0071 ) //"Existe competência(s) sem medições, deseja gerar agora?"
				U_TGCVA055(Padr(cContra,TamSx3("CN9_NUMERO")[1]), .F.)
			EndIf
		EndIf

	End Sequence

	CN9->(RestArea(aAreaCN9))

Return


//------------------------------------------------------------
/*/{Protheus.doc} GV1BRWCL
//TODO Chama as funções dos botões do Browse de Clientes 

<AUTHOR>
@since 06/08/2016

@param nlOpc, numeric, Opção de processamento

/*/
//------------------------------------------------------------
User Function GV1BRWCL(nTipo)

	Do Case
	Case nTipo == 1
		U_TGC001VC() //"Vis. Cliente"
	Case nTipo == 2
		U_TGC001PC() //"Pos. Cliente"
	Case nTipo == 3
		U_TGC001TB() //"Tit. Baixados"
	Case nTipo == 4
		U_TGC001CH() //"Chamados"
	Case nTipo == 5
		U_TGC001SF() //"Site(s) Faturamento"
	Case nTipo == 6
		U_TGCVJ000() //"Processamentos Lote"
	End Case

Return

//-----------------------------------------------------------------------------
/*/{Protheus.doc} GV01CLFBT
//TODO Chama as funções dos botões do Browse de Manutenção do contrato, 
trata os filtros do Browse, para não impactos nas tabelas.

<AUTHOR>
@since 06/08/2016

@param nOpcBrow, numeric, Qual o Browse que estão  
@param nlOpc, numeric, Opção do Botão

/*/
//-----------------------------------------------------------------------------
User Function GV01CLFBT(nOpcBrow , nlOpc )

	Local nRecSA1 := SA1->(Recno())
	Local nRecCN9 := CN9->(Recno())

	If nOpcBrow == 1 // Clientes
		U_GV1BRWCL(nlOpc)
	ElseIf nOpcBrow == 2 // Manutenção de Contrato
		U_GV01ASEL(nlOpc,(cAliasTrb)->CN9_RECNO)
	EndIf

	SA1->(dbGoTo(nRecSA1))
	CN9->(dbGoTo(nRecCN9))

	Carga(cAliasTrb)

Return

//----------------------------------------------------------------
Static Function RetX3Opc(cCampo,uConteudo)

	Local cRetorno  := ""
	Local aArea	    := SX3->(GetArea())
	Local aOpcoes	:= RetSX3Box( Posicione( "SX3", 2, cCampo, "X3CBox()" ),,, TamSx3(cCampo)[1] )
	Local nPos 		:= 0



	If (nPos:= Ascan(aOpcoes,{|x|Alltrim(x[2])==Alltrim(uConteudo)})) > 0

		cRetorno := aOpcoes[nPos][3]

	Endif

	RestArea(aArea)

Return cRetorno

//-----------------------------------------------------------------------------
/*/{Protheus.doc} ClearAll

<AUTHOR> Bueno
@since 06/08/2016


/*/
//-----------------------------------------------------------------------------

Static Function ClearAll(oBrowseProposals, oBrowseValidity)

	If Select(cAliasTrb) > 0

		(cAliasTrb)->(__dbZap())

	Endif

	If Select(cAliasPH7) > 0

		(cAliasPH7)->(__dbZap())

	Endif

	oBrowseProposals:Refresh()
	oBrowseValidity:Refresh()
Return Nil

//-----------------------------------------------------------------------------
/*/{Protheus.doc} TGCORPCA(lAltera)

<AUTHOR> dos Reis Fragazi
@since 30/11/2016

/*/
//-----------------------------------------------------------------------------

User Function TGCORPCA(nRecCN9, lAltera)

	Local aArea			:= GetArea()
	Local aAreaSA1 		:= SA1->(GetArea())
	Local aAreaCN9 		:= CN9->(GetArea())
	Local aAreaCNC 		:= CNC->(GetArea())

	Local nRecSA1		:= 0
	Local cRecNew		:= 0
	Local lRet			:= .T.
	Local cContrato 	:= ""
	Local cRevisa		:= ""
	Local cCliente		:= SA1->A1_COD
	Local cLoja			:= SA1->A1_LOJA
	Local cChaveLock	:= ""

	CursorWait()

	CN9->(DBGOTO(nRecCN9))

	cChaveLock	:= "CN9"+xFilial("CN9")+CN9->CN9_NUMERO


	cRecNew 	:= nRecCN9
	nRecSA1		:= SA1->(Recno())

	//Limpa o Filtro Aplicado
	CN9->(dbGoTo(nRecCN9))
	SA1->(dbGoTo(nRecSA1))

	If CN9->(Eof())

		Aviso("TGC001CA", STR0028, {STR0026})		//"Selecione um Contrato para Alteração."	//"Fechar"

	Else

		If lAltera

			If U_GVFUNLOC(1,cChaveLock,"TGCVA001")

				U_TGCVA007(CN9->CN9_NUMERO,CN9->CN9_REVISA,2,SA1->A1_COD,SA1->A1_LOJA)

				U_GVFUNLOC(2,cChaveLock,"TGCVA001")

			Else
				MsgStop("Não possível realizar lock do contrato!")
				lRet := .F.
			EndIf
		Else
			U_TGCVA007(CN9->CN9_NUMERO,CN9->CN9_REVISA,1,SA1->A1_COD,SA1->A1_LOJA)
		EndIf

	EndIf

	If lRet
		SA1->(dbGoTo(nRecSA1))

		RestArea(aAreaCNC)
		RestArea(aAreaCN9)
		RestArea(aAreaSA1)
		RestArea(aArea)

		CN9->(dbGoTo(cRecNew))

	EndIf

	CursorArrow()

Return lRet


//-----------------------------------------------------------------------------
/*/{Protheus.doc} GV1SLcMst
Static para controle de Licença misterchef cancelada - Bematech

<AUTHOR>
@since 05/11/2017
/*/
//-----------------------------------------------------------------------------
User Function GV1SLcMst( cValue )

	aadd( aLicMistD , cValue )

Return

//-----------------------------------------------------------------------------
/*/{Protheus.doc} GV1CLcMst
Limpa a Static para controle de Licença misterchef cancelada - Bematech

<AUTHOR>
@since 05/11/2017
/*/
//-----------------------------------------------------------------------------
User Function GV1CLcMst( )
	aLicMistD := {}
Return


//-----------------------------------------------------------------------------
/*/{Protheus.doc} GVDstMist
Verifica se precisa chamar a tela para desativação da licença Misterchef - 
Bematech

<AUTHOR>
@since 05/11/2017
/*/
//-----------------------------------------------------------------------------
Static Function GVDstMist()

	If Len(aLicMistD) > 0

		If Aviso("GVDSTMIST","Foi cancelado um item de recorrência Misterchef, deseja desativar a licença agora?", {"Sim","Não"} , 3) == 1

			U_G73AtDsM( CN9->(Recno()) )

		EndIf

	EndIf

Return


Static Function creatTmpTab(aFields, cAlias, aIndice)
	Local oTempTable
	Local nx := 1
	Local cNameIdx := "01"

//Criação do objeto
	oTempTable := FWTemporaryTable():New( cAlias )

//Atribui os campos
	oTemptable:SetFields( aFields )

//Adiciona o indice da tabela

	For nx:=1 to Len(aIndice)
		oTempTable:AddIndex(cNameIdx , aIndice[nx])
		cNameIdx := SOMA1(cNameIdx)
	Next

//Criação da tabela
	oTempTable:Create()

return oTempTable


User Function GCVA001U()

	Carga(cAliasTrb)
Return


User Function GCVA001R()

	Carga(cAliasTrb)
	oBrowseValidity:GoTop(.T.)

	UpdateFldr(oBrowseValidity,cAliasPH7)
	oBrowseProposals:GoTop(.T.)
Return

	Static __nSecIni := 0

User Function LogContrato(nModo, cAcao, cContrato)
	Local nSec     := Seconds()
	Local nSecAnt  := 0
	Local nDifSeg  := 0
	Local cRevisa  := ""
	Local cLog     := ""
	Local aArea    := GetArea()
	Local aAreaCN9 := CN9->(GetArea("CN9"))

	If nModo == 1
		__nSecIni := nSec
	ElseIf nModo == 2 .and. __nSecIni > 0
		nSecAnt := __nSecIni
		If nSec < nSecAnt  // caso seja maior, significa que passaou da meia noite onde o seconds recomeça com 0
			nDifSeg := 86399 - nSecAnt
			nDifSeg += nSec
		Else
			nDifSeg := nSec - nSecAnt
		EndIf


		CN9->(DbOrderNickName("CN9P03")) //CN9_FILIAL+CN9_SITUAC+CN9_ESPCTR+CN9_TPCTO+CN9_NUMERO
		CN9->(dbSeek(FwxFilial("CN9") + "05" + "2" + "013" + cContrato ))
		cRevisa := CN9->CN9_REVISA
		If cAcao == "GRAVAR"
			cLog    := RetJustif(cContrato, cRevisa)
		EndIf
		GravaLog(cContrato, cRevisa, cAcao,  Sec2Time(__nSecIni), Sec2Time(nSec),  Sec2Time(nDifSeg),  nDifSeg, cLog)

		__nSecIni := 0

	EndIf

	RestArea(aAreaCN9)
	RestArea(aArea)
Return

Static Function Sec2Time(nSec)
	Local nHour := 0
	Local nMin  := 0

	nSec := If(nSec == NIL, 0, nSec)

	nMin  := Int(nSec/60)
	nHour := Int(nMin/60)

	If ( nMin > 0 )
		nSec -= nMin*60
	EndIf

	If ( nHour > 0 )
		nMin -= nHour*60
	EndIf

Return StrZero(nHour,2,0) + ":" + StrZero(nMin,2,0) + ":" + StrZero(nSec,2,0)

Static Function GravaLog(cContrato, cRevisa, cAcao,  cInicio, cTermino, cTempo,  nDifSeg, cLog)
	Local cCabec   := "CONTRATO;REVISAO;CLIENTE; DATA;EVENTO;HORA INICIO; HORA FIM; TEMPO;MIL. SEG;LOG"
	Local cLinha   := ""
	Local cData    := dtoc(date())

	Local cDir := "system\tijob\"
	Local cArquivo := "logcontrato.csv"
	Local cCliente := Subs(cContrato, 4, 6)
	Local cNomCli  := ""

	If ! ExistDir(cDir)
		MakeDir(cDir)
	Endif

	cDir := "system\tijob\LOGCONTRATO\"

	If ! ExistDir(cDir)
		MakeDir(cDir)
	Endif


	If ! File(cDir + cArquivo)
		GrvArq(cDir + cArquivo, cCabec)
	EndIf

	cNomCli := Alltrim(ReadValue("SA1", 1, xFilial("SA1") + cCliente, "A1_NOME"))

	cLinha := cContrato + ";" + cRevisa + ";" + cNomCli + ";" + cData + ";" + cAcao + ";" + cInicio + ";"+ cTermino + ";"+ cTempo + ";"+ AllTrim(Str(nDifSeg)) + ";" + cLog
	GrvArq(cDir + cArquivo, cLinha)

Return

Static Function GrvArq(cArquivo, cLinha, lEnter)
	Local nHandle2 := 0
	Default lEnter := .t.
	If ! File(cArquivo)
		If (nHandle2 := MSFCreate(cArquivo,0)) == -1
			Return
		EndIf
	Else
		If (nHandle2 := FOpen(cArquivo,2)) == -1
			Return
		EndIf
	EndIf
	FSeek(nHandle2,0,2)
	If lEnter
		FWrite(nHandle2, cLinha + CRLF)
	Else
		FWrite(nHandle2, cLinha )
	EndIf
	FClose(nHandle2)
Return

Static Function RetJustif(cContrato, cRevisa)
	Local cDescJus := ""
	Local nx:= 0

	cDescJus := MSMM( CN9->CN9_CODJUS )
	cDescJus := StrTran(cDescJus, CHR(10)," ")
	cDescJus := StrTran(cDescJus, CHR(13)," ")
	cDescJus := StrTran(cDescJus, CHR(13)+CHR(10)," ")
	cDescJus := strtran(cDescJus, chr(9), " ")

	For nx := 20 to 2 Step -1
		cDescJus := StrTran(cDescJus, space(nx), " ")
	Next

	cDescJus := Alltrim(Left(cDescJus, 250))

Return cDescJus


